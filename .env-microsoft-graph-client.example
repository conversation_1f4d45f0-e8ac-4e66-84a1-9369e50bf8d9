# Microsoft Graph Client Configuration
# Copy this file to .env-microsoft-graph-client and fill in your values

# Your Azure AD Tenant ID (GUID)
MS_GRAPH_TENANT_ID=your-tenant-id-here

# Your App Registration Client ID (GUID)
MS_GRAPH_CLIENT_ID=your-client-id-here

# Your App Registration Client Secret
MS_GRAPH_CLIENT_SECRET=your-client-secret-here

# Required App Registration Permissions:
# - User.ReadWrite.All (to read and delete users)
# - Directory.ReadWrite.All (to read directory information)
#
# To set up the App Registration:
# 1. Go to Azure Portal > Azure Active Directory > App registrations
# 2. Create a new registration or use existing one
# 3. Go to API permissions > Add a permission > Microsoft Graph > Application permissions
# 4. Add User.ReadWrite.All and Directory.ReadWrite.All
# 5. Grant admin consent for your organization
# 6. Go to Certificates & secrets > New client secret
# 7. Copy the client secret value to MS_GRAPH_CLIENT_SECRET above
