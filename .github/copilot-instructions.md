# CrateNFCAPI Project Instructions

## Project Overview

CrateNFCAPI is a music sharing system backend that allows users to manage and share music tracks through NFC cards. The system consists of two main components:

1. **CrateApi**: Main API service for mobile clients handling user authentication, track management, collections, and NFC cards
2. **UnfurlApi**: Specialized service for scraping music service metadata from various platforms

## Architecture and Project Structure

- **CrateApi.Common**: Shared models, DTOs, and utilities
- **CrateApi.Data**: Entity Framework models and database context
- **CrateApi.Services**: Business logic and service implementations
- **CrateApi**: Main API controllers and endpoint definitions
- **UnfurlApi.Services**: Music service scrapers and platform-specific clients
- **UnfurlApi**: API for music metadata extraction

## Code Conventions

### General Coding Style

- Use C# primary constructors for dependency injection
- DO NOT prefix private fields with underscores (`_`)
- Use 4 spaces for indentation
- Use explicit types rather than `var` for clarity
- Use `record` types for DTOs and immutable data structures
- Prefer async/await over direct Task handling
- Use `CancellationToken` parameters in async methods and pass them through the call chain
- Use `ConfigureAwait(false)` only when necessary (library code), not in ASP.NET Core applications
- Prefer `IEnumerable<T>` for method return types when the caller only needs to iterate once
- Use `IReadOnlyList<T>` or `IReadOnlyCollection<T>` when the caller needs multiple iterations
- Use `List<T>` only when the collection needs to be modified
- Prefer expression-bodied members for simple methods and properties
- Use `nameof()` instead of hardcoded strings for property names
- Use `StringComparison` overloads when comparing strings
- Prefer string interpolation over string concatenation
- Use `DateTimeOffset` instead of `DateTime` for timestamps that need to preserve timezone information

### Functional Programming with LanguageExt

- Use LanguageExt for functional programming patterns
- Prefer `Option<T>` over null values
- Use `Either<L, R>` for operations that can return different result types
- Use `TryAsync<T>` for operations that can fail
- Use monadic composition with LINQ query syntax (from/select)
- Follow the pattern of runtime environments with type class instances

### API Design

- Use attribute routing with consistent patterns (`api/v1/[controller]`)
- Use `[RequiresApiKey]` attribute for endpoints requiring API key authentication
- Use `[Authorize]` attribute for endpoints requiring JWT authentication
- Health check endpoints should NOT require API key authentication
- Use `[ProducesResponseType]` attributes to document response types
- Use intuitive field naming in API responses (e.g., 'uptime' instead of 'totalDuration')
- Provide concise, helpful Swagger descriptions for endpoints
- Use consistent casing conventions (camelCase for JSON properties)
- Use HTTP status codes appropriately (200 for success, 201 for creation, 204 for no content, etc.)
- Use route parameters for resource identifiers and query parameters for filtering/pagination
- Implement proper pagination for collection endpoints with `start` and `size` parameters
- Return problem details (RFC 7807) for error responses
- Use action method parameter binding attributes (`[FromBody]`, `[FromRoute]`, `[FromQuery]`) explicitly
- Validate request models using data annotations and return 400 Bad Request for invalid inputs
- Use minimal API endpoints only when appropriate for very simple operations

### Error Handling

- Use the `ExceptionResult` handler pattern for consistent error responses
- Return appropriate HTTP status codes for different error types
- Use custom exceptions like `ScraperException`, `BadRequestException`, etc.
- Log errors with appropriate severity levels
- Use the `TryAsync` pattern for operations that can fail
- Include correlation IDs in logs and error responses for request tracing
- Use structured logging with semantic logging properties
- Avoid catching generic exceptions (`Exception`) except at application boundaries
- Prefer specific exception types that convey intent
- Use middleware for global exception handling
- Log stack traces only for unexpected exceptions
- Don't expose sensitive information in error responses
- Use `ProblemDetails` for standardized error responses in APIs

### Authentication and Security

- Use JWT tokens for user authentication
- Use API keys for service-to-service authentication
- Health check endpoints should be excluded from authentication requirements
- Use attributes like `ApiKeyRequirementAttribute` for controlling API key authentication
- Prefer an array of excluded paths like `_excludedPaths = new[] { "/api/v1/healthcheck" }` for authentication exclusions

### Health Checks

- Consolidate health check services in a single `AddHealthChecks()` call with chained methods
- Include Git commit information in health check endpoints for better version tracking
- Include uptime information in health check responses
- Use simple health check implementations without overly detailed descriptions

### Testing

- Write unit tests for service logic
- Use mocks for external dependencies
- Follow the Arrange-Act-Assert pattern
- Test both success and failure paths
- Use xUnit for test projects
- Use descriptive test names that explain the scenario and expected outcome
- Keep tests independent and avoid shared state
- Use test fixtures for common setup code
- Use theory tests with inline data for testing multiple scenarios
- Mock only the immediate dependencies of the system under test
- Test the behavior, not the implementation details
- Use integration tests for testing the full request pipeline
- Use in-memory database for data access tests when appropriate
- Avoid testing third-party code

## Package Management

- Target .NET 8.0 for all projects
- Use .NET 9.0.0 packages for EntityFrameworkCore, Extensions.DependencyInjection, Extensions.Logging, and System.Text.Json
- Do NOT upgrade Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore to version 9.0.0 as it's incompatible with the project
- Do NOT upgrade the CrateApi project to .NET 9
- Use central package management when possible
- Pin package versions to specific versions rather than using floating versions
- Regularly update packages for security fixes
- Prefer official Microsoft packages over third-party alternatives when available
- Use analyzers like Microsoft.CodeAnalysis.NetAnalyzers to enforce coding standards
- Use SourceLink for debugging into NuGet packages
- Minimize transitive dependencies

## Database

- Use Entity Framework Core with PostgreSQL in production
- Use SQLite for local development
- Run migrations using the EF Core CLI tools
- Use code-first approach for database schema management
- Define entity configurations in separate configuration classes
- Use strongly-typed IDs for entities when appropriate
- Implement soft delete patterns for entities that shouldn't be permanently deleted
- Use appropriate indexing strategies for frequently queried columns
- Use database transactions for operations that modify multiple entities
- Implement proper concurrency control using row version or concurrency tokens
- Use eager loading with `Include()` only when necessary
- Use projection queries with `Select()` to retrieve only needed properties
- Avoid lazy loading in web applications
- Use database-generated values for creation and modification timestamps

## Docker and Deployment

- Use Docker Compose for local development
- Use GitHub Actions for CI/CD
- Include Git commit information in GitHub Actions workflows
- Use Azure App Service for hosting
- Use multi-stage Docker builds to minimize image size
- Use environment variables for configuration in Docker containers
- Implement health checks for container orchestration
- Use container registry for storing and versioning Docker images
- Implement proper logging in containerized applications
- Use Docker volumes for persistent data
- Implement proper container security practices
- Use infrastructure as code for deployment automation
- Implement blue-green deployment for zero-downtime updates
- Use semantic versioning for application releases

## Supported Music Services

- Spotify
- Apple Music
- YouTube Music
- Generic web pages (fallback for unsupported services)

## Performance Best Practices

- Use asynchronous programming for I/O-bound operations
- Implement caching for frequently accessed data
- Use response compression for API responses
- Minimize database round trips
- Use pagination for large data sets
- Implement proper connection pooling
- Use memory caching for frequently accessed, rarely changing data
- Optimize LINQ queries to minimize database load
- Use appropriate serialization settings for JSON responses
- Implement proper resource disposal with `using` statements or `IDisposable` pattern
- Use background services for long-running operations
- Implement proper timeout handling for external service calls
- Use distributed caching in multi-instance environments
- Monitor application performance with appropriate metrics

## Security Best Practices

- Implement proper input validation for all user inputs
- Use parameterized queries to prevent SQL injection
- Implement proper authentication and authorization
- Use HTTPS for all communications
- Implement proper CORS policies
- Use secure cookies with appropriate flags
- Implement rate limiting for API endpoints
- Use security headers (HSTS, CSP, etc.)
- Implement proper secrets management
- Regularly update dependencies for security patches
- Implement proper logging for security events
- Use anti-forgery tokens for forms
- Implement proper password hashing
- Follow the principle of least privilege
