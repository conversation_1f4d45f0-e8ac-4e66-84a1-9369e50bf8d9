name: Lint, Test and Build Docker Images

on:
  pull_request:
  push:
    branches:
      - master
    tags:
      - '*'
  workflow_dispatch:
    inputs:
      tag:
        description: 'Optional custom tag for Docker images (e.g., my-test-tag)'
        required: false
        default: ''

permissions:
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io
  CRATEAPI_IMAGE_NAME: ghcr.io/lilrobo/cratenfcapi-crateapi
  UNFURLAPI_IMAGE_NAME: ghcr.io/lilrobo/cratenfcapi-unfurlapi

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.x'
      - name: Restore dependencies
        run: dotnet restore
      - name: Build with dotnet
        run: dotnet build --no-restore 

  test:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - uses: actions/checkout@v4
      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.x'
      - name: Restore dependencies
        run: dotnet restore
      - name: Run unit tests
        run: dotnet test --no-restore --verbosity normal
        # The step will be marked as failed but the workflow will continue
        continue-on-error: true
      
  build-crateapi:
    runs-on: ubuntu-latest
    needs: [lint, test]  # Waits for lint and test to complete
    if: always()  # Runs even if test fails
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata for CrateApi
        id: meta-crateapi
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.CRATEAPI_IMAGE_NAME }}
          flavor: |
            latest=false
          tags: |
            type=raw,value=latest,enable=${{ github.event_name == 'push' && github.ref_name == 'master' }},event=push
            type=semver,pattern={{version}},enable=${{ github.event_name == 'push' }}
            type=ref,event=tag,enable=${{ github.event_name == 'push' }}
            type=ref,event=branch,enable=${{ github.event_name == 'push' && github.ref_name != 'master' }}
            type=raw,value={{inputs.tag}},event=workflow_dispatch,enable=${{ github.event_name == 'workflow_dispatch' }}
            type=ref,event=pr,enable=${{ github.event_name == 'pull_request' }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push CrateApi
        uses: docker/build-push-action@v5
        with:
          context: .
          file: CrateApi/Dockerfile
          # platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta-crateapi.outputs.tags }}

  build-unfurlapi:
    runs-on: ubuntu-latest
    needs: [lint, test]  # Waits for lint and test to complete
    if: always()  # Runs even if test fails
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata for UnfurlApi
        id: meta-unfurlapi
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.UNFURLAPI_IMAGE_NAME }}
          flavor: |
            latest=false
          tags: |
            type=raw,value=latest,enable=${{ github.event_name == 'push' && github.ref_name == 'master' }},event=push
            type=semver,pattern={{version}},enable=${{ github.event_name == 'push' }}
            type=ref,event=tag,enable=${{ github.event_name == 'push' }}
            type=ref,event=branch,enable=${{ github.event_name == 'push' && github.ref_name != 'master' }}
            type=raw,value={{inputs.tag}},event=workflow_dispatch,enable=${{ github.event_name == 'workflow_dispatch' }}
            type=ref,event=pr,enable=${{ github.event_name == 'pull_request' }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push UnfurlApi
        uses: docker/build-push-action@v5
        with:
          context: .
          file: UnfurlApi/Dockerfile
          # platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta-unfurlapi.outputs.tags }}