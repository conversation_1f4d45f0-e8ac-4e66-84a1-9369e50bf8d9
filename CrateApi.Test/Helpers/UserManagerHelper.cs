using CrateApi.Services.Authentication;
using Moq;

namespace CrateApi.Test.Helpers;

public static class UserManagerHelper
{
    public static Mock<IUserManager> MockUserManager(Guid userId)
    {
        var rq = new MockRequestContext { IpAddress = "127.0.0.1", UserAgent = "macOs blah blah", RequestPath = "test/api/com" };
        var userContext = UserContext.CreateAuthenticated(userId, rq);
        var mockUserManager = new Mock<IUserManager>();
        mockUserManager.Setup(u => u.Current).Returns(userContext);
        return mockUserManager;
    }
}

public record MockRequestContext : IRequestContext
{
    public string IpAddress { get; init; } 
    public string UserAgent { get; init; } 
    public string RequestPath { get; init; } 
}
