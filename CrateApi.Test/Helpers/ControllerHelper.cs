using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace CrateApi.Test.Helpers;

public static class ControllerHelper
{
    public static T GetResponseFromController<T>(IActionResult result)
    {
        Assert.NotNull(result, "[GetResponseFromController] IActionResult was null");

        var r = ((ObjectResult)result).Value;
        Assert.NotNull(r);

        var response = (T)r!;
        Assert.NotNull(response, "[GetResponseFromController] Failed to convert to correct type");
        return response;
    }
}
