using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Common.Dto.Request;
using CrateApi.Data.Models;
using CrateApi.Data;
using CrateApi.Services.Database;
using CrateApi.Services.Runtimes;
using LanguageExt;
using Microsoft.EntityFrameworkCore;
using Moq;
using Microsoft.Extensions.Logging;
using CrateApi.Test.Helpers;
using CrateApi.Common.Dto.Response;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using CrateApi.Routes.CollectionEndpoints;
using FastEndpoints;
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
namespace CrateApi.Test.CollectionControllerTests;
public class RemoveTracksFromCollectionTests
{
    private CrateDbContext dbContext;

    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<CrateDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        dbContext = new CrateDbContext(options);
    }

    [Test]
    public async Task RemoveTrackFromEmptyCollection()
    {
        var userId      = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var createdDate = DateTime.Now;
        var updatedDate = DateTime.Now;
        var runtime     = CollectionEndpointHelper.CreateRuntime(dbContext, userManager);
        var endpoint    = Factory.Create<RemoveTracks>(runtime);
        var t_1         = Items.CreateTrack(null, userId, "Track1", "Artist1", "someImage.Png", "url", "domainUrl", createdDate, updatedDate);
        var t_2         = Items.CreateTrack(null, userId, "Track2", "Artist2", "someImage.Png", "url", "domainUrl", createdDate, updatedDate);
        var e_1         = dbContext.Tracks.Add(t_1);
        var e_2         = dbContext.Tracks.Add(t_2);
        var c_1         = dbContext.Collections.Add(new Collection { Name = "Test", Thumbnail = "Test" , UserId = userId });
        await dbContext.SaveChangesAsync();
        //end setup
        
        endpoint.HttpContext.Request.RouteValues.Add("id", c_1.Entity!.Id!.Value);
        await endpoint.HandleAsync(new List<int>{ e_1.Entity.Id!.Value, e_2.Entity.Id!.Value }, default(CancellationToken));

        Assert.That(endpoint.HttpContext.Response.StatusCode, Is.EqualTo(200));
        var result = (CollectionResponseDto)endpoint.Response!;
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Contents.Count, Is.EqualTo(0));

        var trackMappings = await dbContext.CollectionTrackMappings.ToListAsync();
        Assert.That(trackMappings.Count, Is.EqualTo(0));
    }

        [Test]
    public async Task RemoveTracksThatExistAndHaveAlreadyBeenRemoved()
    {
        var userId      = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var createdDate = DateTime.Now;
        var updatedDate = DateTime.Now;
        var runtime     = CollectionEndpointHelper.CreateRuntime(dbContext, userManager);
        var endpoint    = Factory.Create<RemoveTracks>(runtime);
        var t_1         = Items.CreateTrack(null, userId, "Track1", "Artist1", "someImage.Png", "url", "domainUrl", createdDate, updatedDate);
        var t_2         = Items.CreateTrack(null, userId, "Track2", "Artist2", "someImage.Png", "url", "domainUrl", createdDate, updatedDate);
        var t_3         = Items.CreateTrack(null, userId, "Track3", "Artist3", "someImage.Png", "url", "domainUrl", createdDate, updatedDate);
        var e_1         = dbContext.Tracks.Add(t_1);
        var e_2         = dbContext.Tracks.Add(t_2);
        var e_3         = dbContext.Tracks.Add(t_3);
        var c_1         = dbContext.Collections.Add(new Collection { Name = "Test", Thumbnail = "Test" , UserId = userId });
        dbContext.CollectionTrackMappings.Add(new CollectionTrackMapping { CollectionId = c_1.Entity.Id!.Value, TrackId = e_1.Entity.Id!.Value });
        dbContext.CollectionTrackMappings.Add(new CollectionTrackMapping { CollectionId = c_1.Entity.Id!.Value, TrackId = e_2.Entity.Id!.Value });
        await dbContext.SaveChangesAsync();
        //end setup

        endpoint.HttpContext.Request.RouteValues.Add("id", c_1.Entity!.Id!.Value);
        await endpoint.HandleAsync(new List<int>{ e_1.Entity.Id!.Value, e_2.Entity.Id!.Value, e_3.Entity.Id!.Value }, default(CancellationToken));
        Assert.That(endpoint.HttpContext.Response.StatusCode, Is.EqualTo(200));
        var result = (CollectionResponseDto)endpoint.Response!;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Contents.Count, Is.EqualTo(0)); // The tracks that already existed in the collection should not be added
        var trackMappings = await dbContext.CollectionTrackMappings.ToListAsync();
        Assert.That(trackMappings.Count, Is.EqualTo(0)); // Check db also to be correct
    }

    [Test]
    public async Task CannotRemoveTrackFromNonExistantCollection()
    {
        var userId      = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var otherUserId = Guid.Parse("00000000-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var createdDate = DateTime.Now;
        var updatedDate = DateTime.Now;
        var runtime     = CollectionEndpointHelper.CreateRuntime(dbContext, userManager);
        var endpoint    = Factory.Create<RemoveTracks>(runtime);
        var t_1         = Items.CreateTrack(null, userId, "Track1", "Artist1", "someImage.Png", "url", "domainUrl", createdDate, updatedDate);
        var e_1         = dbContext.Tracks.Add(t_1);
        var c_1         = dbContext.Collections.Add(new Collection { Name = "Test", Thumbnail = "Test" , UserId = otherUserId }); //track does not belong to current user
        await dbContext.SaveChangesAsync();
        //end setup
        

        endpoint.HttpContext.Request.RouteValues.Add("id", c_1.Entity!.Id!.Value);
        await endpoint.HandleAsync(new List<int>{ e_1.Entity.Id!.Value}, default(CancellationToken));
        Assert.That(endpoint.HttpContext.Response.StatusCode, Is.EqualTo(404));

        var collection = await dbContext.Collections
            .Include(e => e.CollectionTracks)
            .ThenInclude(e => e.Track)
            .FirstAsync(e => e.Id == c_1.Entity.Id.Value);

        Assert.That(collection.CollectionTracks.Count, Is.EqualTo(0)); // Check db also to be correct
    }

    [TearDown]
    public void TearDown()
    {
        dbContext?.Dispose();
    }
}
