using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Common.Dto.Request;
using CrateApi.Data.Models;
using CrateApi.Data;
using CrateApi.Services.Database;
using CrateApi.Services.Runtimes;
using LanguageExt;
using Microsoft.EntityFrameworkCore;
using Moq;
using Microsoft.Extensions.Logging;
using CrateApi.Test.Helpers;
using CrateApi.Common.Dto.Response;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using FastEndpoints;
using CrateApi.Routes.CollectionEndpoints;
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.

namespace CrateApi.Test.CollectionControllerTests;
public  class AddTracksToCollectionTests
{
    private CrateDbContext dbContext;

    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<CrateDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        dbContext = new CrateDbContext(options);
    }

    [Test]
    public async Task AddContentToCollection()
    {
        //setup
        var userId                  = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var userManager             = UserManagerHelper.MockUserManager(userId).Object;
        var createdDate             = DateTime.Now;
        var updatedDate             = DateTime.Now;
        var runtime                 = CollectionEndpointHelper.CreateRuntime(dbContext, userManager);
        var endpoint                = Factory.Create<AddTracks>(runtime);
        var content1                = Items.CreateContent(null, userId, "Track1", "Artist1", "url", "domainUrl", createdDate, updatedDate);
        var content2                = Items.CreateContent(null, userId, "Track2", "Artist2", "url", "domainUrl", createdDate, updatedDate);
        var addedContent1           = dbContext!.Contents.Add(content1);
        var addedContent2           = dbContext!.Contents.Add(content2);
        var collection1             = dbContext!.Collections.Add(new Collection { Name = "Test", Thumbnail = "Test" , UserId = userId });

        await dbContext!.SaveChangesAsync();
        //end setup

        endpoint.HttpContext.Request.RouteValues.Add("id", collection1.Entity!.Id!.Value);
        await endpoint.HandleAsync(new List<int>{ addedContent1.Entity.Id!.Value, addedContent2.Entity.Id!.Value }, default(CancellationToken));
        var result = (CollectionResponseDto)endpoint.Response!;
        Assert.That(endpoint.HttpContext.Response.StatusCode, Is.EqualTo(200));

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Contents.Count, Is.EqualTo(2));
        var trackMappings = await dbContext.CollectionContentMappings.ToListAsync();
        Assert.That(trackMappings.Count, Is.EqualTo(2));
    }

    [Test]
    public async Task AddContentThatAlreadyExists()
    {
        //setup
        var userId                  = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var userManager             = UserManagerHelper.MockUserManager(userId).Object;
        var createdDate             = DateTime.Now;
        var updatedDate             = DateTime.Now;
        var runtime                 = CollectionEndpointHelper.CreateRuntime(dbContext, userManager);
        var endpoint                = Factory.Create<AddTracks>(runtime);
        var content1                = Items.CreateContent(null, userId, "Track1", "Artist1", "url", "domainUrl", createdDate, updatedDate);
        var content2                = Items.CreateContent(null, userId, "Track2", "Artist2", "url", "domainUrl", createdDate, updatedDate);
        var content3                = Items.CreateContent(null, userId, "Track3", "Artist3", "url", "domainUrl", createdDate, updatedDate);
        var addedContent1           = dbContext.Contents.Add(content1);
        var addedContent2           = dbContext.Contents.Add(content2);
        var addedContent3           = dbContext.Contents.Add(content3);
        var collection1             = dbContext.Collections.Add(new Collection { Name = "Test", Thumbnail = "Test" , UserId = userId });
        dbContext.CollectionContentMappings.Add(new CollectionContentMapping { CollectionId = collection1.Entity.Id!.Value, ContentId = addedContent1.Entity.Id!.Value });
        dbContext.CollectionContentMappings.Add(new CollectionContentMapping { CollectionId = collection1.Entity.Id!.Value, ContentId = addedContent2.Entity.Id!.Value });
        await dbContext.SaveChangesAsync();
        //end setup
        

        endpoint.HttpContext.Request.RouteValues.Add("id", collection1.Entity!.Id!.Value);
        await endpoint.HandleAsync(new List<int>{ addedContent1.Entity.Id!.Value, addedContent2.Entity.Id!.Value, addedContent3.Entity.Id!.Value }, default(CancellationToken));
        var result = (CollectionResponseDto)endpoint.Response!;
        Assert.That(endpoint.HttpContext.Response.StatusCode, Is.EqualTo(200));

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Contents.Count, Is.EqualTo(3)); // The tracks that already existed in the collection should not be added

        var trackMappings = await dbContext.CollectionContentMappings.ToListAsync();
        Assert.That(trackMappings.Count, Is.EqualTo(3)); // Check db also to be correct
    }

        [Test]
    public async Task CannotAddContentToNonExistentCollection()
    {
        var userId              = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var otherUserId         = Guid.Parse("00000000-1576-4a9c-ba11-c32882be3345");
        var userManager         = UserManagerHelper.MockUserManager(userId).Object;
        var createdDate         = DateTime.Now;
        var updatedDate         = DateTime.Now;
        var content1            = Items.CreateContent(null, userId, "Track1", "Artist1", "url", "domainUrl", createdDate, updatedDate);
        var e_1                 = dbContext.Contents.Add(content1);
        var c_1                 = dbContext.Collections.Add(new Collection { Name = "Test", Thumbnail = "Test" , UserId = otherUserId }); //track does not belong to current user
        var runtime             = CollectionEndpointHelper.CreateRuntime(dbContext, userManager);
        var endpoint            = Factory.Create<AddTracks>(runtime);

        await dbContext.SaveChangesAsync();
        //end setup

        endpoint.HttpContext.Request.RouteValues.Add("id", c_1.Entity!.Id!.Value);
        await endpoint.HandleAsync(new List<int>{ e_1.Entity.Id!.Value}, default(CancellationToken));
        Assert.That(endpoint.HttpContext.Response.StatusCode, Is.EqualTo(404));

        var collection = await dbContext.Collections
            .Include(e => e.CollectionTracks)
            .ThenInclude(e => e.Track)
            .FirstAsync(e => e.Id == c_1.Entity.Id.Value);

        Assert.That(collection.CollectionContent.Count, Is.EqualTo(0)); // Check db also to be correct
    }

    [TearDown]
    public void TearDown()
    {
        dbContext?.Dispose();
    }
}
