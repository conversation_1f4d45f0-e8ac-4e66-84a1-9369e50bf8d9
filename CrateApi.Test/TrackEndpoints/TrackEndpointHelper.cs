using CrateApi.Data;
using CrateApi.Services.Authentication;
using CrateApi.Services.Runtimes;
using Microsoft.Extensions.Logging;
using Moq;

namespace CrateApi.Test.TrackEndpoints;

public static class TrackEndpointHelper
{
    public static ApiRuntime CreateRuntime(CrateDbContext dbContext, IUserManager userManager)
    {
        var logger2 = Mock.Of<ILogger<ApiRuntime>>(MockBehavior.Loose);
        var serviceProviderMock = new Mock<IServiceProvider>();
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(ILogger<ApiRuntime>)))
            .Returns(logger2);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(CrateDbContext)))
            .Returns(dbContext);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IUserManager)))
            .Returns(userManager);

        return ApiRuntime.Create(serviceProviderMock.Object);
    }
}
