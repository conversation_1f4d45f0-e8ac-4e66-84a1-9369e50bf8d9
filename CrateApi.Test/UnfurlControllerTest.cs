using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using CrateApi.Common;
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Data;
using CrateApi.Routes.UnfurlEndpoints;
using CrateApi.Services.Authentication;
using CrateApi.Services.DomainTypes;
using CrateApi.Services.Runtimes;
using CrateApi.Test.Helpers;
using FastEndpoints;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Moq;
using Moq.Protected;

namespace CrateApi.Test;
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
/*
 * 
 * Just one controller test. Unfurl is good because it covers the whole flow. (unfurl, get user token, add to database with userId from token)
 * 
 * Most important aspect of these tests is to make sure we test the JWT flow for endpoints that have the authorized attribute.
 * Meaning the UserManager should ACTUALLY read its value from a token rather than just a manually created user manager.
 * 
 */
public class UnfurlEndpointTests
{
    public const string JwtKey1 = "ValidSecuritySigningKeyBlahBlah32BytesLong";
    public const string JwtKey2 = "InvalidKeyDifferentFromServerExpectation123";

    private const string validSpotifyUrl = "https://open.spotify.com/bladeeTrack123";
    private const string validYoutubeUrl = "https://youtu.be/3-MSlNVqzYY";

    private CrateDbContext dbContext;

    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<CrateDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
            
        dbContext = new CrateDbContext(options);
    }

    [TearDown]
    public void TearDown()
    {
        dbContext.Dispose();
    }

    [Test]
    public async Task Endpoint_UnfurlsSpotifyTrack_AndSavesToDatabase()
    {
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var name = "username";
        var serviceSettings = new ServiceSettings("https://test.com");
        var domainUnfurl = DomainUnfurlUrl.From(validSpotifyUrl).ThrowIfFail();
        var content = new ContentUnfurled("Song", "Artist", "Url", "Url2", (int)UnfurledContentType.Spotify);
        var requestUrl = $"{serviceSettings.UnfurlServiceUrl}/api/v1/unfurl/{domainUnfurl.Endpoint}";
        var httpClientFactory = HttpMockHelper.CreateMockJsonResponseFactoryForUri(
            e => e.RequestUri == new Uri(requestUrl),
            content
        );
        var httpContext = new DefaultHttpContext();
        JwtTestHelper.AddAuthorizationHeader(httpContext, userId, name);
        var httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContext);
        var userManager = UserManagerHelper.MockUserManager(userId).Object;

        var apiRuntime = CreateServiceProvider(dbContext, userManager, httpClientFactory.Object, serviceSettings, httpContext);

        var endpoint = Factory.Create<Unfurl>(apiRuntime);

        var requestModel = new UnfurlUrlRequestDto(validSpotifyUrl);
        await endpoint.HandleAsync(requestModel, CancellationToken.None);

        Assert.That(endpoint.HttpContext.Response.StatusCode, Is.EqualTo(200));

        var responseBody = (UnfurledContentDto)endpoint.Response;
        Assert.That(responseBody!.Title, Is.EqualTo(content.Title));

        var tracks = await dbContext.Tracks.ToListAsync();
        Assert.That(tracks.Count, Is.EqualTo(1));
        var track = tracks.First();
        Assert.That(track.UserId, Is.EqualTo(userId));
        Assert.That(track.PlatformType, Is.EqualTo((int)UnfurledContentType.Spotify));
        
        var contents = await dbContext.Contents.ToListAsync();
        Assert.That(contents.Count, Is.EqualTo(1));
        var contentEntity = contents.First();
        Assert.That(contentEntity.UserId, Is.EqualTo(userId));
        
        var mappings = await dbContext.TrackContentMappings.ToListAsync();
        Assert.That(mappings.Count, Is.EqualTo(1));
        var mapping = mappings.First();
        Assert.That(mapping.ContentId, Is.EqualTo(contentEntity.Id));
        Assert.That(mapping.TrackId, Is.EqualTo(track.Id));
    }
    
    [Test]
    public async Task AnonymousEndpoint_UnfurlsSpotifyTrack_DoesNotSaveToDatabase()
    {
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var name = "username";
        var serviceSettings = new ServiceSettings("https://test.com");
        var domainUnfurl = DomainUnfurlUrl.From(validSpotifyUrl).ThrowIfFail();
        var content = new ContentUnfurled("Song", "Artist", "Url", "Url2", (int)UnfurledContentType.YoutubeVideo);
        var requestUrl = $"{serviceSettings.UnfurlServiceUrl}/api/v1/unfurl/{domainUnfurl.Endpoint}";
        var httpClientFactory = HttpMockHelper.CreateMockJsonResponseFactoryForUri(
            e => e.RequestUri == new Uri(requestUrl),
            content
        );
        var httpContext = new DefaultHttpContext();
        JwtTestHelper.AddAuthorizationHeader(httpContext, userId, name);
        var httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContext);
        var userManager = UserManagerHelper.MockUserManager(userId).Object;

        var apiRuntime = CreateServiceProvider(dbContext, userManager, httpClientFactory.Object, serviceSettings, httpContext);
        var endpoint = Factory.Create<UnfurlAnonymous>(apiRuntime);

        var requestModel = new UnfurlUrlRequestDto(validSpotifyUrl);
        await endpoint.HandleAsync(requestModel, CancellationToken.None);

        Assert.That(endpoint.HttpContext.Response.StatusCode, Is.EqualTo(200));

        var responseBody = (UnfurledContentDto)endpoint.Response;
        Assert.That(responseBody!.Title, Is.EqualTo(content.Title));

        var tracks = await dbContext.Tracks.ToListAsync();
        Assert.That(tracks.Count, Is.EqualTo(0), "Anonymous unfurl should not save track to db");
    }
    
    [Test]
    public async Task Endpoint_UnfurlsYoutubeTrack_HandlesCorrectly()
    {
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var name = "username";
        var serviceSettings = new ServiceSettings("https://test.com");
        var domainUnfurl = DomainUnfurlUrl.From(validYoutubeUrl).ThrowIfFail();
        var content = new ContentUnfurled("Song", "Artist", "Url", "Url2", (int)UnfurledContentType.YoutubeVideo);
        var requestUrl = $"{serviceSettings.UnfurlServiceUrl}/api/v1/unfurl/{domainUnfurl.Endpoint}";
        var httpClientFactory = HttpMockHelper.CreateMockJsonResponseFactoryForUri(
            e => e.RequestUri == new Uri(requestUrl),
            content
        );
        var httpContext = new DefaultHttpContext();
        JwtTestHelper.AddAuthorizationHeader(httpContext, userId, name);
        var httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContext);
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var apiRuntime = CreateServiceProvider(dbContext, userManager, httpClientFactory.Object, serviceSettings, httpContext);

        var endpoint = Factory.Create<Unfurl>(apiRuntime);
        var requestModel = new UnfurlUrlRequestDto(validYoutubeUrl);
        await endpoint.HandleAsync(requestModel, CancellationToken.None);

        Assert.That(endpoint.HttpContext.Response.StatusCode, Is.EqualTo(200));

        var responseBody = (UnfurledContentDto)endpoint.Response;
        Assert.That(responseBody!.Title, Is.EqualTo(content.Title));

        var tracks = await dbContext.Tracks.ToListAsync();
        Assert.That(tracks.Count, Is.EqualTo(0)); 
        
        var contents = await dbContext.Contents.ToListAsync();
        Assert.That(contents.Count, Is.EqualTo(1));
        var contentEntity = contents.First();
        Assert.That(contentEntity.UserId, Is.EqualTo(userId));
        Assert.That(contentEntity.Platform, Is.EqualTo((int)UnfurledContentType.YoutubeVideo));
    }
    
    [Test]
    public async Task Endpoint_WithInvalidUrl_ThrowsException()
    {
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var name = "username";
        var serviceSettings = new ServiceSettings("https://test.com");
        var domainUnfurl = DomainUnfurlUrl.From(validYoutubeUrl).ThrowIfFail();
        var content = new ContentUnfurled("Song", "Artist", "Url", "Url2", (int)UnfurledContentType.YoutubeVideo);
        var requestUrl = $"{serviceSettings.UnfurlServiceUrl}/api/v1/unfurl/{domainUnfurl.Endpoint}";
        var httpClientFactory = HttpMockHelper.CreateMockJsonResponseFactoryForUri(
            e => e.RequestUri == new Uri(requestUrl),
            content
        );
        var httpContext = new DefaultHttpContext();
        JwtTestHelper.AddAuthorizationHeader(httpContext, userId, name);
        var httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContext);
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var apiRuntime = CreateServiceProvider(dbContext, userManager, httpClientFactory.Object, serviceSettings, httpContext);
        var endpoint = Factory.Create<Unfurl>(apiRuntime);

        var requestModel = new UnfurlUrlRequestDto("mal';formed ur;l");
        await endpoint.HandleAsync(requestModel, CancellationToken.None);
        Assert.That(endpoint.HttpContext.Response.StatusCode, Is.Not.EqualTo(200));
    }

    private ApiRuntime CreateServiceProvider(
        CrateDbContext context,
        IUserManager userManager,
        IHttpClientFactory factory,
        ServiceSettings serviceSettings,
        HttpContext httpContext)
    {
        var logger2 = Mock.Of<ILogger<ApiRuntime>>(MockBehavior.Loose);

        var serviceProviderMock = new Mock<IServiceProvider>();
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(ILogger<ApiRuntime>)))
            .Returns(logger2);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(CrateDbContext)))
            .Returns(context);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IUserManager)))
            .Returns(userManager);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(ServiceSettings)))
            .Returns(serviceSettings);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IHttpClientFactory)))
            .Returns(factory);

        var serviceProvider = serviceProviderMock.Object;
        return ApiRuntime.Create(serviceProviderMock.Object);

    }
}

public static class JwtTestHelper
{
    private const string TestSecret = "SomeTestSecuritySigningKeyBlahBlah32BytesLong123";
        
    public static string GenerateJwtToken(Guid userId, string username)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(TestSecret);
            
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, userId.ToString()),
            new Claim(ClaimTypes.Name, username)
        };
            
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddHours(1),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key),  SecurityAlgorithms.HmacSha256Signature)
        };
            
        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }
        
    public static void AddAuthorizationHeader(HttpContext httpContext, Guid userId, string nameClaim)
    {
        var token = GenerateJwtToken(userId, nameClaim);
        httpContext.Request.Headers.Add("Authorization", $"Bearer {token}");

        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, userId.ToString()),
            new Claim(ClaimTypes.Name, nameClaim)
        };
            
        var identity = new ClaimsIdentity(claims, JwtBearerDefaults.AuthenticationScheme);
        httpContext.User = new ClaimsPrincipal(identity);
    }

    public static void AddAuthorizationHeader(HttpContext httpContext, Guid userId, string nameClaim, string invalidTestToken)
    {
        httpContext.Request.Headers.Add("Authorization", $"Bearer {invalidTestToken}");

        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, userId.ToString()),
            new Claim(ClaimTypes.Name, nameClaim)
        };
            
        var identity = new ClaimsIdentity(claims, JwtBearerDefaults.AuthenticationScheme);
        httpContext.User = new ClaimsPrincipal(identity);
    }
}

public static class HttpMockHelper
{
    public static Mock<IHttpClientFactory> CreateMockHttpClientFactory(
        Expression<Func<HttpRequestMessage, bool>> requestMatcher,
        HttpResponseMessage response,
        string clientName = null!)
    {
        var handlerMock = new Mock<HttpMessageHandler>();
        
        handlerMock.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is(requestMatcher),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(response);
        
        var client = new HttpClient(handlerMock.Object);
        
        var factory = new Mock<IHttpClientFactory>();
        factory.Setup(f => f.CreateClient(It.IsAny<string>())).Returns(client);
        
        return factory;
    }
    public static Mock<IHttpClientFactory> CreateMockHttpClientFactoryForUri(
        string uri,
        HttpResponseMessage response,
        string clientName = null!)
    {
        return CreateMockHttpClientFactory(
            req => req.RequestUri == new Uri(uri),
            response,
            clientName);
    }
    public static Mock<IHttpClientFactory> CreateMockJsonResponseFactory<T>(
        Expression<Func<HttpRequestMessage, bool>> requestMatcher,
        T responseObject,
        HttpStatusCode statusCode = HttpStatusCode.OK,
        string clientName = null!)
    {
        var response = new HttpResponseMessage
        {
            StatusCode = statusCode,
            Content = new StringContent(
                JsonSerializer.Serialize(responseObject),
                System.Text.Encoding.UTF8,
                "application/json")
        };
        
        return CreateMockHttpClientFactory(requestMatcher, response, clientName);
    }
    public static Mock<IHttpClientFactory> CreateMockJsonResponseFactoryForUri<T>(
        Expression<Func<HttpRequestMessage, bool>> requestMatcher,
        T responseObject,
        HttpStatusCode statusCode = HttpStatusCode.OK,
        string clientName = null!)
    {
        return CreateMockJsonResponseFactory(
            requestMatcher,
            responseObject,
            statusCode,
            clientName);
    }
}

