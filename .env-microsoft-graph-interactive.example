# Microsoft Graph Interactive Authentication Configuration
# Copy this file to .env-microsoft-graph-interactive and fill in your values

# Your Azure AD Tenant ID (GUID)
MS_GRAPH_TENANT_ID=your-tenant-id-here

# Your App Registration Client ID (GUID)
MS_GRAPH_CLIENT_ID=your-client-id-here

# NOTE: No client secret needed for interactive authentication!

# Required App Registration Configuration:
# 1. Authentication > Platform configurations > Add a platform > Mobile and desktop applications
# 2. Add redirect URI: http://localhost:8080
# 3. API permissions > Add a permission > Microsoft Graph > DELEGATED permissions:
#    - User.ReadWrite.All (Delegated)
#    - Directory.ReadWrite.All (Delegated)
# 4. Grant admin consent for your organization
#
# Required User Permissions:
# The user who runs this script must have one of these Azure AD roles:
# - Global Administrator
# - User Administrator  
# - Privileged Authentication Administrator
#
# Security Benefits:
# - No client secret stored anywhere
# - Requires actual admin to sign in each time
# - Uses the admin's permissions, not app permissions
# - Browser-based authentication with MFA support
# - Token caching for convenience during session
