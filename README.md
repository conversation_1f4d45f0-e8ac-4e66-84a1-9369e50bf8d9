# NETCrateAPI

CrateNFC API Implementation in C# using .NET 8.

## Prerequisites

Before running the project, ensure you have the necessary tools installed.

### Install .NET 8 SDK

The application requires the [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0) to build and run.

#### macOS Installation (Recommended via Homebrew)

Install [.NET 8 using Homebrew on macOS](<https://formulae.brew.sh/formula/dotnet@8>).

```sh
brew install dotnet@8
echo 'export PATH="/opt/homebrew/opt/dotnet@8/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
dotnet --version

# Expected output:
# 8.0.112
```

You may need to update `DOTNET_ROOT` in your shell profile to point to the correct .NET SDK path.

```sh
dotnet --info
# Environment variables:
#   DOTNET_ROOT       [/opt/homebrew/Cellar/dotnet@8/8.0.12/libexec]
```

### Install Entity Framework Core CLI Tool

Install dotnet-ef tool to work with database models and migrations.

```sh
dotnet tool install --global dotnet-ef
```

More details: [Entity Framework Core CLI](https://learn.microsoft.com/en-us/ef/core/cli/dotnet)

## Clone and Set Up the Project

### Clone the Repository

```sh
<NAME_EMAIL>:lilrobo/CrateNFCAPI.git>
cd CrateNFCAPI
```

### Set Up the Local SQLite Database

Create the database directory and local sqlite database file

```sh
mkdir -p CrateApi.Data/Data
touch CrateApi.Data/Data/cratedb.db
```

### Apply Database Migrations

Run database migrations to create the necessary tables in the SQLite database.

```sh
cd ./CrateApi.Data
dotnet-ef database update --startup-project ../CrateApi
```

## Run the Application

### Start the API Server and Unfurl Api Server


```sh
make run
make run2 #seperate terminal window
```

The main server will be accessible at at [http://localhost:8000](http://localhost:8000).

## Verify API Endpoints

### Open Swagger UI

Explore API documentation and test endpoints using Swagger UI.

* [http://localhost:8000/swagger](http://localhost:8000/swagger)

## Testing with Sample Data

### Default API Credentials

```json
{
  "email": "<EMAIL>",
  "password": "123"
}
```

## Additional Notes

* If testing with the **Crate NFC** app, set the server to `http://localhost:8000` in the app settings.

* Refer to [.NET Documentation](https://learn.microsoft.com/en-us/dotnet/) for further reading.
