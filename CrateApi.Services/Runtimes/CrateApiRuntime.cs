using System.Diagnostics.Contracts;
using CrateApi.Common;
using CrateApi.Data;
using CrateApi.Services.Authentication;
using CrateApi.Services.DomainTypes;
using LanguageExt;
using LanguageExt.Traits;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using static LanguageExt.Prelude;

namespace CrateApi.Services.Runtimes;

public sealed record ApiRuntime :
    Has<Eff<ApiRuntime>, IHttpClientFactory>,
    Has<Eff<ApiRuntime>, IUserManager>,
    Has<Eff<ApiRuntime>, ILogger<ApiRuntime>>,
    Has<Eff<ApiRuntime>, CrateDbContext>,
    Has<Eff<ApiRuntime>, ServiceSettings>
{
    private sealed record ApiRuntimeEnv(IServiceProvider ServiceProvider);
    private ApiRuntimeEnv Env { get; }
    private ApiRuntime(ApiRuntimeEnv env) => Env = env;

    [Pure]
    static K<Eff<ApiRuntime>, IHttpClientFactory> Has<Eff<ApiRuntime>, IHttpClientFactory>.Ask =>
        liftEff<ApiRuntime, IHttpClientFactory>(e => e.Env.ServiceProvider.GetRequiredService<IHttpClientFactory>());

    [Pure]
    static K<Eff<ApiRuntime>, ILogger<ApiRuntime>> Has<Eff<ApiRuntime>, ILogger<ApiRuntime>>.Ask =>
        liftEff<ApiRuntime, ILogger<ApiRuntime>>(e => e.Env.ServiceProvider.GetRequiredService<ILogger<ApiRuntime>>());

    [Pure]
    static K<Eff<ApiRuntime>, ServiceSettings> Has<Eff<ApiRuntime>, ServiceSettings>.Ask =>
        liftEff<ApiRuntime, ServiceSettings>(e => e.Env.ServiceProvider.GetRequiredService<ServiceSettings>());

    [Pure]
    static K<Eff<ApiRuntime>, IUserManager> Has<Eff<ApiRuntime>, IUserManager>.Ask =>
        liftEff<ApiRuntime, IUserManager>(e => e.Env.ServiceProvider.GetRequiredService<IUserManager>());

    [Pure]
    static K<Eff<ApiRuntime>, CrateDbContext> Has<Eff<ApiRuntime>, CrateDbContext>.Ask =>
        liftEff<ApiRuntime, CrateDbContext>(e => e.Env.ServiceProvider.GetRequiredService<CrateDbContext>());

    public static ApiRuntime Create(IServiceProvider serviceProvider) =>
        new ApiRuntime(new ApiRuntimeEnv(serviceProvider));
}
