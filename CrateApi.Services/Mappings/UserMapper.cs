using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Common.Dto.Response;
using CrateApi.Common;
using CrateApi.Data.Models;
using Riok.Mapperly.Abstractions;

namespace CrateApi.Services.Mappings;

[Mapper]
public partial class UserMapper
{
    private static readonly UserMapper mapper = new UserMapper();
    public static UserMapper Instance => mapper;
    private UserMapper() { }

    [MapperIgnoreSource(nameof(User.Password))]
    public partial UserProfileResponseDto FromUserToResponse(User user);
}
