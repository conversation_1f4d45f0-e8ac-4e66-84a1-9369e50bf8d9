using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Data.Models;
using LanguageExt;
using LanguageExt.Traits;
using static LanguageExt.Prelude;
using Riok.Mapperly.Abstractions;
using LanguageExt.Common;

namespace CrateApi.Services.Mappings;

[Mapper]
public partial class CollectionMapper
{
    private static readonly CollectionMapper mapper = new CollectionMapper();
    public static CollectionMapper Instance => mapper;
    private CollectionMapper() { }

    [MapperIgnoreTarget(nameof(Collection.Id))]
    [MapperIgnoreTarget(nameof(Collection.UserId))]
    [MapperIgnoreTarget(nameof(Collection.Created))]
    [MapperIgnoreTarget(nameof(Collection.Updated))]
    [MapperIgnoreTarget(nameof(Collection.CollectionTracks))]
    public partial Collection FromAddCollectionDto(AddCollectionRequestDto entity);

    [MapperIgnoreTarget(nameof(CollectionResponseDto.Contents))]
    public partial CollectionResponseDto ToCollectionResponseDto(Collection entity);
}
public partial class CollectionMapper<M> where M : Monad<M>, Fallible<M>
{
    public static K<M, Collection> FromAddCollectionDto(AddCollectionRequestDto entity) =>
        M.Pure(CollectionMapper.Instance.FromAddCollectionDto(entity));
    public static K<M, CollectionResponseDto> ToCollectionResponseDto(Collection entity) =>
        M.Pure(CollectionMapper.Instance.ToCollectionResponseDto(entity));
    public static K<M, List<CollectionResponseDto>> ToCollectionResponseDto(List<Collection> entity) =>
        from value in entity.AsIterable().Traverse(ToCollectionResponseDto)
        select value.ToList();
    public static K<M, CollectionResponseDto> ToCollectionResponseDtoWithTracks(Collection entity) =>
        from dto in ToCollectionResponseDto(entity)
        from _0  in guard(entity.CollectionContent is not null, Error.New(new ArgumentException("Collection tracks not included when mapping")))
        from tr  in M.Pure(entity.CollectionContent.Select(ct => ct.Content).ToList())
        from mp  in M.Pure(ContentMapper.Instance.FromEntity(tr))
        from tt  in M.Map(e => e.Contents = mp, M.Pure(dto))
        select dto;

    public static K<M, List<CollectionResponseDto>> ToCollectionResponseDtoWithTracks(List<Collection> entities) =>
        from collections in entities.AsIterable().Traverse(ToCollectionResponseDtoWithTracks)
        select collections.ToList();
}


