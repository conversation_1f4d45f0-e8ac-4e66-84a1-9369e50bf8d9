using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Common.Dto.Response;
using CrateApi.Common;
using CrateApi.Data.Models;
using Riok.Mapperly.Abstractions;

namespace CrateApi.Services.Mappings;

[Mapper]
public partial class ContentMapper
{
    private static readonly ContentMapper mapper = new ContentMapper();
    public static ContentMapper Instance => mapper;
    private ContentMapper() { }

    [MapperIgnoreSource(nameof(IContentUnfurled.Type))]
    public partial UnfurledContentDto FromContent(IContentUnfurled content);

    [MapperIgnoreSource(nameof(Content.Platform))]
    public partial UnfurledContentDto FromEntity(Content content);
    public partial List<UnfurledContentDto> FromEntity(List<Content> content);

    [MapProperty(nameof(Track.Id), nameof(UnfurledContentDto.Id))]
    [MapProperty(nameof(Track.ArtistName), nameof(UnfurledContentDto.Detail))]
    [MapProperty(nameof(Track.MediaUrl), nameof(UnfurledContentDto.MediaUrl))]
    [MapProperty(nameof(Track.Url), nameof(UnfurledContentDto.Url))]
    [MapProperty(nameof(Track.TrackTitle), nameof(UnfurledContentDto.Title))]
    public partial UnfurledContentDto FromTrack(Track track);

    [MapProperty(nameof(IContentUnfurled.Type), nameof(Track.PlatformType))]
    [MapProperty(nameof(IContentUnfurled.Detail), nameof(Track.ArtistName))]
    [MapProperty(nameof(IContentUnfurled.MediaUrl), nameof(Track.MediaUrl))]
    [MapProperty(nameof(IContentUnfurled.Url), nameof(Track.Url))]
    [MapProperty(nameof(IContentUnfurled.Title), nameof(Track.TrackTitle))]
    public partial Track FromContentToTrack(IContentUnfurled content);

    [MapProperty(nameof(IContentUnfurled.Type), nameof(Content.Platform))]
    [MapProperty(nameof(IContentUnfurled.Detail), nameof(Content.Detail))]
    [MapProperty(nameof(IContentUnfurled.MediaUrl), nameof(Content.MediaUrl))]
    [MapProperty(nameof(IContentUnfurled.Url), nameof(Content.Url))]
    [MapProperty(nameof(IContentUnfurled.Title), nameof(Content.Title))]
    public partial Content FromContentToContent(IContentUnfurled content);
}
