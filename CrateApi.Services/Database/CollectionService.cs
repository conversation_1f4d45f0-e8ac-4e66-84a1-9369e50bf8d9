using System.Diagnostics;
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Extensions;
using CrateApi.Data;
using CrateApi.Data.Context;
using CrateApi.Data.Models;
using CrateApi.Data.Models.Interfaces;
using CrateApi.Services.Authentication;
using CrateApi.Services.Logic;
using CrateApi.Services.Mappings;
using CrateApi.Services.Runtimes;
using LanguageExt;
using LanguageExt.Common;
using LanguageExt.Traits;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using static LanguageExt.Prelude;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace CrateApi.Services.Database;


public static class CollectionService<M, RT>
    where RT :
        Has<M, CrateDbContext>,
        Has<M, IUserManager>,
        Has<M, ILogger<ApiRuntime>>
    where M :
        Monad<M>,
        Fallible<M>
{
    private static readonly Error CollectionNotFoundError          = Expected.New(404, $"Collection not found");
    private static readonly Error CollectionNotFoundOrDeletedError = Expected.New(204, $"Collection not found");
    private static readonly Error NothingToUpdateError             = Expected.New(422, $"Nothing to update");
    private static readonly Error InvalidValue                     = Expected.New(400, $"Invalid Value");

    private const string AddedCollection         = "User Added Collection with Id: {0}";
    private const string UpdatedCollection       = "User Updated Collection with Id: {0}";
    private const string UpdatedCollectionContent = "User Updated Collection with Id: {0}";
    private const string DeletedCollection       = "User Deleted Collection with Id: {0}";
    private const string DeletedAllCollections   = "User Deleted All of their collections";
    private static K<M, Collection> GetCollection(int collectionId) =>
        from cn in Has<M, RT, CrateDbContext>.ask
        from um in Has<M, RT, IUserManager>.ask
        from cl in IO.liftAsync(() => cn.Collections
            .Include(c => c.CollectionContent)
            .ThenInclude(c => c.Content)
            .FirstOrDefaultAsync(c => c.Id == collectionId && c.UserId == um.Current.UserId))
        select cl;

    private static K<M, List<Content>> GetContent(List<int> contentIds) =>
        from cn in Has<M, RT, CrateDbContext>.ask
        from tr in IO.liftAsync(() => cn.Contents.Where(t => contentIds.Contains(t.Id!.Value)).ToListAsync())
        select tr;

    private static List<CollectionContentMapping> CreateMappings(List<Content> content, int collectionId) =>
        content.Select(content => CollectionContentMapping.Create(collectionId, content.Id!.Value)).ToList();

    private static IO<List<int>> GetContentNotAlreadyAdded(List<int> contentIds, Collection collection) =>
        from ee in IO.lift(() => collection.CollectionContent.Select(ct => ct.ContentId).ToHashSet())
        from nn in IO.lift(() => contentIds.Where(id => !ee.Contains(id)).ToList())
        select nn;

    private static K<M, List<Collection>> GetPagedCollections(int start, int size) =>
        from context in Has<M, RT, CrateDbContext>.ask
        from um      in Has<M, RT, IUserManager>.ask
        from col     in IO.liftAsync(
            () =>
                context.Collections
                    .Include(e => e.CollectionContent)
                    .ThenInclude(cc => cc.Content)
                    .Where(e => e.UserId == um.Current.UserId)
                    .OrderByDescending(e => e.Updated)
                    .Skip(start)
                    .Take(size)
                    .ToListAsync())
        select col;

    private static K<M, Collection> UpdateCollectionDetails(Collection collection, UpdateCollectionRequestDto dto) =>
        from updated in M.Map(col => {
            if (!string.IsNullOrEmpty(dto.Name))      col.Name      = dto.Name;
            if (!string.IsNullOrEmpty(dto.Thumbnail)) col.Thumbnail = dto.Thumbnail;
            return collection;
        }, M.Pure(collection))
        select updated;

    public static K<M, CollectionResponseDto> AddContentToCollection(int collectionId, List<int> contentIds) =>
        from context                in Has<M, RT, CrateDbContext>.ask
        from _l                     in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog               in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(AddContentToCollection), OL.Start)
        from _0                     in guard(collectionId >= 0                           , InvalidValue)
        from collection             in GetCollection(collectionId)
        from _1                     in guard(collection is not null                      , CollectionNotFoundError)
        from _2                     in guard(contentIds is not null && contentIds?.Count > 0 , NothingToUpdateError)
        from contentNotAlreadyAdded in GetContentNotAlreadyAdded(contentIds, collection)
        from content                in GetContent(contentNotAlreadyAdded)
        from _3                     in guard(content.Count > 0                            , NothingToUpdateError)
        from _4                     in DbTraits<M, RT>.UpdateDbTraits(collection)
        from _5                     in DbTraits<M, RT>.UpdateDbTraitsList(content)
        from _6                     in IO.lift(() => collection.CollectionContent.AddRange(CreateMappings(content, collectionId)))
        from _7                     in context.UpdateListWithoutSaveIO(content)
        from updatedCollection      in context.UpdateAndSaveIO(collection)
        from _8                     in _l.LogInformationIO("Successfully added content to collection with id: {Id}", collectionId)
        from mapped                 in CollectionMapper<M>.ToCollectionResponseDtoWithTracks(updatedCollection)
        from _9                     in AuditService<M, RT>.CreateAudit(string.Format(UpdatedCollectionContent, collection.Id))
        from endLog                 in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(AddContentToCollection), OL.End)
        select mapped;

    public static K<M, CollectionResponseDto> RemoveContentFromCollection(int collectionId, List<int> contentIds) =>
        from context               in Has<M, RT, CrateDbContext>.ask
        from _l                    in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog              in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(RemoveContentFromCollection), OL.Start)
        from _0                    in guard(collectionId >= 0, InvalidValue)
        from collection            in GetCollection(collectionId)
        from _1                    in guard(collection is not null                      , CollectionNotFoundError)
        from _2                    in guard(contentIds is not null && contentIds?.Count > 0 , NothingToUpdateError)
        from mappingsToRemove      in IO.lift(() => collection.CollectionContent.Where(map => contentIds.Contains(map.ContentId)).AsQueryable())
        from contentToUpdate       in IO.lift(() => mappingsToRemove.Select(e => e.Content))
        from _3                    in DbTraits<M, RT>.UpdateDbTraits(collection)
        from _4                    in DbTraits<M, RT>.UpdateDbTraitsList(contentToUpdate)
        from _5                    in context.RemoveRangeWithoutSaveIO(mappingsToRemove)
        from _6                    in context.UpdateListWithoutSaveIO(contentToUpdate)
        from updatedCollection     in context.UpdateAndSaveIO(collection)
        from _7                    in _l.LogInformationIO("Successfully removed content from collection with id: {Id}", collectionId)
        from mapped                in CollectionMapper<M>.ToCollectionResponseDtoWithTracks(updatedCollection)
        from _8                    in AuditService<M, RT>.CreateAudit(string.Format(UpdatedCollectionContent, collection.Id))
        from endLog                in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(RemoveContentFromCollection), OL.End)
        select mapped;

    public static K<M, List<CollectionResponseDto>> Paged(int start, int size) =>
        from cn                    in Has<M, RT, CrateDbContext>.ask
        from um                    in Has<M, RT, IUserManager>.ask
        from _l                    in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog              in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(Paged), OL.Start)
        from _0                    in guard(start >= 0 && size >= 0, InvalidValue)
        from collections           in GetPagedCollections(start, size)
        from _1                    in _l.LogInformationIO("Successfully fetched paged collection with count: {Count}", collections.Count)
        from mapped                in CollectionMapper<M>.ToCollectionResponseDtoWithTracks(collections)
        from endLog                in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(Paged), OL.End)
        select mapped;

    public static K<M, CollectionResponseDto> Add(AddCollectionRequestDto dto) =>
        from context               in Has<M, RT, CrateDbContext>.ask
        from um                    in Has<M, RT, IUserManager>.ask
        from _l                    in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog              in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(Add), OL.Start)
        from collection            in CollectionMapper<M>.FromAddCollectionDto(dto)
        from _0                    in DbTraits<M, RT>.AssignDbTraits(collection)
        from updated               in context.AddAndSaveIO(collection)
        from _2                    in _l.LogInformationIO("Successfully Added collection with id: {Id}", updated.Id!.Value)
        from mapped                in CollectionMapper<M>.ToCollectionResponseDtoWithTracks(updated)
        from _3                    in AuditService<M, RT>.CreateAudit(string.Format(AddedCollection, collection.Id))
        from endLog                in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(Add), OL.End)
        select mapped;

    public static K<M, CollectionResponseDto> Update(int collectionId, UpdateCollectionRequestDto dto) =>
        from context               in Has<M, RT, CrateDbContext>.ask
        from um                    in Has<M, RT, IUserManager>.ask
        from _l                    in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog              in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(Update), OL.Start)
        from _0                    in guard(collectionId >= 0, InvalidValue)
        from _1                    in guard(dto.IsValidUpdate     , NothingToUpdateError)
        from collection            in IO.liftAsync(() => context.Collections.FirstOrDefaultAsync(e => e.Id == collectionId && e.UserId == um.Current.UserId))
        from _2                    in guard(collection is not null, CollectionNotFoundError)
        from updated               in UpdateCollectionDetails(collection, dto)
        from _3                    in DbTraits<M, RT>.UpdateDbTraits(updated)
        from updatedEntity         in context.UpdateAndSaveIO(updated)
        from _4                    in _l.LogInformationIO("Successfully Updated collection with id: {Id}", updated.Id!.Value)
        from mapped                in CollectionMapper<M>.ToCollectionResponseDtoWithTracks(updatedEntity)
        from _5                    in AuditService<M, RT>.CreateAudit(string.Format(UpdatedCollection, collection.Id))
        from endLog                in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(Update), OL.End)
        select mapped;

    public static K<M, Unit> Delete(int id) =>
        from context               in Has<M, RT, CrateDbContext>.ask
        from um                    in Has<M, RT, IUserManager>.ask
        from _l                    in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog              in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(Delete), OL.Start)
        from _0                    in guard(id >= 0, InvalidValue)
        from collection            in IO.liftAsync(() => context.Collections.FirstOrDefaultAsync(c => c.Id == id && c.UserId == um.Current.UserId))
        from _1                    in guard(collection is not null, CollectionNotFoundOrDeletedError)
        from _2                    in context.RemoveAndSaveIO(collection)
        from _3                    in _l.LogInformationIO("Successfully deleted collection with id: {Id}", id)
        from _5                    in AuditService<M, RT>.CreateAudit(string.Format(DeletedCollection, collection.Id))
        from endLog                in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(Delete), OL.End)
        select unit;
}
