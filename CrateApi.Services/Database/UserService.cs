using System.Diagnostics;
using System.Security.Claims;
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Extensions;
using CrateApi.Data;
using CrateApi.Data.Context;
using CrateApi.Data.Models;
using CrateApi.Services.Authentication;
using CrateApi.Services.Logic;
using CrateApi.Services.Mappings;
using CrateApi.Services.Runtimes;
using LanguageExt;
using LanguageExt.Common;
using LanguageExt.Traits;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using static LanguageExt.Prelude;

namespace CrateApi.Services.Database;

public static class UserService<M, RT>
    where RT :
        Has<M, CrateDbContext>,
        Has<M, IUserManager>,
        Has<M, ILogger<ApiRuntime>>
    where M :
        Monad<M>,
        Fallible<M>
{
    private const string EmailClaim = "email";
    private const string NameClaim  = "name";

    private static readonly Error UserNotFound          = Expected.New(404, "User not found");
    private static readonly Error NoSubjectClaim        = Expected.New(401, "No subject claim found in token");
    private static readonly Error InvalidClaims         = Expected.New(400, "Claims not found in claimsPrincipal");
    private static K<M, User?> FindUserByEntraSubjectId(string subjectId) =>
        from context        in Has<M, RT, CrateDbContext>.ask
        from user           in IO.liftAsync(() => context.Users.FirstOrDefaultAsync(u => u.EntraSubjectId == subjectId))
        select user;

    private static K<M, User> CreateUserFromClaims(ClaimsPrincipal claimsPrincipal, string subjectId) =>
        from context        in Has<M, RT, CrateDbContext>.ask
        from _l             in Has<M, RT, ILogger<ApiRuntime>>.ask
        from _0             in _l.LogInformationIO("Creating new user with Entra subject ID: {SubjectId}", subjectId)
        from email          in IO.lift(() => claimsPrincipal.FindFirst(EmailClaim)?.Value)
        from name           in IO.lift(() => claimsPrincipal.FindFirst(NameClaim)?.Value)
        from _1             in guard(email is not null && name is not null, InvalidClaims)
        from user           in M.Pure(User.New(email, name, subjectId))
        from moddedUser     in DbTraits<M, RT>.AssignDbTraits(user)
        from addedUser      in context.AddAndSaveIO(moddedUser)
        from _2             in _l.LogInformationIO("Created new user with ID: {UserId}", addedUser.Id?.ToString() ?? "unknown")
        select addedUser;

    private static K<M, User> UpdateUserLastLogin(User user) =>
        from context        in Has<M, RT, CrateDbContext>.ask
        from _l             in Has<M, RT, ILogger<ApiRuntime>>.ask
        from _0             in _l.LogInformationIO("Updating last login for user with ID: {UserId}", user.Id?.ToString() ?? "unknown")
        from updatedUser    in DbTraits<M, RT>.UpdateDbTraits(user)
        from _1             in context.UpdateAndSaveIO(updatedUser)
        select updatedUser;

    private static K<M, User> FindUserById(Guid userId) =>
        from context    in Has<M, RT, CrateDbContext>.ask
        from _l         in Has<M, RT, ILogger<ApiRuntime>>.ask
        from _0         in _l.LogInformationIO("Looking up user with ID: {UserId}", userId)
        from user       in IO.liftAsync(() => context.Users.FirstOrDefaultAsync(u => u.Id == userId))
        from validUser  in user != null
            ? M.Pure(user)
            : M.Fail<User>(UserNotFound)
        select validUser;

    private static K<M, Unit> DeleteUser(User user) =>
        from context        in Has<M, RT, CrateDbContext>.ask
        from _l             in Has<M, RT, ILogger<ApiRuntime>>.ask
        from _0             in _l.LogInformationIO("Deleting user with ID: {UserId}", user.Id?.ToString() ?? "unknown")
        from _              in context.RemoveAndSaveIO(user)
        select unit;

    public static K<M, Unit> DeleteUserById(Guid userId, ClaimsPrincipal claimsPrincipal) =>
        from _l             in Has<M, RT, ILogger<ApiRuntime>>.ask
        from userManager    in Has<M, RT, IUserManager>.ask
        from startLog       in _l.LogMethod(nameof(UserService<M, RT>), nameof(DeleteUserById), OL.Start)
        from subClaim       in IO.lift(() => claimsPrincipal.FindFirst("sub")?.Value)
        from _0             in guard(!string.IsNullOrEmpty(subClaim), NoSubjectClaim)
        from user           in FindUserById(userId)
        from _1             in guard(user.EntraSubjectId == subClaim, UserNotFound) // Only allow users to delete their own account or admins (future enhancement)
        from _              in DeleteUser(user)
        from endLog         in _l.LogMethod(nameof(UserService<M, RT>), nameof(DeleteUserById), OL.End)
        select unit;

    public static K<M, UserProfileResponseDto> GetOrCreateUserProfile(ClaimsPrincipal claimsPrincipal) =>
        from _l             in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog       in _l.LogMethod(nameof(UserService<M, RT>), nameof(GetOrCreateUserProfile), OL.Start)
        from subClaim       in IO.lift(() => claimsPrincipal.FindFirst("sub")?.Value)
        from _0             in guard(!string.IsNullOrEmpty(subClaim), NoSubjectClaim)
        from _1             in _l.LogInformationIO("Looking up user with Entra subject ID: {SubjectId}", subClaim)
        from existingUser   in FindUserByEntraSubjectId(subClaim)
        from user           in existingUser != null
            ? UpdateUserLastLogin(existingUser)
            : CreateUserFromClaims(claimsPrincipal, subClaim)
        from response       in M.Pure(UserMapper.Instance.FromUserToResponse(user))
        from endLog         in _l.LogMethod(nameof(UserService<M, RT>), nameof(GetOrCreateUserProfile), OL.End)
        select response;
}
