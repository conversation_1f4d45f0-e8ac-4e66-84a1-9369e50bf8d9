using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Extensions;
using CrateApi.Data.Models.Interfaces;
using CrateApi.Data.Models;
using CrateApi.Data;
using CrateApi.Services.Mappings;
using CrateApi.Services.Runtimes;
using LanguageExt.Common;
using LanguageExt.Traits;
using LanguageExt;
using Microsoft.Extensions.Logging;
using CrateApi.Common;
using CrateApi.Data.Context;
using Microsoft.EntityFrameworkCore;
using static LanguageExt.Prelude;
using CrateApi.Services.Logic;
using CrateApi.Services.Authentication;
using System.Linq.Expressions;

namespace CrateApi.Services.Database;

public static class ContentService<M, RT>
    where RT :
        Has<M, CrateDbContext>,
        Has<M, IUserManager>,
        Has<M, ILogger<ApiRuntime>>
    where M :
        Monad<M>,
        Fallible<M>
{
    private static readonly Error InvalidValue = Expected.New(400, $"Invalid value");

    private const string DeletedContent     = "User Deleted Content with Id: {0}";
    private const string DeletedAllContent  = "User Deleted All of their Content";

    private static K<M, List<Content>> FindUserContent(int start, int size) =>
        from context in Has<M, RT, CrateDbContext>.ask
        from um      in Has<M, RT, IUserManager>.ask
        from tracks  in IO.liftAsync(() =>
            context.Contents
                .Where(t => t.UserId == um.Current.UserId)
                .OrderByDescending(t => t.Updated)
                .Skip(start)
                .Take(size)
                .ToListAsync()
        )
        select tracks;
    private static K<M, List<Content>> FindAnyContent(int start, int size) =>
        from context in Has<M, RT, CrateDbContext>.ask
        from um      in Has<M, RT, IUserManager>.ask
        from tracks  in IO.liftAsync(() =>
            context.Contents
                .OrderByDescending(t => t.Updated)
                .Skip(start)
                .Take(size)
                .ToListAsync()
        )
        select tracks;

    public static K<M, UnfurledContentDto> Add(IContentUnfurled content) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Add), OL.Start)
        from dto           in
            content.Type switch {
                < (int)UnfurledContentType.Unsupported => AddTrackAndContent(content),
                _                                      => AddContent(content)
        }
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Add), OL.End)
        select dto;

    public static K<M, List<UnfurledContentDto>> Trending(int start = 0, int size = 20) =>
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Trending), OL.Start)
        from content       in FindAnyContent(start, size)
        from mapped        in M.Pure(ContentMapper.Instance.FromEntity(content))
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Trending), OL.End)
        select mapped;

    public static K<M, List<UnfurledContentDto>> Latest(int start = 0, int size = 20) =>
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Latest), OL.Start)
        from content       in FindUserContent(start, size)
        from mapped        in M.Pure(ContentMapper.Instance.FromEntity(content))
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Latest), OL.End)
        select mapped;

    private static K<M, UnfurledContentDto> AddContent(IContentUnfurled c) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(AddContent), OL.Start)
        from content       in M.Pure(ContentMapper.Instance.FromContentToContent(c))
        from _0            in DbTraits<M, RT>.AssignDbTraits(content)
        from addedContent  in context.AddAndSaveIO(content)
        from _1            in _l.LogInformationIO($"Added content (no track) for content with url: {c.Url}")
        from mapped        in M.Pure(ContentMapper.Instance.FromEntity(addedContent))
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(AddContent), OL.End)
        select mapped;

    private static K<M, UnfurledContentDto> AddTrackAndContent(IContentUnfurled c) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(AddTrackAndContent), OL.Start)
        from content       in M.Pure(ContentMapper.Instance.FromContentToContent(c))
        from track         in M.Pure(ContentMapper.Instance.FromContentToTrack(c))
        from _0            in DbTraits<M, RT>.AssignDbTraits(content)
        from _1            in DbTraits<M, RT>.AssignDbTraits(track)
        from addedTrack    in context.AddWithoutSaveIO(track)
        from addedContent  in context.AddWithoutSaveIO(content)
        from _3            in context.AddWithoutSaveIO(TrackContentMapping.Create(addedTrack, addedContent))
        from _4            in context.SaveChangesIO()
        from _5            in _l.LogInformationIO($"Added content and track for content with url: {c.Url}")
        from mapped        in M.Pure(ContentMapper.Instance.FromEntity(addedContent))
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(AddTrackAndContent), OL.End)
        select mapped;

    public static K<M, Unit> DeleteAll() =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(DeleteAll), OL.Start)
        from content       in IO.lift(() => context.Contents.Where(e => e.UserId == um.Current.UserId))
        from _0            in context.RemoveRangeAndSaveIO(content)
        from _1            in _l.LogInformationIO("Successfully deleted all content")
        from _5            in AuditService<M, RT>.CreateAudit(DeletedAllContent)
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(DeleteAll), OL.End)
        select unit;

    public static K<M, Unit> Delete(int id) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Delete), OL.Start)
        from _0            in guard(id >= 0, InvalidValue)
        from _c            in IO.liftAsync(() => context.Contents.FirstOrDefaultAsync(e => e.UserId == um.Current.UserId && e.Id == id))
        from _1            in guard(_c is not null, Expected.New(204, $"Content with {id} does not exist. It may already be deleted."))
        from _2            in context.RemoveAndSaveIO<Content>(_c)
        from _3            in _l.LogInformationIO("Successfully deleted content with id: {Id}", id)
        from _5            in AuditService<M, RT>.CreateAudit(string.Format(DeletedContent, _c.Id))
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Delete), OL.End)
        select unit;
}
