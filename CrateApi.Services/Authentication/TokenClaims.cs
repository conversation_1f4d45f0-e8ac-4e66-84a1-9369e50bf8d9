using System.Security.Claims;
using LanguageExt;
using static LanguageExt.Prelude;

namespace CrateApi.Services.Authentication;

public interface ITokenClaims
{
    string Subject { get; }
    string Name { get; }
    string Email { get; }
    string PreferredUsername { get; }
    Guid? ObjectId { get; }
    List<string> Roles { get; }
    DateTime? IssuedAt { get; }
    DateTime? ExpiresAt { get; }
}

public record TokenClaims : ITokenClaims
{
    public string Subject { get; init; } = string.Empty;
    public string Name { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public string PreferredUsername { get; init; } = string.Empty;
    public Guid? ObjectId { get; init; }
    public List<string> Roles { get; init; } = new();
    public DateTime? IssuedAt { get; init; }
    public DateTime? ExpiresAt { get; init; }

    public const string ClaimSubject = "sub";
    public const string ClaimName = "name";
    public const string ClaimEmail = "email";
    public const string ClaimIssuedAt = "iat";
    public const string ClaimExpiration = "exp";
    public const string ClaimRoles = "roles";
    public const string ClaimObjectId = "oid";
    public const string ClaimPreferredUsername = "preferred_username";

    private bool ClaimsValid =>
        !string.IsNullOrEmpty(Subject) && !string.IsNullOrEmpty(Name) && !string.IsNullOrEmpty(Email);

    private TokenClaims() { }

    public static Option<ITokenClaims> ExtractClaims(ClaimsPrincipal claimsPrincipal)
    {
        if (claimsPrincipal?.Identity?.IsAuthenticated != true)
        {
            return Option<ITokenClaims>.None;
        }

        var claims = new TokenClaims
        {
            Subject = GetClaimValue(claimsPrincipal, ClaimSubject),
            Name = GetClaimValue(claimsPrincipal, ClaimName),
            Email = GetClaimValue(claimsPrincipal, ClaimEmail),
            PreferredUsername = GetClaimValue(claimsPrincipal, ClaimPreferredUsername),
            ObjectId = TryParseGuid(GetClaimValue(claimsPrincipal, ClaimObjectId)),
            Roles = GetClaimValues(claimsPrincipal, ClaimRoles),
            IssuedAt = GetDateTimeFromUnixTimestamp(GetClaimValue(claimsPrincipal, ClaimIssuedAt)),
            ExpiresAt = GetDateTimeFromUnixTimestamp(GetClaimValue(claimsPrincipal, ClaimExpiration))
        };

        return claims.ClaimsValid
            ? Some(claims as ITokenClaims)
            : Option<ITokenClaims>.None;
    }

    private static string GetClaimValue(ClaimsPrincipal principal, string claimType) =>
        principal.FindFirst(claimType)?.Value ?? string.Empty;


    private static List<string> GetClaimValues(ClaimsPrincipal principal, string claimType) =>
        principal.Claims
            .Where(c => c.Type == claimType)
            .Select(c => c.Value)
            .ToList();
    private static DateTime? GetDateTimeFromUnixTimestamp(string timestamp) =>
        long.TryParse(timestamp, out long unixTime)
            ? DateTimeOffset.FromUnixTimeSeconds(unixTime).DateTime
            : null;
    private static Guid? TryParseGuid(string guidStr) =>
        Guid.TryParse(guidStr, out Guid result)
            ? result
            : null;
}
