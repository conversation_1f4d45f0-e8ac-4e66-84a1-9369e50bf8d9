using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CrateApi.Services.Authentication;

public record UserContext
{
    public Guid UserId { get; init; }
    public bool IsAuthenticated { get; init; }
    public string IpAddress { get; init; } = string.Empty;
    public string UserAgent { get; init; } = string.Empty;
    public string RequestPath { get; init; } = string.Empty;
    private UserContext() { }
    private UserContext(
        Guid userId,
        bool isAuthenticated,
        string ipAddress,
        string userAgent,
        string requestPath)
    {
        UserId = userId;
        IsAuthenticated = isAuthenticated;
        IpAddress = ipAddress;
        UserAgent = userAgent;
        RequestPath = requestPath;
    }

    public static UserContext CreateAuthenticated(Guid userId, IRequestContext rq)
    {
        if (userId == Guid.Empty) throw new ArgumentException("User ID cannot be empty", nameof(userId));

        return new UserContext(userId, true, rq.IpAddress, rq.UserAgent, rq.RequestPath);
    }
    public static UserContext CreateUnauthenticated(IRequestContext rq) =>
        new UserContext(Guid.Empty, false, rq.IpAddress, rq.UserAgent, rq.RequestPath);
}
