using CrateApi.Data;
using LanguageExt.UnsafeValueAccess;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;

namespace CrateApi.Services.Authentication;

public interface IUserManager
{
    UserContext Current { get; }
}

public class UserManager(IHttpContextAccessor HttpContextAccessor, CrateDbContext DbContext, IMemoryCache Cache) : IUserManager
{
    private UserContext? cachedContext;
    public UserContext Current => cachedContext ?? GetCurrentAsync();
    private  UserContext GetCurrentAsync()
    {
        var httpContext = HttpContextAccessor.HttpContext;
        var rq = RequestContext.FromHttpContext(httpContext);
        var user = httpContext!.User;
        if (user is null)
        {
            cachedContext = UserContext.CreateUnauthenticated(rq);
            return cachedContext;
        }

        var claims = TokenClaims.ExtractClaims(user);
        if (claims.IsNone)
        {
            cachedContext = UserContext.CreateUnauthenticated(rq);
            return cachedContext;
        }

        var claimValues = claims.ValueUnsafe()!;
        var cacheKey = $"User_{claimValues.Subject}";

        if (Cache.TryGetValue(cacheKey, out Guid userId))
        {
            cachedContext = UserContext.CreateAuthenticated(userId, rq);
            return cachedContext;
        }

        var dbUser = DbContext.Users.FirstOrDefault(u => u.EntraSubjectId == claimValues.Subject);

        if (dbUser?.Id is not null)
        {
            var usrId = dbUser.Id.Value;
            Cache.Set(cacheKey, usrId, TimeSpan.FromMinutes(5));
            cachedContext = UserContext.CreateAuthenticated(usrId, rq);
            return cachedContext;
        }

        cachedContext = UserContext.CreateUnauthenticated(rq);

        return cachedContext;
    }
}

