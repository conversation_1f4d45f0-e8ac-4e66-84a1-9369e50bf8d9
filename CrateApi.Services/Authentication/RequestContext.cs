using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace CrateApi.Services.Authentication;

public interface IRequestContext
{
    string IpAddress { get; init; }
    string UserAgent { get; init; }
    string RequestPath { get; init; }
}

public record RequestContext : IRequestContext
{
    public string IpAddress { get; init; } = string.Empty;
    public string UserAgent { get; init; } = string.Empty;
    public string RequestPath { get; init; } = string.Empty;

    private const string HeaderXForwardedFor = "X-Forwarded-For";
    private const string HeaderUserAgent = "User-Agent";
    private RequestContext() { }
    public static IRequestContext FromHttpContext(HttpContext? httpContext)
    {
        if (httpContext == null)
        {
            return new RequestContext();
        }

        return new RequestContext
        {
            IpAddress = GetIpAddress(httpContext),
            UserAgent = GetUserAgent(httpContext),
            RequestPath = GetRequestPath(httpContext)
        };
    }
    private static string GetIpAddress(HttpContext httpContext)
    {
        var forwardedFor = httpContext.Request?.Headers[HeaderXForwardedFor].ToString();

        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        return httpContext.Connection?.RemoteIpAddress?.ToString() ?? string.Empty;
    }

    private static string GetUserAgent(HttpContext httpContext) =>
        httpContext.Request?.Headers[HeaderUserAgent].ToString() ?? string.Empty;

    private static string GetRequestPath(HttpContext httpContext) =>
        httpContext.Request?.Path.Value ?? string.Empty;

}

