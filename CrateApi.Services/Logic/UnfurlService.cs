using System.Net.Mime;
using CrateApi.Common;
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Extensions;
using CrateApi.Data;
using CrateApi.Services.Authentication;
using CrateApi.Services.Database;
using CrateApi.Services.DomainTypes;
using CrateApi.Services.Logic;
using CrateApi.Services.Mappings;
using CrateApi.Services.Runtimes;
using LanguageExt;
using LanguageExt.Traits;
using LanguageExt.UnsafeValueAccess;
using Microsoft.Extensions.Logging;
using static LanguageExt.Prelude;

namespace CrateApi.Services;

public class UnfurlService<M, RT>
    where RT :
        Has<M, IHttpClientFactory>,
        Has<M, ILogger<ApiRuntime>>,
        Has<M, IUserManager>,
        Has<M, CrateDbContext>,
        Has<M, ServiceSettings>
    where M :
        Monad<M>,
        Fallible<M>
{
    private readonly DomainUnfurlUrl unfurl;
    private UnfurlService(DomainUnfurlUrl url)
    {
        unfurl = url;
    }
    private const string AuditMessage = "User Unfurled Content Id: {0}, Url: {1}, Title: {2}";
    private const string EmptyAuditMessage = "User Unfurled Empty Content";
    private static string GetAuditMessage(Option<UnfurledContentDto> content) =>
        content.Match(e => string.Format(AuditMessage, e.Id, e.Url, e.Title), () => EmptyAuditMessage);

    public K<M, Option<UnfurledContentDto>> Unfurl(UnfurlUrlRequestDto request) =>
        from um          in Has<M, RT, IUserManager>.ask
        from l0          in Has<M, RT, ILogger<ApiRuntime>>.ask
        from _0          in l0.LogInformationIO("[UNFURL] UNFURL CALLED for UserId: {User} | Url: {Url}", um.Current.UserId, unfurl.Url)
        from track       in GetContent(request)
        from response    in track.Match(Some: SaveContent, None: M.Pure(Option<UnfurledContentDto>.None))
        from _1          in l0.LogInformationIO("[UNFURL] UNFURL COMPLETED for UserId: {User} | Url: {Url}", um.Current.UserId, unfurl.Url)
        from _2          in AuditService<M, RT>.CreateAudit(GetAuditMessage(response))
        select response;

    public K<M, Option<UnfurledContentDto>> UnfurlAnonymous(UnfurlUrlRequestDto request) =>
        from l0          in Has<M, RT, ILogger<ApiRuntime>>.ask
        from _0          in l0.LogInformationIO("[UNFURL] ANONYMOUS UNFURL CALLED: {Url}", unfurl.Url)
        from track       in GetContent(request)
        from response    in MapContent(track)
        from _1          in l0.LogInformationIO("[UNFURL] ANONYMOUS UNFURL COMPLETED FOR: {Url}", unfurl.Url)
        select response;

    private K<M, Option<IContentUnfurled>> GetContent(UnfurlUrlRequestDto request) =>
        from f0          in Has<M, RT, IHttpClientFactory>.ask
        from logger      in Has<M, RT, ILogger<ApiRuntime>>.ask
        from settings    in Has<M, RT, ServiceSettings>.ask
        from serviceUrl  in M.Pure($"{settings.UnfurlServiceUrl}/api/v1/unfurl/{unfurl.Endpoint}")
        from _0          in logger.LogInformationIO("[UNFURL] ATTEMPTING UNFURL ON ENDPOINT URL: {Url}", serviceUrl)
        from track       in UnfurlClient<M, RT>.PostJson(request, unfurl.UrlType, serviceUrl)
        from _2          in logger.LogInformationIO("[UNFURL] SUCCESSFUL UNFURL ON ENDPOINT URL: {Url} Result: {Option}", serviceUrl, track)
        select track;

    private K<M, Option<UnfurledContentDto>> SaveContent(IContentUnfurled content) =>
        from logger       in Has<M, RT, ILogger<ApiRuntime>>.ask
        from _0           in logger.LogInformationIO("[UNFURL] Saving content to database... {track}", content)
        from addedContent in ContentService<M, RT>.Add(content)
        select Optional(addedContent);

    private K<M, Option<UnfurledContentDto>> MapContent(Option<IContentUnfurled> track) =>
        track.Match(
            Some: t => M.Pure(Some(ContentMapper.Instance.FromContent(t))),
            None:      M.Pure(Option<UnfurledContentDto>.None)
        );

    public static UnfurlService<M, RT> Create(DomainUnfurlUrl url) => new UnfurlService<M, RT>(url);
}

        
