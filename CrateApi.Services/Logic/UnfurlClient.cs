using System.Net;
using System.Net.Http.Json;
using CrateApi.Common;
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Extensions;
using CrateApi.Services.Runtimes;
using LanguageExt;
using LanguageExt.Common;
using LanguageExt.Traits;
using Microsoft.Extensions.Logging;
using static LanguageExt.Prelude;

namespace CrateApi.Services.Logic;

public static class UnfurlClient<M, RT>
    where RT :
        Has<M, IHttpClientFactory>,
        Has<M, ILogger<ApiRuntime>>
    where M :
        Monad<M>,
        Fallible<M>
{
    static K<M, IHttpClientFactory> clientFactory =>
        Has<M, RT, IHttpClientFactory>.ask;
    static K<M, ILogger<ApiRuntime>> logger =>
        Has<M, RT, ILogger<ApiRuntime>>.ask;

    public static K<M, Option<IContentUnfurled>> PostJson<TRequest>(TRequest request, UnfurledContentType urlType, string unfurlUrl, CancellationToken token = default) =>
        from logger        in logger
        from clientFactory in clientFactory
        from _0            in logger.LogInformationIO("[HTTP] Sending POST request to: {Url}", unfurlUrl)
        from client        in IO.lift(() => clientFactory.CreateClient())
        from response      in PostHttp(client, unfurlUrl, request, token)
        from _1            in logger.LogInformationIO("[HTTP] Received response with status: {StatusCode} from: {Url}", response.StatusCode, unfurlUrl)
        from itrack        in GetTrackData(response)
        select itrack;

    private static IO<HttpResponseMessage> PostHttp<TRequest>(HttpClient client, string requestUri, TRequest request, CancellationToken token = default) =>
        from r   in IO.liftAsync(() => client.PostAsJsonAsync(requestUri, request, token))
        from err in guard(r.IsSuccessStatusCode, () => throw new Exception($"Failed to Unfurl URL: {requestUri}"))
        select r;

    private static IO<Option<IContentUnfurled>> GetTrackData(HttpResponseMessage response) =>
        from option in response.StatusCode != HttpStatusCode.NoContent ?
            (
                from res in IO.liftAsync<IContentUnfurled?>(() => response.Content.ReadFromJsonAsync<ContentUnfurled>().ContinueWith(t => (IContentUnfurled?)t.Result))
                from _g0 in guard(res is not null, Expected.New(500, "Failed to deserialize track data"))
                select Optional(res)
            )
            : IO.lift(() => Option<IContentUnfurled>.None)
        select option;
}
