using System.Text;
using System.Text.Json;
using LanguageExt;
using LanguageExt.Traits;

namespace CrateApi.Services.Logic;

public static class DubCoLinkService<M, RT>
    where RT :
        Has<M, IHttpClientFactory>
    where M :
        Monad<M>,
        Fallible<M>
{
    static K<M, IHttpClientFactory> clientFactory =>
        Has<M, RT, IHttpClientFactory>.ask;

    private readonly static JsonSerializerOptions jsonOptions = new JsonSerializerOptions
    {
        DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
    };

    private static IO<string> SerializeContent(object requestData) =>
        IO.lift(() => JsonSerializer.Serialize(requestData, jsonOptions));

    private static IO<StringContent> CreateStringContent(string content) =>
        IO.lift(() => new StringContent(content, Encoding.UTF8, "application/json"));

    private static IO<StringContent> CreateRequestContent(string url, string domain, string slug) =>
        from _0 in SerializeContent(new { url, domain, slug })
        from _1 in CreateStringContent(_0)
        select _1;

    public static K<M, string> CreateLink(string url, string domain = "dub.sh", string slug = null) =>
        from _c in clientFactory
        from client in IO.lift(() => _c.CreateClient("dubco"))
        from content in CreateRequestContent(url, domain, slug)
        from response in IO.liftAsync(() => client.PostAsync("https://api.dub.co/links", content))
        from responseBody in IO.liftAsync(() => response.Content.ReadAsStringAsync())
        from _ in IO.lift(() => response.EnsureSuccessStatusCode())
        select responseBody;
}
