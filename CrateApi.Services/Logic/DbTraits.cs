using CrateApi.Data.Models.Interfaces;
using CrateApi.Services.Authentication;
using LanguageExt;
using LanguageExt.Traits;
using static LanguageExt.Prelude;
namespace CrateApi.Services.Logic;

public static class DbTraits<M, RT>
    where RT :
        Has<M, IUserManager>
    where M :
        Monad<M>
{
    public static K<M, T> AssignDbTraits<T>(T entity) where T : class =>
        from um in Has<M, RT, IUserManager>.ask
        from _0 in (entity is ICreatedDate createdDate) ? ICreatedDate.AssignCreatedNow(createdDate)        : unitIO
        from _1 in (entity is IUpdatedDate updatedDate) ? IUpdatedDate.AssignUpdatedNow(updatedDate)        : unitIO
        from _2 in (entity is IUserIdentifier uid)      ? IUserIdentifier.AssignUid(uid, um.Current.UserId) : unitIO
        from _3 in (entity is ILastLogin lastLogin)     ? ILastLogin.AssignUpdatedNow(lastLogin)            : unitIO
        select entity;

    public static K<M, Unit> AssignDbTraitsList<T>(IEnumerable<T> entities) where T : class =>
        from um in Has<M, RT, IUserManager>.ask
        from _5 in entities.AsIterable().Traverse(AssignDbTraits)
        select unit;

   public static K<M, T> UpdateDbTraits<T>(T entity) where T : class =>
        from um in Has<M, RT, IUserManager>.ask
        from _1 in (entity is IUpdatedDate updatedDate) ? IUpdatedDate.AssignUpdatedNow(updatedDate)        : unitIO
        from _2 in (entity is ILastLogin lastLogin)     ? ILastLogin.AssignUpdatedNow(lastLogin)            : unitIO
        select entity;

   public static K<M, Unit> UpdateDbTraitsList<T>(IEnumerable<T> entities) where T : class =>
        from um in Has<M, RT, IUserManager>.ask
        from _5 in entities.AsIterable().Traverse(UpdateDbTraits)
        select unit;
}
