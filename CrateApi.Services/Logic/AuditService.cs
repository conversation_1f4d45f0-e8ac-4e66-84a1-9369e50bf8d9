using CrateApi.Common.Extensions;
using CrateApi.Data;
using CrateApi.Data.Context;
using CrateApi.Data.Models;
using CrateApi.Services.Authentication;
using CrateApi.Services.Runtimes;
using LanguageExt;
using LanguageExt.Traits;
using Microsoft.Extensions.Logging;
using static LanguageExt.Prelude;

namespace CrateApi.Services.Logic;

public static class AuditService<M, RT>
    where RT :
        Has<M, ILogger<ApiRuntime>>,
        Has<M, IUserManager>,
        Has<M, CrateDbContext>
    where M :
        Monad<M>,
        Fallible<M>
{
    public static K<M, Unit> CreateAudit(string message) =>
        from um          in Has<M, RT, IUserManager>.ask
        from context     in Has<M, RT, CrateDbContext>.ask
        from logger      in Has<M, RT, ILogger<ApiRuntime>>.ask
        from _0          in logger.LogInformationIO("[AUDIT] Creating Audit for UserId: {User}", um.Current.UserId)
        from _1          in context.AddAndSaveIO(AuditLog.Create(message, um.Current.UserId, um.Current.IpAddress, um.Current.UserAgent, um.Current.RequestPath))
        from _2          in logger.LogInformationIO("[AUDIT] Created Audit for UserId: {User}", um.Current.UserId)
        select unit;
}
