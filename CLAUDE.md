# CrateNFCAPI Project Guide

## Project Overview

CrateNFCAPI is a music sharing system backend that allows users to manage and share music tracks through NFC cards. It serves as the backend for a mobile app that enables users to scan NFC cards to access music collections.

## System Architecture

The system is split into two main components:

1. **CrateApi**: Main API service for mobile clients handling user authentication, track management, collections, and NFC cards
2. **UnfurlApi**: Specialized service for scraping music service metadata, maintaining separate concerns for better maintainability

## Key Features

- Track unfurling (extracting metadata from various music service URLs)
- Track collections management
- NFC card registration and management
- User management and authentication
- Cross-referencing between music services to get additional metadata

## Supported Music Services

- Spotify
- Apple Music
- YouTube Music (recently added)
- Generic web pages (fallback for unsupported services)

## Code Conventions

### Architecture Patterns

- Multi-layered architecture with controllers, services, and data layers
- Entity Framework Core for database operations
- LanguageExt for functional programming patterns

### Coding Conventions

- **Primary Constructors**: Use C# primary constructors for dependency injection
- **Field Naming**: DO NOT prefix private fields with underscores (`_`)
- **LanguageExt**: Utilize LanguageExt patterns for better functional programming style
- **DTOs**: Separate request and response DTOs in the Common project
- **Error Handling**: Use Exception handlers and TryAsync pattern for error handling

## Project Structure

- **CrateApi.Common**: Shared models, DTOs, and utilities
- **CrateApi.Data**: Entity Framework models and database context
- **CrateApi.Services**: Business logic and service implementations
- **CrateApi**: Main API controllers and endpoint definitions
- **UnfurlApi.Services**: Music service scrapers and platform-specific clients
- **UnfurlApi**: API for music metadata extraction

## Key Implementation Patterns

### Track Unfurling Process

1. Client sends URL to CrateApi
2. CrateApi determines service type (Spotify, Apple Music, YouTube Music, etc.)
3. CrateApi forwards request to UnfurlApi with appropriate endpoint
4. UnfurlApi scrapes metadata and returns structured track data
5. CrateApi saves track (if authenticated) and optionally creates a shortened link via dub.co

### Cross-Referencing for ISRC

For services that don't directly provide ISRC codes (like YouTube Music):

1. Extract basic track metadata (title, artist)
2. Use Spotify service to search for matching track
3. When a match is found, extract ISRC from Spotify data
4. Return combined metadata with the retrieved ISRC

## Enhancements & Future Work

- Support for more music services
- Improved match accuracy for cross-referencing
- Caching for better performance
- Additional track metadata

## Useful Development Commands

For future development tasks, use these commands:

```bash
# Build the project
dotnet build

# Run the API locally 
dotnet run --project CrateApi/CrateApi.csproj

# Run the unfurl service locally
dotnet run --project UnfurlApi/UnfurlApi.csproj

# Entity Framework migrations
dotnet ef migrations add <MigrationName> --project CrateApi.Data/CrateApi.Data.csproj --startup-project CrateApi/CrateApi.csproj
dotnet ef database update --project CrateApi.Data/CrateApi.Data.csproj --startup-project CrateApi/CrateApi.csproj
```
