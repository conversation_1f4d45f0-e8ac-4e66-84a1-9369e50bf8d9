using Microsoft.OpenApi.Models;

namespace UnfurlApi.Infrastructure;

public static class SwaggerExtensions
{
    public static IServiceCollection ConfigureSwaggerExt(this IServiceCollection services)
    {
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "CrateApi", Version = "v1" });
            c.CustomSchemaIds(type => $"{type.ToString()}");
        });
        return services;
    }
}
