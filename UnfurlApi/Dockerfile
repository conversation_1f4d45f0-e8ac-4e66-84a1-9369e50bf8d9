# UnfurlApi/Dockerfile

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy application source files
COPY . .

# Downloads and installs packages dependencies
RUN dotnet restore "UnfurlApi/UnfurlApi.csproj"

# Compile app
WORKDIR /src/UnfurlApi
RUN dotnet publish -c Release -o /app/publish

# Multi-stage build for minimal size and dependencies
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS runtime

WORKDIR /app

# Copy the published application from the build stage
COPY --from=build /app/publish .

ENTRYPOINT ["dotnet", "UnfurlApi.dll"]
