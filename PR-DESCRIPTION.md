# Add User Profile and Delete Endpoints with Entra External ID Integration

## Overview
This PR implements user profile management endpoints that integrate with Entra External ID authentication. It adds functionality to automatically create or retrieve user profiles when authenticated, and allows users to delete their profiles when needed.

## Changes

### Database Changes
- Added `EntraSubjectId` field to the `User` model to link users to their Entra External ID identity
- Added `LastLogin`, `Created`, and `Updated` timestamp fields for better user tracking
- Created and applied a database migration (`AddEntraSubjectIdToUser`)

### New Functionality
- Implemented a `/user/profile` endpoint that:
  - Requires authentication with a valid JWT token
  - Extracts the subject claim (`sub`) from the token
  - Looks up the user in the database by their Entra subject ID
  - Creates a new user if one doesn't exist with information from token claims
  - Updates the last login time for existing users
  - Returns the user profile information

- Implemented a `/user/{UserId}` DELETE endpoint that:
  - Requires authentication with a valid JWT token
  - Verifies the authenticated user is deleting their own profile
  - Removes the user from the database
  - Returns a 204 No Content response on success

### Implementation Details
- Used functional programming approach with monadic patterns following codebase conventions
- Implemented `UserService` with pure functions for database operations
- Added proper error handling and logging throughout the flow
- Created test scripts for validating the endpoint functionality

## Why This Approach
We chose to implement a "create-or-return" pattern for user profiles because:

1. It simplifies the authentication flow by automatically creating users on first login
2. It maintains a consistent user experience by linking Entra identities to our database
3. It follows the functional programming patterns established in the codebase
4. It provides a clean API for clients to retrieve user information after authentication

The delete endpoint follows security best practices by:
1. Only allowing users to delete their own profiles
2. Requiring authentication with a valid JWT token
3. Verifying the subject claim matches the user being deleted

## Testing
The implementation has been tested with real Entra External ID tokens to verify:
- New user creation on first authentication
- Existing user retrieval on subsequent authentications
- Proper extraction and storage of user information from token claims
- Correct error handling for invalid tokens
- User deletion with proper authorization checks

## Next Steps
- Consider adding additional profile fields as needed
- Implement profile update functionality
- Add admin endpoints for user management
