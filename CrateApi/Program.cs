using CrateApi;
using CrateApi.Infrastructure;
using FastEndpoints;
using FastEndpoints.Swagger;
using Microsoft.AspNetCore.Diagnostics;

_ = ApplicationStartTimeProvider.StartTime;

var builder = WebApplication.CreateBuilder(args);

<PERSON><PERSON>Configure(builder);

var app = builder.Build();

var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("CrateApi is starting up...");
logger.LogInformation("Environment: {Environment}", app.Environment.EnvironmentName);
app.UseHttpsRedirection();
app.UseRouting();
app.UseCors();
app.UseExceptionHandler(errorApp => 
{
    errorApp.Run(async context => 
    {
        var loggerFactory = context.RequestServices.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger("GlobalExceptionHandler");
        context.Response.ContentType = "application/json";
        var exceptionHandlerFeature = context.Features.Get<IExceptionHandlerFeature>();
        var exception = exceptionHandlerFeature?.Error;
        await AppExceptionHandler.HandleEx(exception!, logger, context);
    });
});
app.UseAuthentication();
app.UseAuthorization();
app.UseFastEndpoints(options => {
    options.Endpoints.RoutePrefix = "api/v1";
});
app.UseSwaggerGen();

logger.LogInformation("CrateApi is ready to accept requests");
await app.RunAsync();
