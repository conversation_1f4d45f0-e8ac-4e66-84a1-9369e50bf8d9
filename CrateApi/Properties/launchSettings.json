{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:57457", "sslPort": 44363}}, "profiles": {"https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "http://localhost:8000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Logging__LogLevel__Default": "Information", "Logging__LogLevel__AuthenticationExtensions": "Debug"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}