#!/bin/sh
set -e

# Check required environment variables
: "${POSTGRES_HOST:?POSTGRES_HOST is not set}"
: "${POSTGRES_USERNAME:?POSTGRES_USERNAME is not set}"
: "${POSTGRES_PASSWORD:?POSTGRES_PASSWORD is not set}"

echo "✅ All required environment variables are set"

echo "Setting up connection string to CrateDB Host ${POSTGRES_HOST} with username ${POSTGRES_USERNAME}"
export ConnectionStrings__DefaultConnection="Host=${POSTGRES_HOST};Database=cratedb;Username=${POSTGRES_USERNAME};Password=${POSTGRES_PASSWORD};SslMode=${SSLMODE:-Disable}"

echo "Running Migrations"
./efbundle --connection "${ConnectionStrings__DefaultConnection}"

echo "Starting CrateApi"
dotnet CrateApi.dll
