using System.IdentityModel.Tokens.Jwt;
using CrateApi.Infrastructure;
using FastEndpoints;
using Microsoft.Extensions.Logging;

namespace CrateApi.Routes.UserEndpoints;

public class DecodeToken(ILogger<DecodeToken> logger) : EndpointWithoutRequest
{
    public override void Configure()
    {
        Get("user/decode");
        AuthSchemes("Bearer");
        PreProcessor<ApiKeyRequirement<EmptyRequest>>();
        Description(d => d
            .WithTags("User")
            .WithSummary("Decode the JWT access token")
            .WithDescription("Returns the decoded claims from the JWT access token in the Authorization header"));
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        // Get authorization header
        var authHeader = HttpContext.Request.Headers["Authorization"].FirstOrDefault();
        logger.LogInformation($"Authorization header: {(authHeader != null ? "present" : "missing")}");

        if (string.IsNullOrEmpty(authHeader))
        {
            logger.LogWarning("Authorization header is missing");
            await SendAsync(new { error = "Authorization header is missing" }, 400);
            return;
        }

        if (!authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
        {
            logger.LogWarning("Authorization header does not start with 'Bearer '");
            await SendAsync(new { error = "Invalid Authorization header format" }, 400);
            return;
        }

        var token = authHeader.Substring("Bearer ".Length).Trim();
        if (string.IsNullOrEmpty(token))
        {
            logger.LogWarning("Token is empty");
            await SendAsync(new { error = "Token is empty" }, 400);
            return;
        }

        try
        {
            // Decode the token
            var decodedToken = DecodeJwtToken(token);
            
            // Return the decoded token
            await SendOkAsync(decodedToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error decoding token");
            await SendAsync(new { error = $"Error decoding token: {ex.Message}" }, 400);
        }
    }

    // Decode JWT token and return claims as a dictionary
    private object DecodeJwtToken(string token)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();
            
            if (!handler.CanReadToken(token))
            {
                logger.LogWarning("Token is not in a valid JWT format");
                throw new Exception("Token is not in a valid JWT format");
            }
            
            var jwtToken = handler.ReadJwtToken(token);
            
            // Create a dictionary to hold the claims and token metadata
            var result = new Dictionary<string, object>
            {
                ["token_type"] = "Bearer",
                ["is_valid"] = true,
                ["header"] = jwtToken.Header.ToDictionary(h => h.Key, h => h.Value),
                ["issued_at"] = ToUnixTimeSeconds(jwtToken.IssuedAt),
                ["expires_at"] = ToUnixTimeSeconds(jwtToken.ValidTo),
                ["issuer"] = jwtToken.Issuer,
                ["audience"] = jwtToken.Audiences.FirstOrDefault() ?? string.Empty
            };
            
            // Create a dictionary to hold the claims
            var claimsDict = new Dictionary<string, object>();
            
            // Add all claims to the dictionary
            foreach (var claim in jwtToken.Claims)
            {
                // Special handling for roles claim (convert to array)
                if (claim.Type == "roles")
                {
                    if (!claimsDict.ContainsKey(claim.Type))
                    {
                        var rolesList = jwtToken.Claims
                            .Where(c => c.Type == "roles")
                            .Select(c => c.Value)
                            .ToArray();
                        claimsDict[claim.Type] = rolesList;
                    }
                }
                // For all other claims, just add them to the dictionary
                else
                {
                    claimsDict[claim.Type] = claim.Value;
                }
            }
            
            // Add claims to the result
            result["claims"] = claimsDict;
            
            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error decoding JWT token");
            throw;
        }
    }
    
    // Helper method to convert DateTime to Unix timestamp (seconds since epoch)
    private static long ToUnixTimeSeconds(DateTime dateTime)
    {
        var epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        return (long)(dateTime.ToUniversalTime() - epoch).TotalSeconds;
    }
}
