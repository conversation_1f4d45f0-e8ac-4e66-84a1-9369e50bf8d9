using CrateApi.Common.Dto.Response;
using CrateApi.Services.Runtimes;
using FastEndpoints;
using LanguageExt;
using LanguageExt.Common;
using static LanguageExt.Prelude;
using Users = CrateApi.Services.Database.UserService<LanguageExt.Eff<CrateApi.Services.Runtimes.ApiRuntime>, CrateApi.Services.Runtimes.ApiRuntime>;

namespace CrateApi.Routes.UserEndpoints;

public sealed class DeleteUserRequest
{
    public Guid UserId { get; set; }
}

public sealed class DeleteUser(ApiRuntime runtime) : Endpoint<DeleteUserRequest>
{
    public override void Configure()
    {
        Delete("user/{UserId}");
        AuthSchemes("Bearer");
        Description(d => d
            .WithTags("User")
            .Produces(204)
            .Produces(401)
            .Produces(403)
            .Produces(404)
            .WithSummary("Delete user profile")
            .WithDescription("Deletes a user profile. Users can only delete their own profiles."));
    }

    public override async Task HandleAsync(DeleteUserRequest req, CancellationToken ct)
    {
        var result = await Users.DeleteUserById(req.UserId, User).RunAsync(runtime);
        
        await result.Match(
            Succ: _ => SendNoContentAsync(ct),
            Fail: error => SendErrorsAsync(error.Code, ct)
        );
    }
}
