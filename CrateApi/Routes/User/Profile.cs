using CrateApi.Common.Dto.Response;
using CrateApi.Services.Runtimes;
using FastEndpoints;
using LanguageExt;
using LanguageExt.Common;
using static LanguageExt.Prelude;
using Users = CrateApi.Services.Database.UserService<LanguageExt.Eff<CrateApi.Services.Runtimes.ApiRuntime>, CrateApi.Services.Runtimes.ApiRuntime>;

namespace CrateApi.Routes.UserEndpoints;

public sealed class Profile(ApiRuntime runtime) : EndpointWithoutRequest<UserProfileResponseDto>
{
    public override void Configure()
    {
        Get("user/profile");
        AuthSchemes("Bearer");
        Description(d => d
            .WithTags("User")
            .Produces<UserProfileResponseDto>(200)
            .Produces(401)
            .WithSummary("Get or create user profile")
            .WithDescription("Retrieves the user profile for the authenticated user or creates one if it doesn't exist"));
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var result = await Users.GetOrCreateUserProfile(User).RunAsync(runtime);

        await result.Match(
            Succ: profile => SendOkAsync(profile),
            Fail: error => SendErrorsAsync(error.Code)
        );
    }
}
