using System.IdentityModel.Tokens.Jwt;
using System.Text.Json;
using CrateApi.Infrastructure;
using CrateApi.Models;
using FastEndpoints;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;

namespace CrateApi.Routes.UserEndpoints;

public class Login(IConfiguration configuration, ILogger<Login> logger) : Endpoint<LoginRequest>
{
    public override void Configure()
    {
        Post("user/login");
        AllowAnonymous();
        PreProcessor<ApiKeyRequirement<LoginRequest>>();
        Description(d => d
            .WithTags("User")
            .Produces<LoginResponse>(200)
            .WithSummary("Login with username and password")
            .WithDescription("Authenticates a user with Entra External ID using username and password and returns a token response"));
    }

    public override async Task HandleAsync(LoginRequest req, CancellationToken ct)
    {
        try
        {
            // Get Entra External ID configuration
            var entraSettings = configuration.GetSection("EntraExternalId").Get<EntraExternalIdSettings>() ?? throw new InvalidOperationException("Entra External ID settings are not configured");
            var scopes = new[] { "api://cratenfc/access" }; // Scopes from the Python script

            // Construct the authority URL
            var tenantSubdomain = entraSettings.Instance.Replace("https://", "").Split('.')[0]; // Extract subdomain from instance URL
            var authority = $"{entraSettings.Instance}/{tenantSubdomain}.onmicrosoft.com";

            // Create MSAL public client application
            var app = PublicClientApplicationBuilder
                .Create(entraSettings.ClientId)
                .WithAuthority(authority)
                .Build();

            logger.LogInformation($"Authenticating user: {req.Email}");

            // Acquire token using username/password
            var result = await app.AcquireTokenByUsernamePassword(
                scopes,
                req.Email, // Using Email as the username
                req.Password
            ).ExecuteAsync(ct);

            if (result == null)
            {
                logger.LogWarning("Authentication failed: No result returned");
                await SendUnauthorizedAsync();
                return;
            }

            logger.LogInformation("Authentication successful");
            logger.LogInformation($"Access token: {result.AccessToken.Substring(0, 20)}...");

            // Calculate expiration time in seconds
            var expiresIn = (int)(result.ExpiresOn - DateTimeOffset.UtcNow).TotalSeconds;

            // Parse the ID token to get the claims
            var idTokenClaims = ParseIdToken(result.IdToken);

            // Get client_info if available
            string? clientInfo = null;
            if (result.ClaimsPrincipal?.Claims != null)
            {
                clientInfo = result.ClaimsPrincipal.Claims
                    .FirstOrDefault(c => c.Type == "client_info")?.Value;
            }

            // Create a response using the LoginResponse record
            var response = new LoginResponse(
                access_token: result.AccessToken,
                id_token: result.IdToken,
                token_type: result.TokenType,
                expires_in: expiresIn,
                ext_expires_in: expiresIn,
                scope: string.Join(" ", result.Scopes),
                id_token_claims: idTokenClaims,
                token_source: "identity_provider",
                client_info: clientInfo
            );

            // Return the response that matches the Entra External ID format
            await SendOkAsync(response);
        }
        catch (MsalException ex)
        {
            logger.LogError(ex, "MSAL authentication error");
            await SendAsync(new { error = ex.Message }, 401);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during authentication");
            await SendAsync(new { error = "An error occurred during authentication" }, 500);
        }
    }

    // Parse the ID token and return its claims as a dictionary
    private object ParseIdToken(string idToken)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();
            var token = handler.ReadJwtToken(idToken);

            // Create a dictionary to hold the claims
            var claimsDict = new Dictionary<string, object>();

            // Add all claims to the dictionary
            foreach (var claim in token.Claims)
            {
                // Special handling for roles claim (convert to array)
                if (claim.Type == "roles")
                {
                    if (!claimsDict.ContainsKey(claim.Type))
                    {
                        var rolesList = token.Claims
                            .Where(c => c.Type == "roles")
                            .Select(c => c.Value)
                            .ToArray();
                        claimsDict[claim.Type] = rolesList;
                    }
                }
                // For all other claims, just add them to the dictionary
                else
                {
                    claimsDict[claim.Type] = claim.Value;
                }
            }

            // Add standard JWT properties
            if (token.ValidFrom != DateTime.MinValue)
                claimsDict["nbf"] = ToUnixTimeSeconds(token.ValidFrom);

            if (token.ValidTo != DateTime.MinValue)
                claimsDict["exp"] = ToUnixTimeSeconds(token.ValidTo);

            if (token.IssuedAt != DateTime.MinValue)
                claimsDict["iat"] = ToUnixTimeSeconds(token.IssuedAt);

            if (!string.IsNullOrEmpty(token.Issuer))
                claimsDict["iss"] = token.Issuer;

            if (token.Audiences.Any())
                claimsDict["aud"] = token.Audiences.First();

            return claimsDict;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error parsing ID token");
            return new Dictionary<string, string>();
        }
    }

    // Helper method to convert DateTime to Unix timestamp (seconds since epoch)
    private static long ToUnixTimeSeconds(DateTime dateTime)
    {
        var epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        return (long)(dateTime.ToUniversalTime() - epoch).TotalSeconds;
    }
}
