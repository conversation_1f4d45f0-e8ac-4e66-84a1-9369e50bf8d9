using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Infrastructure;
using CrateApi.Models;
using CrateApi.Services.Runtimes;
using FastEndpoints;
using LanguageExt;
using static LanguageExt.Prelude;
using Collections = CrateApi.Services.Database.CollectionService<LanguageExt.Eff<CrateApi.Services.Runtimes.ApiRuntime>, CrateApi.Services.Runtimes.ApiRuntime>;
namespace CrateApi.Routes.CollectionEndpoints;

public sealed class Add(ApiRuntime runtime) : Endpoint<AddCollectionRequestDto>
{
    public override void Configure()
    {
        Post("collection");
        AuthSchemes("Bearer");
        Description(d => d
            .WithTags("Collection")
            .Produces<CollectionResponseDto>(200)
            .WithSummary("Add Collection")
            .WithDescription("""
                Add a new collection
             """
        ));
    }

    public override async Task HandleAsync(AddCollectionRequestDto req, CancellationToken ct)
    {
        var result = await Collections.Add(req).RunAsync(runtime);
        await result.Match(
            succ  => SendOkAsync(succ),
            error => SendAsync(new ErrorResponseDto(error.Message), error.Code)
        );
    }
}
