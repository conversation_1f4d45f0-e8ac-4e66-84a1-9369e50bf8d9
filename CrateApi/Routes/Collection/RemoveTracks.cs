using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Infrastructure;
using CrateApi.Models;
using CrateApi.Services.Runtimes;
using FastEndpoints;
using LanguageExt;
using static LanguageExt.Prelude;
using Collections = CrateApi.Services.Database.CollectionService<LanguageExt.Eff<CrateApi.Services.Runtimes.ApiRuntime>, CrateApi.Services.Runtimes.ApiRuntime>;
namespace CrateApi.Routes.CollectionEndpoints;

public sealed class RemoveTracks(ApiRuntime runtime) : Endpoint<List<int>>
{
    public override void Configure()
    {
        Post("collection/{id}/remove");
        AuthSchemes("Bearer");
        Description(d => d
            .WithTags("Collection")
            .Produces<CollectionResponseDto>(200)
            .WithSummary("Remove tracks")
            .WithDescription("""
                Removes tracks from collection by id 
             """
        ));
    }

    public override async Task HandleAsync(List<int> req, CancellationToken ct)
    {
        var id = Route<int>("id");
        var result = await Collections.RemoveContentFromCollection(id, req).RunAsync(runtime);
        await result.Match(
            succ  => SendOkAsync(succ),
            error => SendAsync(new ErrorResponseDto(error.Message), error.Code)
        );
    }
}
