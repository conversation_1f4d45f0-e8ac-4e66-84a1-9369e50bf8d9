using CrateApi.Common.Dto.Response;
using CrateApi.Models;
using CrateApi.Services.Runtimes;
using FastEndpoints;
using LanguageExt;
using Contents = CrateApi.Services.Database.ContentService<LanguageExt.Eff<CrateApi.Services.Runtimes.ApiRuntime>, CrateApi.Services.Runtimes.ApiRuntime>;
namespace CrateApi.Routes.Content;

public sealed class Trending(ApiRuntime runtime) : Endpoint<PaginationQuery>
{
    public override void Configure()
    {
        Get("content/trending");
        AllowAnonymous();
        Description(d => d
            .WithTags("Content")
            .Produces<List<UnfurledContentDto>>(200)
            .WithSummary("Return the trending content sorted by last updated date")
            .WithDescription("""
                Returns the trending content sorted by last updated.
             """
        ));
    }

    public override async Task HandleAsync(PaginationQuery req, CancellationToken ct)
    {
        var result = await Contents.Trending(req.Start, req.Size).RunAsync(runtime);

        await result.Match(
            succ  => SendOkAsync(succ),
            error => SendAsync(new ErrorResponseDto(error.Message), error.Code)
        );
    }
}
