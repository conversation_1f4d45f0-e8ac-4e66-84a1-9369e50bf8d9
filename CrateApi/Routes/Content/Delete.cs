using CrateApi.Common.Dto.Response;
using CrateApi.Services.Runtimes;
using FastEndpoints;
using LanguageExt;
using static LanguageExt.Prelude;
using Contents = CrateApi.Services.Database.ContentService<LanguageExt.Eff<CrateApi.Services.Runtimes.ApiRuntime>, CrateApi.Services.Runtimes.ApiRuntime>;

namespace CrateApi.Routes.ContentEndpoints;

public sealed class Delete(ApiRuntime runtime) : EndpointWithoutRequest
{
    public override void Configure()
    {
        Delete("content/{id}");
        AuthSchemes("Bearer");
        Description(d => d
            .WithTags("Content")
            .Produces(204)
            .WithSummary("Deletes a content item")
            .WithDescription("""
                Deletes a content item by id for the user, from the crate.Contents table
             """
        ));
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var id = Route<int>("id");
        var result = await Contents.Delete(id).RunAsync(runtime);
        await result.Match(
            succ  => SendNoContentAsync(),
            error => SendAsync(new ErrorResponseDto(error.Message), error.Code)
        );
    }
}
