using CrateApi.Common.Dto.Response;
using CrateApi.Services.Runtimes;
using FastEndpoints;
using LanguageExt;
using static LanguageExt.Prelude;
using Contents = CrateApi.Services.Database.ContentService<LanguageExt.Eff<CrateApi.Services.Runtimes.ApiRuntime>, CrateApi.Services.Runtimes.ApiRuntime>;

namespace CrateApi.Routes.ContentEndpoints;

public sealed class DeleteAll(ApiRuntime runtime) : EndpointWithoutRequest
{
    public override void Configure()
    {
        Delete("content");
        AuthSchemes("Bearer");
        Description(d => d
            .WithTags("Content")
            .Produces(204)
            .WithSummary("Deletes all content")
            .WithDescription("""
                Deletes all content for the user
             """
        ));
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var result = await Contents.DeleteAll().RunAsync(runtime);
        await result.Match(
            succ  => SendNoContentAsync(),
            error => SendAsync(new ErrorResponseDto(error.Message), error.Code)
        );
    }
}
