namespace CrateApi.Models;

public record GetChallengeRequest
{
    public required string Uid { get; set; }
    public required string Atr { get; set; }
}

public record ChallengeResponse
{
    public required string Challenge { get; set; }
}

public record VerifyChallengeRequest
{
    public required string Uid { get; set; }
    public required string Atr { get; set; }
    public required string Challenge { get; set; }
    public required string Response { get; set; }
}

public record HashTestRequest
{
    public required string Uid { get; set; }
    public required string Atr { get; set; }
    public required string Challenge { get; set; }
}
