namespace CrateApi.Models;

public record LoginRequest(string Email, string Password);
public record UserModel(string UserId, string Email, string Username, string Token);
public record LoginResponse(
    string access_token,
    string id_token,
    string token_type,
    int expires_in,
    int ext_expires_in,
    string scope,
    object id_token_claims,
    string token_source = "identity_provider",
    string? client_info = null
);

// Response model for the user profile endpoint
public record UserProfileResponse(
    string UserId,
    string Username,
    string Email,
    string EntraSubjectId,
    DateTime Created,
    DateTime? LastLogin,
    DateTime Updated
);
