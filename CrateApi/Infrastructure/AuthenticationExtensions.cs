using System.IdentityModel.Tokens.Jwt;
using CrateApi.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;

namespace CrateApi.Infrastructure;

public static class AuthenticationExtensions
{
    public static IServiceCollection ConfigureAuthenticationExt(this IServiceCollection services, IConfiguration config, ILogger? logger = null)
    {
        // Enable PII logging for debugging authentication issues
        IdentityModelEventSource.ShowPII = true;

        // Use provided logger or create a null logger if none provided
        logger ??= NullLogger.Instance;

        // Get and log the Entra External ID configuration
        var entraSettings = config.GetSection("EntraExternalId").Get<EntraExternalIdSettings>() ?? throw new InvalidOperationException("Entra External ID settings are not configured");

        logger.LogInformation("Entra External ID Configuration:");
        logger.LogInformation("TenantId: {TenantId}", entraSettings.TenantId ?? "null");
        logger.LogInformation("Audience: {Audience}", entraSettings.Audience ?? "null");
        logger.LogInformation("Instance: {Instance}", entraSettings.Instance ?? "null");
        var metadataAddress = $"{entraSettings.Instance}/{entraSettings.TenantId}/v2.0/.well-known/openid-configuration";

        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            options.MetadataAddress = metadataAddress;
            options.Audience = entraSettings.Audience;
            options.IncludeErrorDetails = true;
            options.MapInboundClaims = false;
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                // Accept multiple possible issuers
                ValidIssuers = new[]
                {
                    $"https://sts.windows.net/{entraSettings.TenantId}/",
                    $"https://login.microsoftonline.com/{entraSettings.TenantId}/",
                    $"https://login.microsoft.com/{entraSettings.TenantId}/",
                    $"https://sts.windows.net/{entraSettings.TenantId}/v2.0",
                    $"https://login.microsoftonline.com/{entraSettings.TenantId}/v2.0",
                    $"https://login.microsoft.com/{entraSettings.TenantId}/v2.0"
                },
                ValidateAudience = true,
                // Accept both the configured audience and Microsoft Graph audience
                ValidAudiences = new[]
                {
                    entraSettings.Audience, // CrateNFC API Access
                    "00000003-0000-0000-c000-000000000000", // Microsoft Graph
                    entraSettings.ClientId, // CrateNFC Login
                },
                ValidateIssuerSigningKey = true,
                ValidateLifetime = true
            };

            options.Events = new JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    logger.LogWarning("Entra Authentication failed: {Message}", context.Exception.Message);

                    // Log more details about the token
                    if (context.Request.Headers.TryGetValue("Authorization", out var authHeader))
                    {
                        if (authHeader.ToString().StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
                        {
                            var token = authHeader.ToString().Substring("Bearer ".Length).Trim();
                            try
                            {
                                var handler = new JwtSecurityTokenHandler();
                                if (handler.CanReadToken(token))
                                {
                                    var jwtToken = handler.ReadJwtToken(token);
                                    logger.LogDebug("Token issuer: {Issuer}", jwtToken.Issuer);
                                    logger.LogDebug("Token audience: {Audiences}", string.Join(", ", jwtToken.Audiences));
                                    logger.LogDebug("Token valid from: {ValidFrom}", jwtToken.ValidFrom);
                                    logger.LogDebug("Token valid to: {ValidTo}", jwtToken.ValidTo);
                                    logger.LogDebug("Token kid: {Kid}", jwtToken.Header.Kid);
                                }
                            }
                            catch (Exception ex)
                            {
                                logger.LogError(ex, "Error parsing token: {Message}", ex.Message);
                            }
                        }
                    }

                    return Task.CompletedTask;
                },
                OnTokenValidated = context =>
                {
                    logger.LogInformation("Entra token validated");
                    var claims = context.Principal?.Claims.Select(c => $"{c.Type}: {c.Value}");
                    logger.LogDebug("Claims: {Claims}", string.Join(", ", claims ?? Array.Empty<string>()));
                    return Task.CompletedTask;
                },
                OnMessageReceived = context =>
                {
                    // Check if Authorization header exists and extract token
                    var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
                    logger.LogDebug("Authorization header: {AuthHeader}", authHeader ?? "null");

                    if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
                    {
                        var token = authHeader.Substring("Bearer ".Length).Trim();
                        // Manually set the token if it's not already set
                        if (string.IsNullOrEmpty(context.Token))
                        {
                            context.Token = token;
                        }
                    }

                    logger.LogDebug("Entra token received: {Token}", context.Token ?? "null");
                    return Task.CompletedTask;
                },
            };
        });

        services.AddAuthorization();
        return services;
    }
}
