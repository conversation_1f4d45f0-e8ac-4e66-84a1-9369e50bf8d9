
namespace CrateApi.Infrastructure;

public class HeartbeatService(ILogger<HeartbeatService> logger) : BackgroundService
{
    public readonly TimeSpan interval = TimeSpan.FromMinutes(5);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            logger.LogInformation("App Alive At: {Time}", DateTimeOffset.Now);
            await Task.Delay(interval);
        }
    }
}
