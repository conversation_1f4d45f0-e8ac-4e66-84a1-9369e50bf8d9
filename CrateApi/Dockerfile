# CrateApi/Dockerfile

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Install EF Core CLI tools
RUN dotnet tool install --global dotnet-ef
ENV PATH="${PATH}:/root/.dotnet/tools"

# Copy application source files
COPY . .

# Downloads and installs packages dependencies
RUN dotnet restore CrateApi/CrateApi.csproj

# Compile App
WORKDIR /src/CrateApi
RUN dotnet publish -c Release -o /app/publish

# Build and publish the migration bundle
RUN dotnet ef migrations bundle -o /app/publish/efbundle

# Multi-stage build for minimal size and dependencies
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app
COPY --from=build /app/publish .

# Copy custom entrypoint script
COPY ./CrateApi/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]
