using CrateApi.Common.Dto.Response;
using LanguageExt.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace CrateApi.Common.Exceptions;

public static class ExceptionResult
{
    private static IActionResult UnprocessableEntity(string message, Exception ex, ILogger logger)
    {
        logger.LogWarning(ex, "Returning unprocessable entity: {Message}", message);

        return new UnprocessableEntityObjectResult(new ErrorResponseDto(message));
    }
    private static IActionResult RequestTimeout(TaskCanceledException ex, ILogger logger)
    {
        logger.LogDebug(ex, "Request timed out or was cancelled");

        return new StatusCodeResult(StatusCodes.Status408RequestTimeout);
    }
    private static IActionResult InternalServerError(Exception ex, ILogger logger)
    {
        logger.LogError(ex, "Returning Internal Server error {Message}", ex.Message);

        return new ObjectResult(new ErrorResponseDto("Internal server error"))
        {
            StatusCode = 500
        };
    }

    private static IActionResult InternalServerError(UnfurlApiException ex, ILogger logger)
    {
        logger.LogError(ex, "Returning Internal Server error {Message}", ex.Message);

        return new ObjectResult(ex.ErrorFromUnfurlApi)
        {
            StatusCode = 500
        };
    }

    private static IActionResult BadRequest(BadRequestException ex, ILogger logger)
    {
        logger.LogDebug(ex, "Bad Request exception occured");

        return new BadRequestObjectResult(new ErrorResponseDto(ex.Message));
    }

    public static Func<Exception, IActionResult> Handle(ILogger logger) =>
        e => Hnd(e, logger);
    public static Func<Error, IActionResult> HandleErr(ILogger logger) =>
        e => e.IsExpected ? HndExpected(e, logger) : Hnd(e.ToException(), logger);
    public static IActionResult HandleEx(Exception ex, ILogger logger) =>
        Hnd(ex, logger);
    private static IActionResult HndExpected(Error e, ILogger logger)
    {
        logger.LogInformation($"Returning Expected error. Status code/Error code: {e.Code} Error message: {e.Message}");
        return new ObjectResult(new ErrorResponseDto(e.Message))
        {
            StatusCode = e.Code
        };
    }
    private static IActionResult Hnd(Exception e, ILogger logger) =>
        e switch
        {
            ScraperException ex => UnprocessableEntity($"An error occured while processing: {ex.Message}", ex, logger),
            BadRequestException ex => BadRequest(ex, logger),
            UnfurlApiException ex => InternalServerError(ex, logger),
            TaskCanceledException ex => RequestTimeout(ex, logger),
            _ => InternalServerError(e, logger)
        };

}
