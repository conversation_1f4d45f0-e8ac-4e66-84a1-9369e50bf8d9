using System.ComponentModel.DataAnnotations;

namespace CrateApi.Common.Attributes;

public class CustomUrlAttribute : ValidationAttribute
{
    public override bool IsValid(object? value)
    {
        if (value is null || value is not string)
        {
            return false;
        }
        var result = Uri.TryCreate(value.ToString(), UriKind.Absolute, out Uri? uri);

        if (!result || uri is null)
        {
            return false;
        }

        return true;
    }
}
