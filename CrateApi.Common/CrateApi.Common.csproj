<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LineEndings>LF</LineEndings>
    <WarningsAsErrors>true</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
      <PackageReference Include="LanguageExt.Core" Version="5.0.0-beta-49" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        <PrivateAssets>all</PrivateAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        <PrivateAssets>all</PrivateAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
      <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
      <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
      <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
      <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="9.0.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="System.Text.Json" Version="9.0.0" />
    </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CrateApi.Data\CrateApi.Data.csproj" />
  </ItemGroup>
</Project>
