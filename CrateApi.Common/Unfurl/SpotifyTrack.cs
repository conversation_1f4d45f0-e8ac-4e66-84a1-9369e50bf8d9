using CrateApi.Common.Dto.Request;
using Newtonsoft.Json;

namespace CrateApi.Common;

public record SpotifyTrack : IContentUnfurled, IIsrc, IDuration
{
    [JsonProperty("id")]
    public string Id { get; init; } = default!;

    [JsonProperty("name")]
    public string Name { get; init; } = default!;

    [JsonProperty("type")]
    public string SType { get; init; } = default!;

    [JsonProperty("uri")]
    public string Uri { get; init; } = default!;

    [JsonProperty("href")]
    public string Href { get; init; } = default!;

    [JsonProperty("duration_ms")]
    public int DurationMs { get; init; }

    [JsonProperty("explicit")]
    public bool Explicit { get; init; }

    [JsonProperty("popularity")]
    public int Popularity { get; init; }

    [JsonProperty("preview_url")]
    public string? PreviewUrl { get; init; }

    [JsonProperty("track_number")]
    public int TrackNumber { get; init; }

    [JsonProperty("disc_number")]
    public int DiscNumber { get; init; }

    [JsonProperty("external_urls")]
    public ExternalUrls ExternalUrls { get; init; } = default!;

    [JsonProperty("external_ids")]
    public ExternalIds ExternalIds { get; init; } = default!;

    [JsonProperty("album")]
    public Album Album { get; init; } = default!;

    [JsonProperty("artists")]
    public IReadOnlyList<Artist> Artists { get; init; } = default!;

    [JsonProperty("available_markets")]
    public IReadOnlyList<string> AvailableMarkets { get; init; } = default!;

    [JsonProperty("is_local")]
    public bool IsLocal { get; init; }

    public string Url => Uri;

    public string Title => Name;

    public string Detail => Artists?.FirstOrDefault()?.Name ?? "";

    public int Type => (int)UnfurledContentType.Spotify;

    public string MediaUrl => Album?.Images?.FirstOrDefault()?.Url ?? "";

    public string Isrc => ExternalIds?.Isrc ?? "";

    public int Duration => DurationMs;
}

public record Album
{
    [JsonProperty("id")]
    public string Id { get; init; } = default!;

    [JsonProperty("name")]
    public string Name { get; init; } = default!;

    [JsonProperty("type")]
    public string Type { get; init; } = default!;

    [JsonProperty("uri")]
    public string Uri { get; init; } = default!;

    [JsonProperty("href")]
    public string Href { get; init; } = default!;

    [JsonProperty("album_type")]
    public string AlbumType { get; init; } = default!;

    [JsonProperty("total_tracks")]
    public int TotalTracks { get; init; }

    [JsonProperty("release_date")]
    public string ReleaseDate { get; init; } = default!;

    [JsonProperty("release_date_precision")]
    public string ReleaseDatePrecision { get; init; } = default!;

    [JsonProperty("images")]
    public IReadOnlyList<Image> Images { get; init; } = default!;

    [JsonProperty("external_urls")]
    public ExternalUrls ExternalUrls { get; init; } = default!;

    [JsonProperty("artists")]
    public IReadOnlyList<Artist> Artists { get; init; } = default!;

    [JsonProperty("available_markets")]
    public IReadOnlyList<string> AvailableMarkets { get; init; } = default!;
}

public record Artist
{
    [JsonProperty("id")]
    public string Id { get; init; } = default!;

    [JsonProperty("name")]
    public string Name { get; init; } = default!;

    [JsonProperty("type")]
    public string Type { get; init; } = default!;

    [JsonProperty("uri")]
    public string Uri { get; init; } = default!;

    [JsonProperty("href")]
    public string Href { get; init; } = default!;

    [JsonProperty("external_urls")]
    public ExternalUrls ExternalUrls { get; init; } = default!;
}

public record Image
{
    [JsonProperty("url")]
    public string Url { get; init; } = default!;

    [JsonProperty("width")]
    public int Width { get; init; }

    [JsonProperty("height")]
    public int Height { get; init; }
}

public record ExternalUrls
{
    [JsonProperty("spotify")]
    public string Spotify { get; init; } = default!;
}

public record ExternalIds
{
    [JsonProperty("isrc")]
    public string Isrc { get; init; } = default!;
}
