using System.Text.Json.Serialization;

namespace CrateApi.Common;

public record YouTubeVideo(
    [property: <PERSON>sonPropertyName("title")]
    string Title,
    [property: <PERSON>son<PERSON>ropertyName("author_name")]
    string AuthorName,
    [property: <PERSON><PERSON><PERSON>ropertyName("author_url")]
    string AuthorUrl,
    [property: <PERSON>son<PERSON>ropertyName("type")]
    string? Type,
    [property: <PERSON>son<PERSON>ropertyName("thumbnail_url")]
    string? ThumbnailUrl
);

    
