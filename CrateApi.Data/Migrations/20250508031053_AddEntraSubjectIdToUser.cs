﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddEntraSubjectIdToUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "Created",
                schema: "crate",
                table: "Users",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "EntraSubjectId",
                schema: "crate",
                table: "Users",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastLogin",
                schema: "crate",
                table: "Users",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "Updated",
                schema: "crate",
                table: "Users",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Created",
                schema: "crate",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "EntraSubjectId",
                schema: "crate",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "LastLogin",
                schema: "crate",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Updated",
                schema: "crate",
                table: "Users");
        }
    }
}
