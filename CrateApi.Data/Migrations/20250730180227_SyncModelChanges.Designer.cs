﻿// <auto-generated />
using System;
using CrateApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CrateApi.Data.Migrations
{
    [DbContext(typeof(CrateDbContext))]
    [Migration("20250730180227_SyncModelChanges")]
    partial class SyncModelChanges
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("crate")
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CrateApi.Data.Models.AuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("ip_address");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)")
                        .HasColumnName("message");

                    b.Property<string>("Path")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)")
                        .HasColumnName("path");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)")
                        .HasColumnName("user_agent");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_audits");

                    b.ToTable("audits", "crate");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Challenge", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ChallengeValue")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("challenge_value");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("boolean")
                        .HasColumnName("is_used");

                    b.Property<int>("NfcCardId")
                        .HasColumnType("integer")
                        .HasColumnName("nfc_card_id");

                    b.HasKey("Id")
                        .HasName("pk_challenges");

                    b.HasIndex("NfcCardId")
                        .HasDatabaseName("ix_challenges_nfc_card_id");

                    b.ToTable("challenges", "crate");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("name");

                    b.Property<string>("Thumbnail")
                        .HasColumnType("text")
                        .HasColumnName("thumbnail");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_collections");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_collections_user_id");

                    b.ToTable("collections", "crate");
                });

            modelBuilder.Entity("CrateApi.Data.Models.CollectionContentMapping", b =>
                {
                    b.Property<int>("CollectionId")
                        .HasColumnType("integer")
                        .HasColumnName("collection_id");

                    b.Property<int>("ContentId")
                        .HasColumnType("integer")
                        .HasColumnName("content_id");

                    b.HasKey("CollectionId", "ContentId")
                        .HasName("pk_collection_content_mappings");

                    b.HasIndex("ContentId")
                        .HasDatabaseName("ix_collection_content_mappings_content_id");

                    b.ToTable("collection_content_mappings", "crate");
                });

            modelBuilder.Entity("CrateApi.Data.Models.CollectionTrackMapping", b =>
                {
                    b.Property<int>("CollectionId")
                        .HasColumnType("integer")
                        .HasColumnName("collection_id");

                    b.Property<int>("TrackId")
                        .HasColumnType("integer")
                        .HasColumnName("track_id");

                    b.HasKey("CollectionId", "TrackId")
                        .HasName("pk_collection_track_mappings");

                    b.HasIndex("TrackId")
                        .HasDatabaseName("ix_collection_track_mappings_track_id");

                    b.ToTable("collection_track_mappings", "crate");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Content", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created");

                    b.Property<string>("Detail")
                        .HasColumnType("text")
                        .HasColumnName("detail");

                    b.Property<string>("MediaUrl")
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)")
                        .HasColumnName("media_url");

                    b.Property<string>("Metadata")
                        .HasColumnType("jsonb")
                        .HasColumnName("metadata");

                    b.Property<int?>("Platform")
                        .HasColumnType("integer")
                        .HasColumnName("platform");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)")
                        .HasColumnName("title");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated");

                    b.Property<string>("Url")
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)")
                        .HasColumnName("url");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_content");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_content_user_id");

                    b.ToTable("content", "crate");
                });

            modelBuilder.Entity("CrateApi.Data.Models.NfcCard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Atr")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("atr");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("LastUsed")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_used");

                    b.Property<string>("Uid")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)")
                        .HasColumnName("uid");

                    b.HasKey("Id")
                        .HasName("pk_nfc_cards");

                    b.ToTable("nfc_cards", "crate");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ArtistName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("artist_name");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created");

                    b.Property<int?>("Duration")
                        .HasColumnType("integer")
                        .HasColumnName("duration");

                    b.Property<string>("Isrc")
                        .HasColumnType("text")
                        .HasColumnName("isrc");

                    b.Property<string>("MediaUrl")
                        .HasColumnType("text")
                        .HasColumnName("media_url");

                    b.Property<int?>("PlatformType")
                        .HasColumnType("integer")
                        .HasColumnName("platform_type");

                    b.Property<string>("TrackTitle")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("track_title");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated");

                    b.Property<string>("Url")
                        .HasColumnType("text")
                        .HasColumnName("url");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_tracks");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_tracks_user_id");

                    b.ToTable("tracks", "crate");
                });

            modelBuilder.Entity("CrateApi.Data.Models.TrackContentMapping", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ContentId")
                        .HasColumnType("integer")
                        .HasColumnName("content_id");

                    b.Property<int>("TrackId")
                        .HasColumnType("integer")
                        .HasColumnName("track_id");

                    b.HasKey("Id")
                        .HasName("pk_track_content_mappings");

                    b.HasIndex("ContentId")
                        .HasDatabaseName("ix_track_content_mappings_content_id");

                    b.HasIndex("TrackId")
                        .HasDatabaseName("ix_track_content_mappings_track_id");

                    b.ToTable("track_content_mappings", "crate");
                });

            modelBuilder.Entity("CrateApi.Data.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("email");

                    b.Property<string>("EntraSubjectId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("entra_subject_id");

                    b.Property<DateTime?>("LastLogin")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_login");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("password");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("username");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("ix_users_email");

                    b.ToTable("users", "crate");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Challenge", b =>
                {
                    b.HasOne("CrateApi.Data.Models.NfcCard", "NfcCard")
                        .WithMany("Challenges")
                        .HasForeignKey("NfcCardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_challenges_nfc_cards_nfc_card_id");

                    b.Navigation("NfcCard");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_collections_users_user_id");
                });

            modelBuilder.Entity("CrateApi.Data.Models.CollectionContentMapping", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Collection", "Collection")
                        .WithMany("CollectionContent")
                        .HasForeignKey("CollectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_collection_content_mappings_collections_collection_id");

                    b.HasOne("CrateApi.Data.Models.Content", "Content")
                        .WithMany("CollectionContent")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_collection_content_mappings_content_content_id");

                    b.Navigation("Collection");

                    b.Navigation("Content");
                });

            modelBuilder.Entity("CrateApi.Data.Models.CollectionTrackMapping", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Collection", "Collection")
                        .WithMany("CollectionTracks")
                        .HasForeignKey("CollectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_collection_track_mappings_collections_collection_id");

                    b.HasOne("CrateApi.Data.Models.Track", "Track")
                        .WithMany("CollectionTracks")
                        .HasForeignKey("TrackId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_collection_track_mappings_tracks_track_id");

                    b.Navigation("Collection");

                    b.Navigation("Track");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Content", b =>
                {
                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_content_users_user_id");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_tracks_users_user_id");
                });

            modelBuilder.Entity("CrateApi.Data.Models.TrackContentMapping", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Content", "Content")
                        .WithMany("TrackMappings")
                        .HasForeignKey("ContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_track_content_mappings_content_content_id");

                    b.HasOne("CrateApi.Data.Models.Track", "Track")
                        .WithMany("ContentMappings")
                        .HasForeignKey("TrackId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_track_content_mappings_tracks_track_id");

                    b.Navigation("Content");

                    b.Navigation("Track");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Navigation("CollectionContent");

                    b.Navigation("CollectionTracks");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Content", b =>
                {
                    b.Navigation("CollectionContent");

                    b.Navigation("TrackMappings");
                });

            modelBuilder.Entity("CrateApi.Data.Models.NfcCard", b =>
                {
                    b.Navigation("Challenges");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.Navigation("CollectionTracks");

                    b.Navigation("ContentMappings");
                });
#pragma warning restore 612, 618
        }
    }
}
