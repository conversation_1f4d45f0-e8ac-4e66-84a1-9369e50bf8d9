﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class saveplatformtypefromunfurltodb : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PlatformType",
                schema: "crate",
                table: "Tracks",
                type: "integer",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PlatformType",
                schema: "crate",
                table: "Tracks");
        }
    }
}
