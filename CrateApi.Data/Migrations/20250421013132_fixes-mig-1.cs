﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class fixesmig1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Url",
                schema: "crate",
                table: "content",
                type: "character varying(2048)",
                maxLength: 2048,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(2048)",
                oldMaxLength: 2048);

            migrationBuilder.AlterColumn<string>(
                name: "Metada<PERSON>",
                schema: "crate",
                table: "content",
                type: "jsonb",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "jsonb");

            migrationBuilder.AlterColumn<string>(
                name: "Detail",
                schema: "crate",
                table: "content",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddColumn<string>(
                name: "MediaUrl",
                schema: "crate",
                table: "content",
                type: "character varying(2048)",
                maxLength: 2048,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UserId",
                schema: "crate",
                table: "content",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_content_UserId",
                schema: "crate",
                table: "content",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_content_Users_UserId",
                schema: "crate",
                table: "content",
                column: "UserId",
                principalSchema: "crate",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_content_Users_UserId",
                schema: "crate",
                table: "content");

            migrationBuilder.DropIndex(
                name: "IX_content_UserId",
                schema: "crate",
                table: "content");

            migrationBuilder.DropColumn(
                name: "MediaUrl",
                schema: "crate",
                table: "content");

            migrationBuilder.DropColumn(
                name: "UserId",
                schema: "crate",
                table: "content");

            migrationBuilder.AlterColumn<string>(
                name: "Url",
                schema: "crate",
                table: "content",
                type: "character varying(2048)",
                maxLength: 2048,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(2048)",
                oldMaxLength: 2048,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Metadata",
                schema: "crate",
                table: "content",
                type: "jsonb",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "jsonb",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Detail",
                schema: "crate",
                table: "content",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);
        }
    }
}
