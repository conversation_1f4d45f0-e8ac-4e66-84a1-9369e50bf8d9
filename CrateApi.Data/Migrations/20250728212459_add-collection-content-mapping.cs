﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class addcollectioncontentmapping : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "collection_content_mapping",
                schema: "crate",
                columns: table => new
                {
                    collection_id = table.Column<int>(type: "integer", nullable: false),
                    content_id = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_collection_content_mapping", x => new { x.collection_id, x.content_id });
                    table.ForeignKey(
                        name: "fk_collection_content_mapping_collections_collection_id",
                        column: x => x.collection_id,
                        principalSchema: "crate",
                        principalTable: "collections",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_collection_content_mapping_content_content_id",
                        column: x => x.content_id,
                        principalSchema: "crate",
                        principalTable: "content",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_collection_content_mapping_content_id",
                schema: "crate",
                table: "collection_content_mapping",
                column: "content_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "collection_content_mapping",
                schema: "crate");
        }
    }
}
