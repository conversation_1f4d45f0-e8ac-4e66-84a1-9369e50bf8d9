﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class snakecase : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Challenges_NfcCards_NfcCardId",
                schema: "crate",
                table: "Challenges");

            migrationBuilder.DropForeignKey(
                name: "FK_Collections_Users_UserId",
                schema: "crate",
                table: "Collections");

            migrationBuilder.DropForeignKey(
                name: "FK_CollectionTrackMappings_Collections_CollectionId",
                schema: "crate",
                table: "CollectionTrackMappings");

            migrationBuilder.DropForeignKey(
                name: "FK_CollectionTrackMappings_Tracks_TrackId",
                schema: "crate",
                table: "CollectionTrackMappings");

            migrationBuilder.DropForeignKey(
                name: "FK_content_Users_UserId",
                schema: "crate",
                table: "content");

            migrationBuilder.DropForeignKey(
                name: "FK_TrackContentMappings_Tracks_TrackId",
                schema: "crate",
                table: "TrackContentMappings");

            migrationBuilder.DropForeignKey(
                name: "FK_TrackContentMappings_content_ContentId",
                schema: "crate",
                table: "TrackContentMappings");

            migrationBuilder.DropForeignKey(
                name: "FK_Tracks_Users_UserId",
                schema: "crate",
                table: "Tracks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Users",
                schema: "crate",
                table: "Users");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Tracks",
                schema: "crate",
                table: "Tracks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_content",
                schema: "crate",
                table: "content");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Collections",
                schema: "crate",
                table: "Collections");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Challenges",
                schema: "crate",
                table: "Challenges");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Audits",
                schema: "crate",
                table: "Audits");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TrackContentMappings",
                schema: "crate",
                table: "TrackContentMappings");

            migrationBuilder.DropPrimaryKey(
                name: "PK_NfcCards",
                schema: "crate",
                table: "NfcCards");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CollectionTrackMappings",
                schema: "crate",
                table: "CollectionTrackMappings");

            migrationBuilder.RenameTable(
                name: "Users",
                schema: "crate",
                newName: "users",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "Tracks",
                schema: "crate",
                newName: "tracks",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "Collections",
                schema: "crate",
                newName: "collections",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "Challenges",
                schema: "crate",
                newName: "challenges",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "Audits",
                schema: "crate",
                newName: "audits",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "TrackContentMappings",
                schema: "crate",
                newName: "track_content_mappings",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "NfcCards",
                schema: "crate",
                newName: "nfc_cards",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "CollectionTrackMappings",
                schema: "crate",
                newName: "collection_track_mappings",
                newSchema: "crate");

            migrationBuilder.RenameColumn(
                name: "Username",
                schema: "crate",
                table: "users",
                newName: "username");

            migrationBuilder.RenameColumn(
                name: "Updated",
                schema: "crate",
                table: "users",
                newName: "updated");

            migrationBuilder.RenameColumn(
                name: "Password",
                schema: "crate",
                table: "users",
                newName: "password");

            migrationBuilder.RenameColumn(
                name: "Email",
                schema: "crate",
                table: "users",
                newName: "email");

            migrationBuilder.RenameColumn(
                name: "Created",
                schema: "crate",
                table: "users",
                newName: "created");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "crate",
                table: "users",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "LastLogin",
                schema: "crate",
                table: "users",
                newName: "last_login");

            migrationBuilder.RenameColumn(
                name: "EntraSubjectId",
                schema: "crate",
                table: "users",
                newName: "entra_subject_id");

            migrationBuilder.RenameIndex(
                name: "IX_Users_Email",
                schema: "crate",
                table: "users",
                newName: "ix_users_email");

            migrationBuilder.RenameColumn(
                name: "Url",
                schema: "crate",
                table: "tracks",
                newName: "url");

            migrationBuilder.RenameColumn(
                name: "Updated",
                schema: "crate",
                table: "tracks",
                newName: "updated");

            migrationBuilder.RenameColumn(
                name: "Isrc",
                schema: "crate",
                table: "tracks",
                newName: "isrc");

            migrationBuilder.RenameColumn(
                name: "Duration",
                schema: "crate",
                table: "tracks",
                newName: "duration");

            migrationBuilder.RenameColumn(
                name: "Created",
                schema: "crate",
                table: "tracks",
                newName: "created");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "crate",
                table: "tracks",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                schema: "crate",
                table: "tracks",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "TrackTitle",
                schema: "crate",
                table: "tracks",
                newName: "track_title");

            migrationBuilder.RenameColumn(
                name: "PlatformType",
                schema: "crate",
                table: "tracks",
                newName: "platform_type");

            migrationBuilder.RenameColumn(
                name: "MediaUrl",
                schema: "crate",
                table: "tracks",
                newName: "media_url");

            migrationBuilder.RenameColumn(
                name: "ArtistName",
                schema: "crate",
                table: "tracks",
                newName: "artist_name");

            migrationBuilder.RenameIndex(
                name: "IX_Tracks_UserId",
                schema: "crate",
                table: "tracks",
                newName: "ix_tracks_user_id");

            migrationBuilder.RenameColumn(
                name: "Url",
                schema: "crate",
                table: "content",
                newName: "url");

            migrationBuilder.RenameColumn(
                name: "Updated",
                schema: "crate",
                table: "content",
                newName: "updated");

            migrationBuilder.RenameColumn(
                name: "Title",
                schema: "crate",
                table: "content",
                newName: "title");

            migrationBuilder.RenameColumn(
                name: "Platform",
                schema: "crate",
                table: "content",
                newName: "platform");

            migrationBuilder.RenameColumn(
                name: "Metadata",
                schema: "crate",
                table: "content",
                newName: "metadata");

            migrationBuilder.RenameColumn(
                name: "Detail",
                schema: "crate",
                table: "content",
                newName: "detail");

            migrationBuilder.RenameColumn(
                name: "Created",
                schema: "crate",
                table: "content",
                newName: "created");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "crate",
                table: "content",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                schema: "crate",
                table: "content",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "MediaUrl",
                schema: "crate",
                table: "content",
                newName: "media_url");

            migrationBuilder.RenameIndex(
                name: "IX_content_UserId",
                schema: "crate",
                table: "content",
                newName: "ix_content_user_id");

            migrationBuilder.RenameColumn(
                name: "Updated",
                schema: "crate",
                table: "collections",
                newName: "updated");

            migrationBuilder.RenameColumn(
                name: "Thumbnail",
                schema: "crate",
                table: "collections",
                newName: "thumbnail");

            migrationBuilder.RenameColumn(
                name: "Name",
                schema: "crate",
                table: "collections",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Created",
                schema: "crate",
                table: "collections",
                newName: "created");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "crate",
                table: "collections",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                schema: "crate",
                table: "collections",
                newName: "user_id");

            migrationBuilder.RenameIndex(
                name: "IX_Collections_UserId",
                schema: "crate",
                table: "collections",
                newName: "ix_collections_user_id");

            migrationBuilder.RenameColumn(
                name: "Created",
                schema: "crate",
                table: "challenges",
                newName: "created");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "crate",
                table: "challenges",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "NfcCardId",
                schema: "crate",
                table: "challenges",
                newName: "nfc_card_id");

            migrationBuilder.RenameColumn(
                name: "IsUsed",
                schema: "crate",
                table: "challenges",
                newName: "is_used");

            migrationBuilder.RenameColumn(
                name: "ChallengeValue",
                schema: "crate",
                table: "challenges",
                newName: "challenge_value");

            migrationBuilder.RenameIndex(
                name: "IX_Challenges_NfcCardId",
                schema: "crate",
                table: "challenges",
                newName: "ix_challenges_nfc_card_id");

            migrationBuilder.RenameColumn(
                name: "Path",
                schema: "crate",
                table: "audits",
                newName: "path");

            migrationBuilder.RenameColumn(
                name: "Message",
                schema: "crate",
                table: "audits",
                newName: "message");

            migrationBuilder.RenameColumn(
                name: "Created",
                schema: "crate",
                table: "audits",
                newName: "created");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "crate",
                table: "audits",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "UserId",
                schema: "crate",
                table: "audits",
                newName: "user_id");

            migrationBuilder.RenameColumn(
                name: "UserAgent",
                schema: "crate",
                table: "audits",
                newName: "user_agent");

            migrationBuilder.RenameColumn(
                name: "IpAddress",
                schema: "crate",
                table: "audits",
                newName: "ip_address");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "crate",
                table: "track_content_mappings",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "TrackId",
                schema: "crate",
                table: "track_content_mappings",
                newName: "track_id");

            migrationBuilder.RenameColumn(
                name: "ContentId",
                schema: "crate",
                table: "track_content_mappings",
                newName: "content_id");

            migrationBuilder.RenameIndex(
                name: "IX_TrackContentMappings_TrackId",
                schema: "crate",
                table: "track_content_mappings",
                newName: "ix_track_content_mappings_track_id");

            migrationBuilder.RenameIndex(
                name: "IX_TrackContentMappings_ContentId",
                schema: "crate",
                table: "track_content_mappings",
                newName: "ix_track_content_mappings_content_id");

            migrationBuilder.RenameColumn(
                name: "Uid",
                schema: "crate",
                table: "nfc_cards",
                newName: "uid");

            migrationBuilder.RenameColumn(
                name: "Created",
                schema: "crate",
                table: "nfc_cards",
                newName: "created");

            migrationBuilder.RenameColumn(
                name: "Atr",
                schema: "crate",
                table: "nfc_cards",
                newName: "atr");

            migrationBuilder.RenameColumn(
                name: "Id",
                schema: "crate",
                table: "nfc_cards",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "LastUsed",
                schema: "crate",
                table: "nfc_cards",
                newName: "last_used");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                schema: "crate",
                table: "nfc_cards",
                newName: "is_active");

            migrationBuilder.RenameColumn(
                name: "TrackId",
                schema: "crate",
                table: "collection_track_mappings",
                newName: "track_id");

            migrationBuilder.RenameColumn(
                name: "CollectionId",
                schema: "crate",
                table: "collection_track_mappings",
                newName: "collection_id");

            migrationBuilder.RenameIndex(
                name: "IX_CollectionTrackMappings_TrackId",
                schema: "crate",
                table: "collection_track_mappings",
                newName: "ix_collection_track_mappings_track_id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_users",
                schema: "crate",
                table: "users",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_tracks",
                schema: "crate",
                table: "tracks",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_content",
                schema: "crate",
                table: "content",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_collections",
                schema: "crate",
                table: "collections",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_challenges",
                schema: "crate",
                table: "challenges",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_audits",
                schema: "crate",
                table: "audits",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_track_content_mappings",
                schema: "crate",
                table: "track_content_mappings",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_nfc_cards",
                schema: "crate",
                table: "nfc_cards",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_collection_track_mappings",
                schema: "crate",
                table: "collection_track_mappings",
                columns: new[] { "collection_id", "track_id" });

            migrationBuilder.AddForeignKey(
                name: "fk_challenges_nfc_cards_nfc_card_id",
                schema: "crate",
                table: "challenges",
                column: "nfc_card_id",
                principalSchema: "crate",
                principalTable: "nfc_cards",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_collection_track_mappings_collections_collection_id",
                schema: "crate",
                table: "collection_track_mappings",
                column: "collection_id",
                principalSchema: "crate",
                principalTable: "collections",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_collection_track_mappings_tracks_track_id",
                schema: "crate",
                table: "collection_track_mappings",
                column: "track_id",
                principalSchema: "crate",
                principalTable: "tracks",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_collections_users_user_id",
                schema: "crate",
                table: "collections",
                column: "user_id",
                principalSchema: "crate",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_content_users_user_id",
                schema: "crate",
                table: "content",
                column: "user_id",
                principalSchema: "crate",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_track_content_mappings_content_content_id",
                schema: "crate",
                table: "track_content_mappings",
                column: "content_id",
                principalSchema: "crate",
                principalTable: "content",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_track_content_mappings_tracks_track_id",
                schema: "crate",
                table: "track_content_mappings",
                column: "track_id",
                principalSchema: "crate",
                principalTable: "tracks",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_tracks_users_user_id",
                schema: "crate",
                table: "tracks",
                column: "user_id",
                principalSchema: "crate",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_challenges_nfc_cards_nfc_card_id",
                schema: "crate",
                table: "challenges");

            migrationBuilder.DropForeignKey(
                name: "fk_collection_track_mappings_collections_collection_id",
                schema: "crate",
                table: "collection_track_mappings");

            migrationBuilder.DropForeignKey(
                name: "fk_collection_track_mappings_tracks_track_id",
                schema: "crate",
                table: "collection_track_mappings");

            migrationBuilder.DropForeignKey(
                name: "fk_collections_users_user_id",
                schema: "crate",
                table: "collections");

            migrationBuilder.DropForeignKey(
                name: "fk_content_users_user_id",
                schema: "crate",
                table: "content");

            migrationBuilder.DropForeignKey(
                name: "fk_track_content_mappings_content_content_id",
                schema: "crate",
                table: "track_content_mappings");

            migrationBuilder.DropForeignKey(
                name: "fk_track_content_mappings_tracks_track_id",
                schema: "crate",
                table: "track_content_mappings");

            migrationBuilder.DropForeignKey(
                name: "fk_tracks_users_user_id",
                schema: "crate",
                table: "tracks");

            migrationBuilder.DropPrimaryKey(
                name: "pk_users",
                schema: "crate",
                table: "users");

            migrationBuilder.DropPrimaryKey(
                name: "pk_tracks",
                schema: "crate",
                table: "tracks");

            migrationBuilder.DropPrimaryKey(
                name: "pk_content",
                schema: "crate",
                table: "content");

            migrationBuilder.DropPrimaryKey(
                name: "pk_collections",
                schema: "crate",
                table: "collections");

            migrationBuilder.DropPrimaryKey(
                name: "pk_challenges",
                schema: "crate",
                table: "challenges");

            migrationBuilder.DropPrimaryKey(
                name: "pk_audits",
                schema: "crate",
                table: "audits");

            migrationBuilder.DropPrimaryKey(
                name: "pk_track_content_mappings",
                schema: "crate",
                table: "track_content_mappings");

            migrationBuilder.DropPrimaryKey(
                name: "pk_nfc_cards",
                schema: "crate",
                table: "nfc_cards");

            migrationBuilder.DropPrimaryKey(
                name: "pk_collection_track_mappings",
                schema: "crate",
                table: "collection_track_mappings");

            migrationBuilder.RenameTable(
                name: "users",
                schema: "crate",
                newName: "Users",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "tracks",
                schema: "crate",
                newName: "Tracks",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "collections",
                schema: "crate",
                newName: "Collections",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "challenges",
                schema: "crate",
                newName: "Challenges",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "audits",
                schema: "crate",
                newName: "Audits",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "track_content_mappings",
                schema: "crate",
                newName: "TrackContentMappings",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "nfc_cards",
                schema: "crate",
                newName: "NfcCards",
                newSchema: "crate");

            migrationBuilder.RenameTable(
                name: "collection_track_mappings",
                schema: "crate",
                newName: "CollectionTrackMappings",
                newSchema: "crate");

            migrationBuilder.RenameColumn(
                name: "username",
                schema: "crate",
                table: "Users",
                newName: "Username");

            migrationBuilder.RenameColumn(
                name: "updated",
                schema: "crate",
                table: "Users",
                newName: "Updated");

            migrationBuilder.RenameColumn(
                name: "password",
                schema: "crate",
                table: "Users",
                newName: "Password");

            migrationBuilder.RenameColumn(
                name: "email",
                schema: "crate",
                table: "Users",
                newName: "Email");

            migrationBuilder.RenameColumn(
                name: "created",
                schema: "crate",
                table: "Users",
                newName: "Created");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "crate",
                table: "Users",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "last_login",
                schema: "crate",
                table: "Users",
                newName: "LastLogin");

            migrationBuilder.RenameColumn(
                name: "entra_subject_id",
                schema: "crate",
                table: "Users",
                newName: "EntraSubjectId");

            migrationBuilder.RenameIndex(
                name: "ix_users_email",
                schema: "crate",
                table: "Users",
                newName: "IX_Users_Email");

            migrationBuilder.RenameColumn(
                name: "url",
                schema: "crate",
                table: "Tracks",
                newName: "Url");

            migrationBuilder.RenameColumn(
                name: "updated",
                schema: "crate",
                table: "Tracks",
                newName: "Updated");

            migrationBuilder.RenameColumn(
                name: "isrc",
                schema: "crate",
                table: "Tracks",
                newName: "Isrc");

            migrationBuilder.RenameColumn(
                name: "duration",
                schema: "crate",
                table: "Tracks",
                newName: "Duration");

            migrationBuilder.RenameColumn(
                name: "created",
                schema: "crate",
                table: "Tracks",
                newName: "Created");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "crate",
                table: "Tracks",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "user_id",
                schema: "crate",
                table: "Tracks",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "track_title",
                schema: "crate",
                table: "Tracks",
                newName: "TrackTitle");

            migrationBuilder.RenameColumn(
                name: "platform_type",
                schema: "crate",
                table: "Tracks",
                newName: "PlatformType");

            migrationBuilder.RenameColumn(
                name: "media_url",
                schema: "crate",
                table: "Tracks",
                newName: "MediaUrl");

            migrationBuilder.RenameColumn(
                name: "artist_name",
                schema: "crate",
                table: "Tracks",
                newName: "ArtistName");

            migrationBuilder.RenameIndex(
                name: "ix_tracks_user_id",
                schema: "crate",
                table: "Tracks",
                newName: "IX_Tracks_UserId");

            migrationBuilder.RenameColumn(
                name: "url",
                schema: "crate",
                table: "content",
                newName: "Url");

            migrationBuilder.RenameColumn(
                name: "updated",
                schema: "crate",
                table: "content",
                newName: "Updated");

            migrationBuilder.RenameColumn(
                name: "title",
                schema: "crate",
                table: "content",
                newName: "Title");

            migrationBuilder.RenameColumn(
                name: "platform",
                schema: "crate",
                table: "content",
                newName: "Platform");

            migrationBuilder.RenameColumn(
                name: "metadata",
                schema: "crate",
                table: "content",
                newName: "Metadata");

            migrationBuilder.RenameColumn(
                name: "detail",
                schema: "crate",
                table: "content",
                newName: "Detail");

            migrationBuilder.RenameColumn(
                name: "created",
                schema: "crate",
                table: "content",
                newName: "Created");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "crate",
                table: "content",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "user_id",
                schema: "crate",
                table: "content",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "media_url",
                schema: "crate",
                table: "content",
                newName: "MediaUrl");

            migrationBuilder.RenameIndex(
                name: "ix_content_user_id",
                schema: "crate",
                table: "content",
                newName: "IX_content_UserId");

            migrationBuilder.RenameColumn(
                name: "updated",
                schema: "crate",
                table: "Collections",
                newName: "Updated");

            migrationBuilder.RenameColumn(
                name: "thumbnail",
                schema: "crate",
                table: "Collections",
                newName: "Thumbnail");

            migrationBuilder.RenameColumn(
                name: "name",
                schema: "crate",
                table: "Collections",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "created",
                schema: "crate",
                table: "Collections",
                newName: "Created");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "crate",
                table: "Collections",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "user_id",
                schema: "crate",
                table: "Collections",
                newName: "UserId");

            migrationBuilder.RenameIndex(
                name: "ix_collections_user_id",
                schema: "crate",
                table: "Collections",
                newName: "IX_Collections_UserId");

            migrationBuilder.RenameColumn(
                name: "created",
                schema: "crate",
                table: "Challenges",
                newName: "Created");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "crate",
                table: "Challenges",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "nfc_card_id",
                schema: "crate",
                table: "Challenges",
                newName: "NfcCardId");

            migrationBuilder.RenameColumn(
                name: "is_used",
                schema: "crate",
                table: "Challenges",
                newName: "IsUsed");

            migrationBuilder.RenameColumn(
                name: "challenge_value",
                schema: "crate",
                table: "Challenges",
                newName: "ChallengeValue");

            migrationBuilder.RenameIndex(
                name: "ix_challenges_nfc_card_id",
                schema: "crate",
                table: "Challenges",
                newName: "IX_Challenges_NfcCardId");

            migrationBuilder.RenameColumn(
                name: "path",
                schema: "crate",
                table: "Audits",
                newName: "Path");

            migrationBuilder.RenameColumn(
                name: "message",
                schema: "crate",
                table: "Audits",
                newName: "Message");

            migrationBuilder.RenameColumn(
                name: "created",
                schema: "crate",
                table: "Audits",
                newName: "Created");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "crate",
                table: "Audits",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "user_id",
                schema: "crate",
                table: "Audits",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "user_agent",
                schema: "crate",
                table: "Audits",
                newName: "UserAgent");

            migrationBuilder.RenameColumn(
                name: "ip_address",
                schema: "crate",
                table: "Audits",
                newName: "IpAddress");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "crate",
                table: "TrackContentMappings",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "track_id",
                schema: "crate",
                table: "TrackContentMappings",
                newName: "TrackId");

            migrationBuilder.RenameColumn(
                name: "content_id",
                schema: "crate",
                table: "TrackContentMappings",
                newName: "ContentId");

            migrationBuilder.RenameIndex(
                name: "ix_track_content_mappings_track_id",
                schema: "crate",
                table: "TrackContentMappings",
                newName: "IX_TrackContentMappings_TrackId");

            migrationBuilder.RenameIndex(
                name: "ix_track_content_mappings_content_id",
                schema: "crate",
                table: "TrackContentMappings",
                newName: "IX_TrackContentMappings_ContentId");

            migrationBuilder.RenameColumn(
                name: "uid",
                schema: "crate",
                table: "NfcCards",
                newName: "Uid");

            migrationBuilder.RenameColumn(
                name: "created",
                schema: "crate",
                table: "NfcCards",
                newName: "Created");

            migrationBuilder.RenameColumn(
                name: "atr",
                schema: "crate",
                table: "NfcCards",
                newName: "Atr");

            migrationBuilder.RenameColumn(
                name: "id",
                schema: "crate",
                table: "NfcCards",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "last_used",
                schema: "crate",
                table: "NfcCards",
                newName: "LastUsed");

            migrationBuilder.RenameColumn(
                name: "is_active",
                schema: "crate",
                table: "NfcCards",
                newName: "IsActive");

            migrationBuilder.RenameColumn(
                name: "track_id",
                schema: "crate",
                table: "CollectionTrackMappings",
                newName: "TrackId");

            migrationBuilder.RenameColumn(
                name: "collection_id",
                schema: "crate",
                table: "CollectionTrackMappings",
                newName: "CollectionId");

            migrationBuilder.RenameIndex(
                name: "ix_collection_track_mappings_track_id",
                schema: "crate",
                table: "CollectionTrackMappings",
                newName: "IX_CollectionTrackMappings_TrackId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Users",
                schema: "crate",
                table: "Users",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Tracks",
                schema: "crate",
                table: "Tracks",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_content",
                schema: "crate",
                table: "content",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Collections",
                schema: "crate",
                table: "Collections",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Challenges",
                schema: "crate",
                table: "Challenges",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Audits",
                schema: "crate",
                table: "Audits",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TrackContentMappings",
                schema: "crate",
                table: "TrackContentMappings",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_NfcCards",
                schema: "crate",
                table: "NfcCards",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CollectionTrackMappings",
                schema: "crate",
                table: "CollectionTrackMappings",
                columns: new[] { "CollectionId", "TrackId" });

            migrationBuilder.AddForeignKey(
                name: "FK_Challenges_NfcCards_NfcCardId",
                schema: "crate",
                table: "Challenges",
                column: "NfcCardId",
                principalSchema: "crate",
                principalTable: "NfcCards",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Collections_Users_UserId",
                schema: "crate",
                table: "Collections",
                column: "UserId",
                principalSchema: "crate",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionTrackMappings_Collections_CollectionId",
                schema: "crate",
                table: "CollectionTrackMappings",
                column: "CollectionId",
                principalSchema: "crate",
                principalTable: "Collections",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionTrackMappings_Tracks_TrackId",
                schema: "crate",
                table: "CollectionTrackMappings",
                column: "TrackId",
                principalSchema: "crate",
                principalTable: "Tracks",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_content_Users_UserId",
                schema: "crate",
                table: "content",
                column: "UserId",
                principalSchema: "crate",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TrackContentMappings_Tracks_TrackId",
                schema: "crate",
                table: "TrackContentMappings",
                column: "TrackId",
                principalSchema: "crate",
                principalTable: "Tracks",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TrackContentMappings_content_ContentId",
                schema: "crate",
                table: "TrackContentMappings",
                column: "ContentId",
                principalSchema: "crate",
                principalTable: "content",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Tracks_Users_UserId",
                schema: "crate",
                table: "Tracks",
                column: "UserId",
                principalSchema: "crate",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
