using CrateApi.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace CrateApi.Data;

public class CrateDbContext : DbContext
{
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
    public CrateDbContext(DbContextOptions<CrateDbContext> options)
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        : base(options) { }

    public DbSet<Track> Tracks { get; set; }

    public DbSet<Collection> Collections { get; set; }

    public DbSet<User> Users { get; set; }

    public DbSet<NfcCard> NfcCards { get; set; }

    public DbSet<Challenge> Challenges { get; set; }

    public DbSet<CollectionTrackMapping> CollectionTrackMappings { get; set; }

    public DbSet<CollectionContentMapping> CollectionContentMappings { get; set; }

    public DbSet<TrackContentMapping> TrackContentMappings { get; set; }

    public DbSet<Content> Contents { get; set; }

    public DbSet<AuditLog> Audits { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.HasDefaultSchema("crate"); // set default schema

        modelBuilder.Entity<Track>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TrackTitle).IsRequired();
            entity.Property(e => e.ArtistName).IsRequired();
            entity.Property(e => e.Updated).IsRequired();
            entity.Property(e => e.Created).IsRequired();
            entity.HasIndex(e => e.UserId);
            entity.HasOne<User>().WithMany().HasForeignKey(t => t.UserId).OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<Collection>(entity =>
        {
            entity.Property(e => e.UserId).IsRequired();
            entity.HasIndex(e => e.UserId);
            entity.HasOne<User>().WithMany().HasForeignKey(c => c.UserId).OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<Content>(entity => {
            entity.HasKey(e => e.Id);
            entity.HasOne<User>().WithMany().HasForeignKey(c => c.UserId).IsRequired(false).OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<AuditLog>(entity => {
            entity.HasKey(e => e.Id);
        });

        modelBuilder.Entity<CollectionTrackMapping>(entity =>
        {
            entity.HasKey(ct => new { ct.CollectionId, ct.TrackId });

            entity.HasOne(ct => ct.Collection)
                .WithMany(c => c.CollectionTracks)
                .HasForeignKey(ct => ct.CollectionId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(ct => ct.Track)
                .WithMany(t => t.CollectionTracks)
                .HasForeignKey(ct => ct.TrackId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        
        modelBuilder.Entity<CollectionContentMapping>(entity =>
        {
            entity.HasKey(ct => new { ct.CollectionId, ct.ContentId });

            entity.HasOne(ct => ct.Collection)
                .WithMany(c => c.CollectionContent)
                .HasForeignKey(ct => ct.CollectionId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(ct => ct.Content)
                .WithMany(t => t.CollectionContent)
                .HasForeignKey(ct => ct.ContentId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<User>().HasIndex(u => u.Email).IsUnique();
    }
}
