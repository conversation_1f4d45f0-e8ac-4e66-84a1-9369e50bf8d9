using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CrateApi.Data.Models.Interfaces;

namespace CrateApi.Data.Models;

public class Challenge : ICreatedDate
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    [Required]
    [MaxLength(64)]
    public required string ChallengeValue { get; set; }

    public DateTime Created { get; set; } = DateTime.UtcNow;

    [DefaultValue(false)]
    public bool IsUsed { get; set; } = false;

    public int NfcCardId { get; set; }

    [ForeignKey("NfcCardId")]
    public virtual NfcCard? NfcCard { get; set; }
}
