using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CrateApi.Data.Models.Interfaces;

namespace CrateApi.Data.Models;


public class Track : IUserIdentifier, ICreatedDate, IUpdatedDate
{
    [Required]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int? Id { get; set; }

    [Required]
    [StringLength(256)]
    public required string TrackTitle { get; set; } 

    [Required]
    [StringLength(256)]
    public required string ArtistName { get; set; } 

    [Url]
    [DisplayName("Url")]
    public string? Url { get; set; } = "";

    [Url]
    [DisplayName("Media URL")]
    public string? MediaUrl { get; set; } = "";

    [DisplayName("Duration")]
    public int? Duration { get; set; } = null;

    [DisplayName("ISRC")]
    public string? Isrc { get; set; } = null;

    [DisplayName("Platform Type")]
    public int? PlatformType { get; set; } = null;

    [Required]
    [DisplayName("User Id")]
    public Guid? UserId { get; set; }

    [Required]
    [DisplayName("Created")]
    public DateTime Created { get; set; }

    [Required]
    [DisplayName("Updated")]
    public DateTime Updated { get; set; }
    public List<CollectionTrackMapping> CollectionTracks { get; set; } = new List<CollectionTrackMapping>();
    public List<TrackContentMapping> ContentMappings { get; set; } = new List<TrackContentMapping>();

}
