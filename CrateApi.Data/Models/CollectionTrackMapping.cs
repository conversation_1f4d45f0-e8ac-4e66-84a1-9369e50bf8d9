using System.ComponentModel.DataAnnotations;

namespace CrateApi.Data.Models;

public class CollectionContentMapping
{
    [Required]
    public int CollectionId { get; set; }

    [Required]
    public int ContentId { get; set; }
    public Collection? Collection { get; set; }
    public Content? Content { get; set; }

    public static CollectionContentMapping Create(int collectionId, int contentId) =>
        new CollectionContentMapping
        {
            ContentId = contentId,
            CollectionId = collectionId
        };
}
