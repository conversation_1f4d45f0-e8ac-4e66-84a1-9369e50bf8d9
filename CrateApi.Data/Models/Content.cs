using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.Mime;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using CrateApi.Data.Models.Interfaces;
using System.ComponentModel;

namespace CrateApi.Data.Models;

[Table("content")]
public class Content : ICreatedDate, IUpdatedDate, IUserIdentifier
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int? Id { get; set; }

    [Required]
    [StringLength(1024)]
    public required string Title { get; set; }
    public string? Detail { get; set; }
    [StringLength(2048)]
    public string? Url { get; set; }
    [StringLength(2048)]
    public string? MediaUrl { get; set; }
    public int? Platform { get; set; }
    [DisplayName("User Id")]
    public Guid? UserId { get; set; }
    public DateTime Created { get; set; }
    public DateTime Updated { get; set; }

    [Column(TypeName = "jsonb")]
    public string? Metadata { get; set; }

    public List<TrackContentMapping> TrackMappings { get; set; } = new List<TrackContentMapping>();
    public List<CollectionContentMapping> CollectionContent { get; set; } = new List<CollectionContentMapping>();
}
