using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CrateApi.Data.Models.Interfaces;
using LanguageExt;

namespace CrateApi.Data.Models;

public class User : ICreatedDate, IUpdatedDate, ILastLogin
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid? Id { get; set; }

    [Required]
    [StringLength(50)]
    public string Username { get; set; } = "";

    [Required]
    [EmailAddress]
    public string Email { get; set; } = "";

    [Required]
    public string Password { get; set; } = "";

    [StringLength(256)]
    public string? EntraSubjectId { get; set; }
    public DateTime? LastLogin { get; set; }

    public DateTime Created { get; set; } = DateTime.UtcNow;

    public DateTime Updated { get; set; } = DateTime.UtcNow;

    public static User New(string email, string user, string subjectId) =>
        new User
        {
            Email = email,
            Username = user,
            Password = Guid.NewGuid().ToString(),
            EntraSubjectId = subjectId,
            LastLogin = DateTime.UtcNow,
            Created = DateTime.UtcNow,
            Updated = DateTime.UtcNow
        };

}
