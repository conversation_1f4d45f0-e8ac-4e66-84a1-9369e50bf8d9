using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CrateApi.Data.Models.Interfaces;

namespace CrateApi.Data.Models;

public class NfcCard : ICreatedDate
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    [Required]
    [MaxLength(32)]
    public required string Uid { get; set; }

    [Required]
    [MaxLength(64)]
    public required string Atr { get; set; }

    public DateTime Created { get; set; } = DateTime.UtcNow;

    public DateTime? LastUsed { get; set; }

    [DefaultValue(true)]
    public bool IsActive { get; set; } = true;

    public virtual ICollection<Challenge> Challenges { get; set; } = new List<Challenge>();
}
