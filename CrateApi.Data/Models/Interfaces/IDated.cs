using LanguageExt;
using static LanguageExt.Prelude;

namespace CrateApi.Data.Models.Interfaces;

public interface ICreatedDate
{
    DateTime Created { get; set; }
    public static IO<Unit> AssignCreated(ICreatedDate dateObj, DateTime date) =>
        IO.lift(() => { dateObj.Created = date; return unit; });
    public static IO<Unit> AssignCreatedNow(ICreatedDate dateObj) =>
        IO.lift(() => { dateObj.Created = DateTime.UtcNow; return unit; });
}
public interface IUpdatedDate
{
    DateTime Updated { get; set; }
    public static IO<Unit> AssignUpdated(IUpdatedDate dateObj, DateTime date) =>
        IO.lift(() => { dateObj.Updated = date; return unit; });
    public static IO<Unit> AssignUpdatedNow(IUpdatedDate dateObj) =>
        IO.lift(() => { dateObj.Updated = DateTime.UtcNow; return unit; });
}
