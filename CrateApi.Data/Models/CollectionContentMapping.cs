using System.ComponentModel.DataAnnotations;

namespace CrateApi.Data.Models;

public class CollectionTrackMapping
{
    [Required]
    public int CollectionId { get; set; }

    [Required]
    public int TrackId { get; set; }
    public Collection? Collection { get; set; }
    public Track? Track { get; set; }

    public static CollectionTrackMapping Create(int collectionId, int trackId) =>
        new CollectionTrackMapping
        {
            TrackId = trackId,
            CollectionId = collectionId
        };
}
