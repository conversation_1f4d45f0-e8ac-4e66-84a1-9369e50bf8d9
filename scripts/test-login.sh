#!/bin/bash

API_URL="http://localhost:8000/api/v1"
API_KEY="crate2025"
USERNAME="<EMAIL>"
PASSWORD="CrateNFC2025"

echo "Testing login with username/password..."
response=$(curl -s -X POST \
  "$API_URL/user/login" \
  -H "ApiKey: $API_KEY" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$USERNAME\",\"password\":\"$PASSWORD\"}")

echo "$response" | jq

# Extract the access token
access_token=$(echo "$response" | jq -r '.access_token')

if [ "$access_token" != "null" ] && [ "$access_token" != "" ]; then
    echo -e "\nAccess token obtained successfully!"
else
    echo -e "\nFailed to obtain access token."
fi
