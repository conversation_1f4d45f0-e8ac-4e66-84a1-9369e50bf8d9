# /// script
# dependencies = [
#   "msal",
#   "requests",
#   "python-dotenv"
# ]
# ///

"""
resolve_groups.py

This script uses client credentials to authenticate with Microsoft Graph
and resolves Azure AD group GUIDs to their human-readable display names.

Requirements:
- A regular Azure AD tenant (not CIAM)
- App Registration with `Directory.Read.All` permission
- Valid client ID, secret, and tenant ID in `.env-microsoft-graph-client`
"""

import os
import requests
import msal
from dotenv import load_dotenv

# Load environment variables from a specific .env file
load_dotenv(".env-microsoft-graph-client")

TENANT_ID = os.getenv("MS_GRAPH_TENANT_ID")
CLIENT_ID = os.getenv("MS_GRAPH_CLIENT_ID")
CLIENT_SECRET = os.getenv("MS_GRAPH_CLIENT_SECRET")

GROUP_IDS = [
    # Replace with real group GUIDs from your token
    "feb29634-22cd-4c9a-a1e2-73c3f3931b64"
]

AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPE = ["https://graph.microsoft.com/.default"]


def acquire_token():
    """
    Authenticate to Microsoft Graph using client credentials flow
    and return a bearer access token.

    Returns:
        str: Access token string for Graph API
    Raises:
        Exception: If token acquisition fails
    """
    app = msal.ConfidentialClientApplication(
        client_id=CLIENT_ID,
        client_credential=CLIENT_SECRET,
        authority=AUTHORITY,
    )
    result = app.acquire_token_for_client(scopes=SCOPE)
    if "access_token" in result:
        return result["access_token"]
    else:
        raise Exception("Could not acquire token", result)


def resolve_group_name(group_id, token):
    """
    Resolve an Azure AD group GUID to its display name using Microsoft Graph.

    Args:
        group_id (str): Azure AD group object ID (GUID)
        token (str): Access token for Microsoft Graph API

    Returns:
        str or None: Display name of the group, or None if request fails
    """
    url = f"https://graph.microsoft.com/v1.0/groups/{group_id}"
    headers = {"Authorization": f"Bearer {token}"}
    r = requests.get(url, headers=headers)
    if r.status_code == 200:
        return r.json().get("displayName")
    else:
        print(f"❌ Error resolving {group_id}: {r.status_code}")
        return None


def list_all_groups(token):
    """
    Retrieves all Azure AD groups in the tenant.

    Args:
        token (str): Bearer token for Microsoft Graph

    Returns:
        dict[str, str]: Mapping of group ID to display name
    """
    url = "https://graph.microsoft.com/v1.0/groups?$select=id,displayName"
    headers = {"Authorization": f"Bearer {token}"}
    group_map = {}

    while url:
        r = requests.get(url, headers=headers)
        if r.status_code != 200:
            print(f"❌ Error listing groups: {r.status_code}")
            break
        data = r.json()
        for group in data.get("value", []):
            group_map[group["id"]] = group.get("displayName", "<no name>")
        url = data.get("@odata.nextLink")  # Handle pagination

    return group_map


def get_tenant_domain(token: str) -> str | None:
    """
    Retrieves the default verified domain (e.g. cratenfc.onmicrosoft.com) for use in identity filtering.

    Args:
        token (str): Access token for Microsoft Graph

    Returns:
        str or None: Tenant domain string
    """
    url = "https://graph.microsoft.com/v1.0/organization"
    headers = {"Authorization": f"Bearer {token}"}
    r = requests.get(url, headers=headers)
    if r.status_code == 200:
        domains = r.json()["value"][0]["verifiedDomains"]
        for d in domains:
            if d.get("default"):
                return d["name"]
    else:
        print(f"❌ Could not fetch tenant domain: {r.status_code}")
        return None


def resolve_upn_from_identity(email: str, tenant_domain: str, token: str) -> str | None:
    """
    Resolves UPN from an external identity (e.g. email used for login) in a CIAM tenant.

    Args:
        email (str): The external identity (e.g., <EMAIL>)
        tenant_domain (str): Your tenant issuer domain (e.g., cratenfc.onmicrosoft.com)
        token (str): Access token for Graph

    Returns:
        str or None: The user's UPN if found
    """
    url = (
        "https://graph.microsoft.com/v1.0/users"
        f"?$filter=identities/any(c:c/issuerAssignedId eq '{email}' and c/issuer eq '{tenant_domain}')"
        "&$select=userPrincipalName"
    )
    headers = {"Authorization": f"Bearer {token}"}
    r = requests.get(url, headers=headers)
    if r.status_code == 200:
        data = r.json()
        if data["value"]:
            return data["value"][0]["userPrincipalName"]
        else:
            print(f"🔍 No match found for {email}")
            return None
    else:
        print(f"❌ Could not resolve identity for {email}: {r.status_code}")
        return None


def main():
    """
    Main entry point. Acquires an access token and resolves the group IDs
    listed in the GROUP_IDS constant to their display names.
    """
    token = acquire_token()
    print("\nResolved groups:")
    for gid in GROUP_IDS:
        name = resolve_group_name(gid, token)
        print(f"{gid} → {name}")

    group_map = list_all_groups(token)
    print("\nAll groups:")
    for gid, name in group_map.items():
        print(f"{gid} → {name}")

    email = "<EMAIL>"
    tenant_domain = get_tenant_domain(token)
    upn = resolve_upn_from_identity(email, tenant_domain, token)
    print(f"\nUPN for \n{email} → {upn}")


if __name__ == "__main__":
    main()
