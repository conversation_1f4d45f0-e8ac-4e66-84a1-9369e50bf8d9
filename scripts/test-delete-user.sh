#!/bin/bash

# Get access token first
echo "🔐 Getting access token..."
TOKEN_RESPONSE=$(curl -s -X POST \
  https://cratenfc.ciamlogin.com/29026024-3977-4f6e-99ea-ebe1ecc342ae/oauth2/v2.0/token \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'client_id=6d030d81-5d4a-42d1-a4c8-76c03b13fcc4&scope=aeaa5e42-a2f3-45e0-a58d-db47c7eec9ae/access openid profile&grant_type=password&username=<EMAIL>&password=LT$zSW6uNPoY%Kur')

# Extract access token
ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.access_token')

if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" == "null" ]; then
  echo "❌ Failed to get access token"
  echo $TOKEN_RESPONSE | jq
  exit 1
fi

echo "✅ Got access token"

# First get the user profile to get the user ID
echo "👤 Getting user profile..."
PROFILE_RESPONSE=$(curl -s -X GET \
  http://192.168.1.64:8000/user/profile \
  -H "ApiKey: crate2025" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

USER_ID=$(echo $PROFILE_RESPONSE | jq -r '.userId')

if [ -z "$USER_ID" ] || [ "$USER_ID" == "null" ]; then
  echo "❌ Failed to get user ID"
  echo $PROFILE_RESPONSE | jq
  exit 1
fi

echo "✅ Got user ID: $USER_ID"

# Now delete the user
echo "🗑️ Deleting user..."
DELETE_RESPONSE=$(curl -v -X DELETE \
  "http://192.168.1.64:8000/user/$USER_ID" \
  -H "ApiKey: crate2025" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

echo "Delete response: $DELETE_RESPONSE"
echo "✅ Done"
