# /// script
# dependencies = [
#   "msal",
#   "requests",
#   "python-dotenv"
# ]
# ///

"""
ms-graph-user-management-interactive.py

This script uses interactive authentication (delegated permissions) to manage users
in Azure AD/Entra ID. It requires an admin to sign in each time, making it much more secure
than using application permissions.

Requirements:
- App Registration with DELEGATED permissions (not Application):
  - User.ReadWrite.All (Delegated)
  - Directory.ReadWrite.All (Delegated) 
- Admin user with appropriate Azure AD roles:
  - Global Administrator, User Administrator, or Privileged Authentication Administrator
- Valid client ID and tenant ID in `.env-microsoft-graph-interactive`

Usage:
    python scripts/ms-graph-user-management-interactive.py --list-all
    python scripts/ms-graph-user-management-interactive.py --delete-lilrobo-aliases
    python scripts/ms-graph-user-management-interactive.py --delete-lilrobo-aliases --confirm
"""

import os
import sys
import argparse
import requests
import msal
import re
import webbrowser
from dotenv import load_dotenv

# Load environment variables from a specific .env file
load_dotenv(".env-microsoft-graph-interactive")

TENANT_ID = os.getenv("MS_GRAPH_TENANT_ID")
CLIENT_ID = os.getenv("MS_GRAPH_CLIENT_ID")

# For interactive authentication, we don't need a client secret
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPES = [
    "https://graph.microsoft.com/User.ReadWrite.All",
    "https://graph.microsoft.com/Directory.ReadWrite.All"
]

# Redirect URI for public client (must be registered in app)
REDIRECT_URI = "http://localhost:8080"


def acquire_token_interactive():
    """
    Authenticate to Microsoft Graph using interactive flow (delegated permissions).
    This will open a browser window for the user to sign in.

    Returns:
        str: Access token string for Graph API
    Raises:
        Exception: If token acquisition fails
    """
    if not all([TENANT_ID, CLIENT_ID]):
        raise Exception("Missing required environment variables. Check .env-microsoft-graph-interactive file.")
    
    # Create a public client application (no client secret needed)
    app = msal.PublicClientApplication(
        client_id=CLIENT_ID,
        authority=AUTHORITY
    )
    
    print("🔐 Starting interactive authentication...")
    print("📱 A browser window will open for you to sign in")
    print("⚠️  Please sign in with an admin account that has user management permissions")
    
    # Try to get token silently first (from cache)
    accounts = app.get_accounts()
    if accounts:
        print("🔍 Found cached account, attempting silent authentication...")
        result = app.acquire_token_silent(SCOPES, account=accounts[0])
        if result and "access_token" in result:
            print("✅ Silent authentication successful")
            return result["access_token"]
    
    # If silent auth fails, do interactive auth
    print("🌐 Opening browser for interactive authentication...")
    result = app.acquire_token_interactive(
        scopes=SCOPES,
        redirect_uri=REDIRECT_URI
    )
    
    if "access_token" in result:
        print("✅ Interactive authentication successful")
        
        # Show who is signed in
        if "id_token_claims" in result:
            claims = result["id_token_claims"]
            name = claims.get("name", "Unknown")
            upn = claims.get("preferred_username", "Unknown")
            print(f"👤 Signed in as: {name} ({upn})")
        
        return result["access_token"]
    else:
        error = result.get("error", "Unknown error")
        error_desc = result.get("error_description", "No description")
        raise Exception(f"Authentication failed: {error} - {error_desc}")


def check_user_permissions(token):
    """
    Check if the current user has the necessary permissions to delete users.

    Args:
        token (str): Access token for Microsoft Graph API
    
    Returns:
        bool: True if user has sufficient permissions
    """
    print("🔍 Checking your permissions...")
    
    # Get current user info
    try:
        url = "https://graph.microsoft.com/v1.0/me"
        headers = {"Authorization": f"Bearer {token}"}
        r = requests.get(url, headers=headers)
        
        if r.status_code == 200:
            user_info = r.json()
            print(f"👤 Current user: {user_info.get('displayName', 'N/A')} ({user_info.get('userPrincipalName', 'N/A')})")
        else:
            print(f"⚠️  Could not get current user info: {r.status_code}")
    except Exception as e:
        print(f"⚠️  Error getting user info: {e}")
    
    # Test if we can read users
    try:
        url = "https://graph.microsoft.com/v1.0/users?$top=1"
        headers = {"Authorization": f"Bearer {token}"}
        r = requests.get(url, headers=headers)
        
        if r.status_code == 200:
            print("✅ Can read users")
        else:
            print(f"❌ Cannot read users: {r.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing read permissions: {e}")
        return False
    
    # Test if we can access directory roles (indicates admin permissions)
    try:
        url = "https://graph.microsoft.com/v1.0/me/memberOf"
        headers = {"Authorization": f"Bearer {token}"}
        r = requests.get(url, headers=headers)
        
        if r.status_code == 200:
            memberships = r.json().get('value', [])
            admin_roles = []
            
            for membership in memberships:
                if membership.get('@odata.type') == '#microsoft.graph.directoryRole':
                    role_name = membership.get('displayName', 'Unknown Role')
                    admin_roles.append(role_name)
            
            if admin_roles:
                print(f"✅ Admin roles found: {', '.join(admin_roles)}")
                return True
            else:
                print("⚠️  No admin roles found - you may not have permission to delete users")
                return False
        else:
            print(f"⚠️  Could not check admin roles: {r.status_code}")
            return False
    except Exception as e:
        print(f"⚠️  Error checking admin roles: {e}")
        return False


def list_all_users(token, limit=50):
    """
    Retrieves all users in the tenant with pagination support.

    Args:
        token (str): Bearer token for Microsoft Graph
        limit (int): Maximum number of users to retrieve

    Returns:
        list[dict]: List of user objects
    """
    url = f"https://graph.microsoft.com/v1.0/users?$select=id,userPrincipalName,displayName,mail,identities&$top={limit}"
    headers = {"Authorization": f"Bearer {token}"}
    users = []
    
    while url and len(users) < limit:
        r = requests.get(url, headers=headers)
        if r.status_code != 200:
            print(f"❌ Error listing users: {r.status_code}")
            print(r.text)
            break
        
        data = r.json()
        batch_users = data.get("value", [])
        users.extend(batch_users)
        
        # Handle pagination
        url = data.get("@odata.nextLink") if len(users) < limit else None
    
    return users[:limit]


def find_users_by_email_pattern(pattern, token, limit=100):
    """
    Find users whose email addresses match a specific regex pattern.

    Args:
        pattern (str): Regex pattern to match against email addresses
        token (str): Access token for Microsoft Graph API
        limit (int): Maximum number of users to retrieve

    Returns:
        list[dict]: List of matching user objects
    """
    all_users = list_all_users(token, limit)
    matching_users = []
    
    compiled_pattern = re.compile(pattern)
    
    for user in all_users:
        # Check userPrincipalName
        upn = user.get('userPrincipalName', '')
        if compiled_pattern.match(upn):
            matching_users.append(user)
            continue
            
        # Check mail field
        mail = user.get('mail', '')
        if mail and compiled_pattern.match(mail):
            matching_users.append(user)
            continue
            
        # Check identities for external users
        identities = user.get('identities', [])
        for identity in identities:
            assigned_id = identity.get('issuerAssignedId', '')
            if assigned_id and compiled_pattern.match(assigned_id):
                matching_users.append(user)
                break
    
    return matching_users


def delete_user(user_id, token):
    """
    Delete a user by their object ID.

    Args:
        user_id (str): User object ID to delete
        token (str): Access token for Microsoft Graph API

    Returns:
        bool: True if deletion was successful, False otherwise
    """
    url = f"https://graph.microsoft.com/v1.0/users/{user_id}"
    headers = {"Authorization": f"Bearer {token}"}
    
    r = requests.delete(url, headers=headers)
    if r.status_code == 204:
        return True
    else:
        print(f"❌ Error deleting user {user_id}: {r.status_code}")
        if r.status_code == 403:
            print("   🔒 Insufficient permissions - ensure you're signed in as an admin")
        print(f"   📄 Response: {r.text}")
        return False


def delete_lilrobo_aliases(token, dry_run=True):
    """
    Delete all users with email addresses matching the pattern <base>+<number>@lilrobo.xyz

    Args:
        token (str): Access token for Microsoft Graph API
        dry_run (bool): If True, only show what would be deleted without actually deleting

    Returns:
        int: Number of users deleted (or would be deleted in dry run)
    """
    # Check permissions first
    if not check_user_permissions(token):
        print("❌ Insufficient permissions to proceed")
        return 0
    
    # Pattern to match <base>+<number>@lilrobo.xyz
    pattern = r'^[^+]+\+\d+@lilrobo\.xyz$'
    
    print(f"🔍 Searching for users matching pattern: {pattern}")
    matching_users = find_users_by_email_pattern(pattern, token, limit=500)
    
    if not matching_users:
        print("✅ No users found matching the lilrobo.xyz alias pattern")
        return 0
    
    print(f"\n📋 Found {len(matching_users)} users matching the pattern:")
    for i, user in enumerate(matching_users, 1):
        upn = user.get('userPrincipalName', 'N/A')
        mail = user.get('mail', 'N/A')
        display_name = user.get('displayName', 'N/A')
        print(f"  {i}. {display_name} - {upn} ({mail})")
    
    if dry_run:
        print(f"\n🔍 DRY RUN: Would delete {len(matching_users)} users")
        print("Use --confirm to actually delete them")
        return len(matching_users)
    
    print(f"\n⚠️ About to delete {len(matching_users)} users!")
    print("⚠️ This action cannot be undone!")
    confirm = input("Are you absolutely sure? Type 'DELETE ALL' to confirm: ")
    
    if confirm != 'DELETE ALL':
        print("❌ Deletion cancelled")
        return 0
    
    deleted_count = 0
    for user in matching_users:
        user_id = user.get('id')
        upn = user.get('userPrincipalName', 'N/A')
        
        if delete_user(user_id, token):
            deleted_count += 1
            print(f"✅ Deleted: {upn}")
        else:
            print(f"❌ Failed to delete: {upn}")
    
    print(f"\n✅ Successfully deleted {deleted_count} out of {len(matching_users)} users")
    return deleted_count


def main():
    """
    Main entry point with command line argument parsing.
    """
    parser = argparse.ArgumentParser(description="Microsoft Graph User Management (Interactive)")
    parser.add_argument("--list-all", action="store_true", help="List all users")
    parser.add_argument("--delete-lilrobo-aliases", action="store_true", help="Delete all users with <base>+<number>@lilrobo.xyz email pattern")
    parser.add_argument("--confirm", action="store_true", help="Actually perform deletions (without this, shows dry run)")
    parser.add_argument("--check-permissions", action="store_true", help="Check current user permissions")
    parser.add_argument("--limit", type=int, default=50, help="Limit number of users to list (default: 50)")

    args = parser.parse_args()

    if not any([args.list_all, args.delete_lilrobo_aliases, args.check_permissions]):
        parser.print_help()
        return

    try:
        token = acquire_token_interactive()
        print("🔐 Successfully authenticated with Microsoft Graph")

        if args.check_permissions:
            check_user_permissions(token)

        if args.list_all:
            print(f"\n📋 Listing up to {args.limit} users:")
            users = list_all_users(token, args.limit)
            for i, user in enumerate(users, 1):
                print(f"\n{i}. {user.get('displayName', 'N/A')} ({user.get('userPrincipalName', 'N/A')})")
                print(f"   ID: {user.get('id', 'N/A')}")
                print(f"   Email: {user.get('mail', 'N/A')}")

        if args.delete_lilrobo_aliases:
            dry_run = not args.confirm
            delete_lilrobo_aliases(token, dry_run=dry_run)

    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
