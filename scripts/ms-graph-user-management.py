# /// script
# dependencies = [
#   "msal",
#   "requests",
#   "python-dotenv"
# ]
# ///

"""
ms-graph-user-management.py

This script uses client credentials to authenticate with Microsoft Graph
and provides user management capabilities including listing and deleting users
by email or Entra subject ID.

Requirements:
- A regular Azure AD tenant (not CIAM)
- App Registration with `User.ReadWrite.All` and `Directory.ReadWrite.All` permissions
- Valid client ID, secret, and tenant ID in `.env-microsoft-graph-client`

Usage:
    python scripts/ms-graph-user-management.py --list-all
    python scripts/ms-graph-user-management.py --find-email "<EMAIL>"
    python scripts/ms-graph-user-management.py --find-subject "subject-id-here"
    python scripts/ms-graph-user-management.py --delete-email "<EMAIL>"
    python scripts/ms-graph-user-management.py --delete-subject "subject-id-here"
"""

import os
import sys
import argparse
import requests
import msal
import re
from dotenv import load_dotenv

# Load environment variables from a specific .env file
load_dotenv(".env-microsoft-graph-client")

TENANT_ID = os.getenv("MS_GRAPH_TENANT_ID")
CLIENT_ID = os.getenv("MS_GRAPH_CLIENT_ID")
CLIENT_SECRET = os.getenv("MS_GRAPH_CLIENT_SECRET")

AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPE = ["https://graph.microsoft.com/.default"]


def acquire_token():
    """
    Authenticate to Microsoft Graph using client credentials flow
    and return a bearer access token.

    Returns:
        str: Access token string for Graph API
    Raises:
        Exception: If token acquisition fails
    """
    if not all([TENANT_ID, CLIENT_ID, CLIENT_SECRET]):
        raise Exception("Missing required environment variables. Check .env-microsoft-graph-client file.")
    
    app = msal.ConfidentialClientApplication(
        client_id=CLIENT_ID,
        client_credential=CLIENT_SECRET,
        authority=AUTHORITY,
    )
    result = app.acquire_token_for_client(scopes=SCOPE)
    if "access_token" in result:
        return result["access_token"]
    else:
        raise Exception("Could not acquire token", result)


def list_all_users(token, limit=50):
    """
    Retrieves all users in the tenant with pagination support.

    Args:
        token (str): Bearer token for Microsoft Graph
        limit (int): Maximum number of users to retrieve

    Returns:
        list[dict]: List of user objects
    """
    url = f"https://graph.microsoft.com/v1.0/users?$select=id,userPrincipalName,displayName,mail,identities&$top={limit}"
    headers = {"Authorization": f"Bearer {token}"}
    users = []
    
    while url and len(users) < limit:
        r = requests.get(url, headers=headers)
        if r.status_code != 200:
            print(f"❌ Error listing users: {r.status_code}")
            print(r.text)
            break
        
        data = r.json()
        batch_users = data.get("value", [])
        users.extend(batch_users)
        
        # Handle pagination
        url = data.get("@odata.nextLink") if len(users) < limit else None
    
    return users[:limit]


def find_user_by_email(email, token):
    """
    Find a user by their email address (mail or userPrincipalName).

    Args:
        email (str): Email address to search for
        token (str): Access token for Microsoft Graph API

    Returns:
        dict or None: User object if found, None otherwise
    """
    # Try searching by mail field first
    url = f"https://graph.microsoft.com/v1.0/users?$filter=mail eq '{email}' or userPrincipalName eq '{email}'&$select=id,userPrincipalName,displayName,mail,identities"
    headers = {"Authorization": f"Bearer {token}"}
    
    r = requests.get(url, headers=headers)
    if r.status_code == 200:
        data = r.json()
        if data["value"]:
            return data["value"][0]
    
    # If not found, try searching in identities for external users
    url = f"https://graph.microsoft.com/v1.0/users?$filter=identities/any(c:c/issuerAssignedId eq '{email}')&$select=id,userPrincipalName,displayName,mail,identities"
    r = requests.get(url, headers=headers)
    if r.status_code == 200:
        data = r.json()
        if data["value"]:
            return data["value"][0]
    
    return None


def find_user_by_subject_id(subject_id, token):
    """
    Find a user by their Entra subject ID (sub claim).

    Args:
        subject_id (str): Subject ID to search for
        token (str): Access token for Microsoft Graph API

    Returns:
        dict or None: User object if found, None otherwise
    """
    # For Entra External ID, the subject ID is typically the user's object ID
    url = f"https://graph.microsoft.com/v1.0/users/{subject_id}?$select=id,userPrincipalName,displayName,mail,identities"
    headers = {"Authorization": f"Bearer {token}"}
    
    r = requests.get(url, headers=headers)
    if r.status_code == 200:
        return r.json()
    elif r.status_code == 404:
        print(f"🔍 No user found with subject ID: {subject_id}")
        return None
    else:
        print(f"❌ Error searching for user: {r.status_code}")
        print(r.text)
        return None


def delete_user(user_id, token):
    """
    Delete a user by their object ID.

    Args:
        user_id (str): User object ID to delete
        token (str): Access token for Microsoft Graph API

    Returns:
        bool: True if deletion was successful, False otherwise
    """
    url = f"https://graph.microsoft.com/v1.0/users/{user_id}"
    headers = {"Authorization": f"Bearer {token}"}

    r = requests.delete(url, headers=headers)
    if r.status_code == 204:
        print(f"✅ User {user_id} deleted successfully")
        return True
    else:
        print(f"❌ Error deleting user {user_id}: {r.status_code}")
        print(r.text)
        return False


def find_users_by_email_pattern(pattern, token, limit=100):
    """
    Find users whose email addresses match a specific regex pattern.

    Args:
        pattern (str): Regex pattern to match against email addresses
        token (str): Access token for Microsoft Graph API
        limit (int): Maximum number of users to retrieve

    Returns:
        list[dict]: List of matching user objects
    """
    all_users = list_all_users(token, limit)
    matching_users = []

    compiled_pattern = re.compile(pattern)

    for user in all_users:
        # Check userPrincipalName
        upn = user.get('userPrincipalName', '')
        if compiled_pattern.match(upn):
            matching_users.append(user)
            continue

        # Check mail field
        mail = user.get('mail', '')
        if mail and compiled_pattern.match(mail):
            matching_users.append(user)
            continue

        # Check identities for external users
        identities = user.get('identities', [])
        for identity in identities:
            assigned_id = identity.get('issuerAssignedId', '')
            if assigned_id and compiled_pattern.match(assigned_id):
                matching_users.append(user)
                break

    return matching_users


def delete_lilrobo_aliases(token, dry_run=True):
    """
    Delete all users with email addresses matching the pattern <base>+<number>@lilrobo.xyz

    Args:
        token (str): Access token for Microsoft Graph API
        dry_run (bool): If True, only show what would be deleted without actually deleting

    Returns:
        int: Number of users deleted (or would be deleted in dry run)
    """
    # Pattern to match <base>+<number>@lilrobo.xyz
    pattern = r'^[^+]+\+\d+@lilrobo\.xyz$'

    print(f"🔍 Searching for users matching pattern: {pattern}")
    matching_users = find_users_by_email_pattern(pattern, token, limit=500)

    if not matching_users:
        print("✅ No users found matching the lilrobo.xyz alias pattern")
        return 0

    print(f"\n📋 Found {len(matching_users)} users matching the pattern:")
    for i, user in enumerate(matching_users, 1):
        upn = user.get('userPrincipalName', 'N/A')
        mail = user.get('mail', 'N/A')
        display_name = user.get('displayName', 'N/A')
        print(f"  {i}. {display_name} - {upn} ({mail})")

    if dry_run:
        print(f"\n🔍 DRY RUN: Would delete {len(matching_users)} users")
        print("Use --delete-lilrobo-aliases --confirm to actually delete them")
        return len(matching_users)

    print(f"\n⚠️ About to delete {len(matching_users)} users!")
    confirm = input("Are you absolutely sure? Type 'DELETE ALL' to confirm: ")

    if confirm != 'DELETE ALL':
        print("❌ Deletion cancelled")
        return 0

    deleted_count = 0
    for user in matching_users:
        user_id = user.get('id')
        upn = user.get('userPrincipalName', 'N/A')

        if delete_user(user_id, token):
            deleted_count += 1
            print(f"🗑️ Deleted: {upn}")
        else:
            print(f"❌ Failed to delete: {upn}")

    print(f"\n✅ Successfully deleted {deleted_count} out of {len(matching_users)} users")
    return deleted_count


def check_app_permissions(token):
    """
    Check what permissions the current app has by attempting various operations.

    Args:
        token (str): Access token for Microsoft Graph API
    """
    print("🔍 Checking app permissions...")

    # Test read permissions
    print("\n📖 Testing read permissions:")
    try:
        url = "https://graph.microsoft.com/v1.0/users?$top=1"
        headers = {"Authorization": f"Bearer {token}"}
        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            print("   ✅ User.Read.All - Can read users")
        else:
            print(f"   ❌ User.Read.All - Cannot read users ({r.status_code})")
    except Exception as e:
        print(f"   ❌ Error testing read permissions: {e}")

    # Test write permissions by attempting to get a specific user (safer than trying to delete)
    print("\n✏️ Testing write permissions:")
    try:
        # Try to access the service principal to check permissions
        url = f"https://graph.microsoft.com/v1.0/servicePrincipals?$filter=appId eq '{CLIENT_ID}'"
        headers = {"Authorization": f"Bearer {token}"}
        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            data = r.json()
            if data.get('value'):
                sp = data['value'][0]
                print(f"   ✅ Found service principal: {sp.get('displayName', 'N/A')}")

                # Get app role assignments
                sp_id = sp.get('id')
                url = f"https://graph.microsoft.com/v1.0/servicePrincipals/{sp_id}/appRoleAssignments"
                r = requests.get(url, headers=headers)
                if r.status_code == 200:
                    assignments = r.json().get('value', [])
                    print(f"   📋 App has {len(assignments)} role assignments")

                    # Check for specific permissions
                    user_write_found = False
                    directory_write_found = False

                    for assignment in assignments:
                        # Note: This is a simplified check - in reality you'd need to resolve the role IDs
                        print(f"   - Role ID: {assignment.get('appRoleId', 'N/A')}")

                    print("\n💡 To check exact permissions, go to Azure Portal > App registrations > Your app > API permissions")
                else:
                    print(f"   ❌ Cannot read app role assignments ({r.status_code})")
            else:
                print("   ❌ Service principal not found")
        else:
            print(f"   ❌ Cannot read service principals ({r.status_code})")
    except Exception as e:
        print(f"   ❌ Error checking permissions: {e}")

    print("\n🔧 Required permissions for user deletion:")
    print("   - User.ReadWrite.All (Application permission)")
    print("   - Directory.ReadWrite.All (Application permission, recommended)")
    print("\n📝 To add permissions:")
    print("   1. Go to Azure Portal > App registrations > Your app")
    print("   2. Go to API permissions > Add a permission")
    print("   3. Microsoft Graph > Application permissions")
    print("   4. Add User.ReadWrite.All and Directory.ReadWrite.All")
    print("   5. Grant admin consent for your organization")


def print_user_info(user):
    """
    Print formatted user information.

    Args:
        user (dict): User object from Microsoft Graph
    """
    print(f"👤 User Information:")
    print(f"   ID: {user.get('id', 'N/A')}")
    print(f"   Display Name: {user.get('displayName', 'N/A')}")
    print(f"   UPN: {user.get('userPrincipalName', 'N/A')}")
    print(f"   Email: {user.get('mail', 'N/A')}")

    identities = user.get('identities', [])
    if identities:
        print(f"   Identities:")
        for identity in identities:
            issuer = identity.get('issuer', 'N/A')
            assigned_id = identity.get('issuerAssignedId', 'N/A')
            sign_in_type = identity.get('signInType', 'N/A')
            print(f"     - {sign_in_type}: {assigned_id} (issuer: {issuer})")


def main():
    """
    Main entry point with command line argument parsing.
    """
    parser = argparse.ArgumentParser(description="Microsoft Graph User Management")
    parser.add_argument("--list-all", action="store_true", help="List all users")
    parser.add_argument("--find-email", type=str, help="Find user by email address")
    parser.add_argument("--find-subject", type=str, help="Find user by Entra subject ID")
    parser.add_argument("--delete-email", type=str, help="Delete user by email address")
    parser.add_argument("--delete-subject", type=str, help="Delete user by Entra subject ID")
    parser.add_argument("--delete-lilrobo-aliases", action="store_true", help="Delete all users with <base>+<number>@lilrobo.xyz email pattern")
    parser.add_argument("--confirm", action="store_true", help="Actually perform deletions (without this, shows dry run)")
    parser.add_argument("--check-permissions", action="store_true", help="Check what permissions the app currently has")
    parser.add_argument("--limit", type=int, default=50, help="Limit number of users to list (default: 50)")

    args = parser.parse_args()

    if not any([args.list_all, args.find_email, args.find_subject, args.delete_email, args.delete_subject, args.delete_lilrobo_aliases, args.check_permissions]):
        parser.print_help()
        return
    
    try:
        token = acquire_token()
        print("🔐 Successfully authenticated with Microsoft Graph")
        
        if args.list_all:
            print(f"\n📋 Listing up to {args.limit} users:")
            users = list_all_users(token, args.limit)
            for i, user in enumerate(users, 1):
                print(f"\n{i}. {user.get('displayName', 'N/A')} ({user.get('userPrincipalName', 'N/A')})")
                print(f"   ID: {user.get('id', 'N/A')}")
                print(f"   Email: {user.get('mail', 'N/A')}")
        
        if args.find_email:
            print(f"\n🔍 Searching for user with email: {args.find_email}")
            user = find_user_by_email(args.find_email, token)
            if user:
                print_user_info(user)
            else:
                print(f"❌ No user found with email: {args.find_email}")
        
        if args.find_subject:
            print(f"\n🔍 Searching for user with subject ID: {args.find_subject}")
            user = find_user_by_subject_id(args.find_subject, token)
            if user:
                print_user_info(user)
        
        if args.delete_email:
            print(f"\n🗑️ Deleting user with email: {args.delete_email}")
            user = find_user_by_email(args.delete_email, token)
            if user:
                print_user_info(user)
                confirm = input(f"\n⚠️ Are you sure you want to delete this user? (yes/no): ")
                if confirm.lower() == 'yes':
                    delete_user(user['id'], token)
                else:
                    print("❌ Deletion cancelled")
            else:
                print(f"❌ No user found with email: {args.delete_email}")
        
        if args.delete_subject:
            print(f"\n🗑️ Deleting user with subject ID: {args.delete_subject}")
            user = find_user_by_subject_id(args.delete_subject, token)
            if user:
                print_user_info(user)
                confirm = input(f"\n⚠️ Are you sure you want to delete this user? (yes/no): ")
                if confirm.lower() == 'yes':
                    delete_user(user['id'], token)
                else:
                    print("❌ Deletion cancelled")

        if args.delete_lilrobo_aliases:
            dry_run = not args.confirm
            delete_lilrobo_aliases(token, dry_run=dry_run)

        if args.check_permissions:
            check_app_permissions(token)

    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
