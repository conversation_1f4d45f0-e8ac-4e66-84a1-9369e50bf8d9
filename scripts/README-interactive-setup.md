# Interactive Microsoft Graph User Management Setup

This guide shows how to set up the secure interactive version of the user management script that requires admin sign-in each time.

## 🔐 Security Benefits

- **No stored secrets**: No client secret required
- **Admin authentication**: Requires actual admin to sign in each time  
- **Delegated permissions**: Uses the admin's permissions, not app permissions
- **MFA support**: Works with multi-factor authentication
- **Role-based**: Only works for users with appropriate Azure AD roles

## 🛠️ Setup Steps

### Step 1: App Registration Configuration

1. **Go to Azure Portal**: https://portal.azure.com
2. **Navigate to**: Azure Active Directory > App registrations
3. **Select your app** (or create a new one for admin operations)

### Step 2: Configure Authentication

1. **Go to**: Authentication > Platform configurations
2. **Add a platform**: Mobile and desktop applications
3. **Add redirect URI**: `http://localhost:8080`
4. **Save** the configuration

### Step 3: Configure API Permissions

1. **Go to**: API permissions
2. **Remove any Application permissions** (if present)
3. **Add permission** > Microsoft Graph > **Delegated permissions**
4. **Add these delegated permissions**:
   - `User.ReadWrite.All`
   - `Directory.ReadWrite.All`
5. **Grant admin consent** for your organization

### Step 4: Environment Configuration

1. **Copy the example file**:
   ```bash
   cp .env-microsoft-graph-interactive.example .env-microsoft-graph-interactive
   ```

2. **Edit the file** and add your values:
   ```
   MS_GRAPH_TENANT_ID=your-tenant-id-here
   MS_GRAPH_CLIENT_ID=your-client-id-here
   ```

### Step 5: User Role Requirements

The person running the script must have one of these Azure AD roles:
- **Global Administrator**
- **User Administrator**
- **Privileged Authentication Administrator**

## 🚀 Usage

### Check Your Permissions
```bash
uv run scripts/ms-graph-user-management-interactive.py --check-permissions
```

### List Users (Test)
```bash
uv run scripts/ms-graph-user-management-interactive.py --list-all
```

### Delete Lilrobo Aliases (Dry Run)
```bash
uv run scripts/ms-graph-user-management-interactive.py --delete-lilrobo-aliases
```

### Delete Lilrobo Aliases (Actual Deletion)
```bash
uv run scripts/ms-graph-user-management-interactive.py --delete-lilrobo-aliases --confirm
```

## 🔄 Authentication Flow

1. **Script starts** and checks for cached tokens
2. **Browser opens** for authentication (if no valid cache)
3. **Admin signs in** with their credentials (MFA supported)
4. **Permissions checked** to ensure admin has required roles
5. **Operations performed** using admin's delegated permissions
6. **Token cached** for convenience during session

## 🛡️ Security Features

- **Interactive Authentication**: Browser-based sign-in each time
- **Permission Validation**: Checks if user has admin roles
- **Delegated Permissions**: Uses signed-in user's permissions
- **No Stored Secrets**: No client secret in configuration
- **MFA Compatible**: Works with multi-factor authentication
- **Session Caching**: Tokens cached temporarily for convenience

## 🔧 Troubleshooting

### "Authentication failed"
- Ensure redirect URI `http://localhost:8080` is configured
- Check that the app registration allows public client flows
- Verify the tenant ID and client ID are correct

### "Insufficient privileges"
- Ensure the signed-in user has admin roles
- Check that delegated permissions are granted with admin consent
- Verify you're signing in with an admin account, not a regular user

### "Cannot read users"
- Ensure `User.ReadWrite.All` delegated permission is granted
- Check that admin consent was provided
- Verify the user has appropriate Azure AD roles

### Browser doesn't open
- Check if port 8080 is available
- Try running from a different terminal
- Ensure you have a default browser configured

## 📋 Comparison: Application vs Delegated Permissions

| Aspect | Application Permissions | Delegated Permissions (This Script) |
|--------|------------------------|-------------------------------------|
| **Security** | ❌ Anyone with app credentials | ✅ Requires admin sign-in |
| **Secrets** | ❌ Client secret required | ✅ No secrets needed |
| **User Context** | ❌ App identity | ✅ Actual admin user |
| **MFA** | ❌ Not supported | ✅ Fully supported |
| **Audit Trail** | ❌ Shows app name | ✅ Shows actual admin user |
| **Convenience** | ✅ Automated | ⚠️ Requires interaction |

The interactive approach is significantly more secure for admin operations!
