# Microsoft Graph User Management Script

This script provides user management capabilities for Azure AD/Entra ID tenants using Microsoft Graph API.

## Features

- **List Users**: Display all users in the tenant with pagination
- **Find by Email**: Search for users by email address (supports both regular and external identities)
- **Find by Subject ID**: Search for users by their Entra subject ID (sub claim)
- **Delete Users**: Safely delete users with confirmation prompts

## Setup

### 1. App Registration Setup

1. Go to [Azure Portal](https://portal.azure.com) > Azure Active Directory > App registrations
2. Create a new registration or use an existing one
3. Note down the **Application (client) ID** and **Directory (tenant) ID**

### 2. API Permissions

Add the following **Application permissions** (not Delegated):
- `User.ReadWrite.All` - To read and delete users
- `Directory.ReadWrite.All` - To read directory information

**Important**: Grant admin consent for your organization after adding permissions.

### 3. Client Secret

1. Go to **Certificates & secrets** > **New client secret**
2. Create a secret and copy the **Value** (not the Secret ID)

### 4. Environment Configuration

1. Copy `.env-microsoft-graph-client.example` to `.env-microsoft-graph-client`
2. Fill in your values:
   ```
   MS_GRAPH_TENANT_ID=your-tenant-id-here
   MS_GRAPH_CLIENT_ID=your-client-id-here
   MS_GRAPH_CLIENT_SECRET=your-client-secret-here
   ```

## Usage

### List All Users
```bash
python scripts/ms-graph-user-management.py --list-all
python scripts/ms-graph-user-management.py --list-all --limit 100
```

### Find User by Email
```bash
python scripts/ms-graph-user-management.py --find-email "<EMAIL>"
python scripts/ms-graph-user-management.py --find-email "<EMAIL>"
```

### Find User by Subject ID
```bash
python scripts/ms-graph-user-management.py --find-subject "12345678-1234-1234-1234-123456789abc"
```

### Delete User by Email
```bash
python scripts/ms-graph-user-management.py --delete-email "<EMAIL>"
```

### Delete User by Subject ID
```bash
python scripts/ms-graph-user-management.py --delete-subject "12345678-1234-1234-1234-123456789abc"
```

## Safety Features

- **Confirmation Prompts**: All delete operations require explicit confirmation
- **User Information Display**: Shows full user details before deletion
- **Error Handling**: Comprehensive error messages and status codes
- **Pagination**: Handles large user lists efficiently

## Notes

- **Subject ID**: In Entra External ID, the subject ID (sub claim) is typically the user's object ID in Azure AD
- **External Users**: The script can find users by their external email addresses (e.g., Gmail, Outlook)
- **Permissions**: Requires high-privilege permissions - use with caution in production environments
- **Rate Limits**: Microsoft Graph has rate limits - the script handles basic pagination but doesn't implement retry logic

## Troubleshooting

### Common Errors

1. **"Missing required environment variables"**
   - Ensure `.env-microsoft-graph-client` file exists and contains all required values

2. **"Could not acquire token"**
   - Check your client ID, secret, and tenant ID
   - Verify the app registration exists and is properly configured

3. **"Insufficient privileges"**
   - Ensure the app has `User.ReadWrite.All` and `Directory.ReadWrite.All` permissions
   - Verify admin consent has been granted

4. **"User not found"**
   - Try different search methods (email vs subject ID)
   - Check if the user exists in the correct tenant

## Security Considerations

- Store the `.env-microsoft-graph-client` file securely and never commit it to version control
- Use least-privilege principles - only grant necessary permissions
- Consider using managed identities in production environments
- Regularly rotate client secrets
- Monitor and audit user deletion activities
