# Client-side Unfurling and Backend Track Service, Content Table Migration

The app uses a two-pronged approach for unfurling URLs: client-side unfurling for immediate UI display and a backend service for server-side processing.

Unfurled tracks are saved to the `tracks` Table in the backend, and an db `id` is returned to client for subsequent usage (ie: adding to Collections).

During the initial development of the App and Backend, over-specialization of Spotify Tracks has been introduced. CrateNFC supports writing any URL to NFC, _NOT JUST SPOTIFY TRACKS_.

This includes different resource types:

- Track
- Album
- Playlist
- Artist

and other platforms that may include videos, podcasts, ebooks, and webpages.

The main goal behind using Tracks was to display Song Title, Artist Name, and Album Cover. This can be generalized in the UI as Title, Detail, Media. As of writing CrateNFC does support unfurling urls in a generic way and displaying the items in correct fields.

Moving forward we should fix and generalize the data model and supporting classes for the more general case, adopting the core table `Content`.

## Client-side Unfurling

The UnfurlService handles client-side unfurling by:

```swift
// CrateServices/Services/UnfurlService.swift
public func unfurl(url: URL) async throws -> Track {
  guard let host = url.host else {
    return try await defaultUnfurl(url: url, doc: Document(""))
  }

  let doc = try await fetchHTML(url: url)

  for (key, handler) in unfurlHandlers where host.contains(key) {
    return try await handler(url, doc)
  }

  return try await defaultUnfurl(url: url, doc: doc)
}
```

It has specialized handlers for different music platforms (Spotify, Apple Music, SoundXYZ, etc.) that extract metadata from HTML and Open Graph tags.

## Backend Track Service

The Swift uses TrackService to send unfurled URLs to the backend, for further unfurling. This could _(and should)_ in the future include the client-side-unfurled payload details. Server-side can handle integrity checking as needed (ie: user-provided data).

```swift
// CrateServices/Services/TrackService.swift
public func unfurl(url: String) async throws {
  let request = UrlRequest(url: url)
  if userState.isSignedIn {
    try await client.postWithoutResponse(
      path: "/api/v1/unfurl",
      body: request
    )
  } else {
    try await client.postWithoutResponse(
      path: "/api/v1/unfurl/anonymous",
      body: request
    )
  }
}
```

## How They Work Together

In the WriteViewModel, both services are used:

```swift
// CrateNFC/App/CrateNFC/Views/Write/WriteViewModel.swift
public func fetchTrackInfo() async {
  do {
    state = .fetching
    guard let url = URL(string: viewState.currentURL) else {
      state = .error("Invalid URL")
      return
    }

    // Use client-side unfurl ONLY.
    let trackData = try await unfurlService.unfurl(url: url)
    viewState.imageURL = URL(string: trackData.imgUrl ?? "")
    state = .loaded(trackData)

    // Still send to backend, but don't use the result for UI.
    try? await trackService.unfurl(url: viewState.currentURL)
  } catch {
    print("Error: \(error)")
    state = .error(error.localizedDescription)
  }
}
```

The app prioritizes client-side unfurling for UI display while still sending data to the backend for persistent storage, analytics, and other server-side processing.

## Client Side Unfurling Motivation

- **Bot Detection Bypass:** Client-side unfurling helps bypass bot detection mechanisms that many music platforms implement to prevent server-to-server scraping.
- **Reliability:** By performing the unfurling directly from the user's device, the app appears as a regular browser request with proper user-agent strings and cookies, reducing the chance of being blocked.
- **Reduced Latency:** Client-side processing eliminates an extra network hop (client → your server → music platform), providing faster results to users.
- **Fallback Mechanism:** If the client-side unfurling fails for any reason, the backend could potentially provide a fallback (though currently the UI only uses client-side results). (or the other way around: Serverside could provide cached unfurl).
- **Analytics Preservation:** By still sending the URL to the backend you maintain analytics data and server-side processing capabilities.

This architecture effectively balances user experience (speed, reliability) with backend needs (data collection, analytics) while working around the limitations imposed by music platforms' anti-scraping measures.

# Moving to Content Table Refactoring

The current Track model is too specialized for the app's needs. It should be generalized to support a wider range of content types and platforms. This will enable CrateNFC to handle not just music but also videos, articles, podcasts, and more.

## Server-side API / DB

We'll want to introduce a new set of tables and API endpoints, to support the different types of `Content`.

## Swift App

## 1. Core Model Refactoring

```swift
// CrateServices/Models/Content.swift
import Foundation
import SwiftData

@Model
public class Content {
    // Core properties
    @Attribute(.unique) public var id: String
    public var url: String?
    public var title: String?
    public var detail: String?
    public var imgUrl: String?

    // Metadata
    public var platform: String?  // spotify, apple_music, youtube, etc.
    public var contentType: ContentType
    public var description: String?
    public var createdAt: Date
    public var updatedAt: Date

    public enum ContentType: String, Codable {
        case track
        case album
        case playlist
        case profile
        case article
        case video
        case podcast
        case other
    }

    public init(
        title: String? = nil,
        detail: String? = nil,
        imgUrl: String? = nil,
        url: String? = nil,
        platform: String? = nil,
        contentType: ContentType = .other,
        description: String? = nil,
        duration: Int? = nil,
        itemCount: Int? = nil,
        updatedAt: Date = Date(),
        createdAt: Date = Date()
    ) {
        self.id = UUID().uuidString
        self.title = title
        self.detail = detail
        self.imgUrl = imgUrl
        self.url = url
        self.platform = platform
        self.contentType = contentType
        self.description = description
        self.duration = duration
        self.itemCount = itemCount
        self.updatedAt = updatedAt
        self.createdAt = createdAt
    }
}
```

## 2. Update UnfurlService

We can add unfurl support to detect the content type.
Though this may need some integer translation for the backend.

```swift
public enum ContentType: String, Codable {
        case track
        case album
        case playlist
        case profile
        case article
        case video
        case podcast
        case other
    }
```

Consider:

```swift
// CrateServices/Services/UnfurlService.swift
public protocol UnfurlServiceProtocol {
  func unfurl(url: URL) async throws -> Content
}

public class UnfurlService: UnfurlServiceProtocol {
  // ...

  private func detectContentType(url: URL, doc: Document) -> Content.ContentType {
    let path = url.path.lowercased()
    let host = url.host ?? ""

    // Spotify detection
    if host.contains("spotify") {
      if path.contains("/track/") { return .track }
      if path.contains("/album/") { return .album }
      if path.contains("/playlist/") { return .playlist }
      if path.contains("/artist/") || path.contains("/user/") { return .profile }
    }

    // YouTube detection
    if host.contains("youtube") || host.contains("youtu.be") {
      if path.contains("/playlist") { return .playlist }
      if path.contains("/channel/") || path.contains("/c/") { return .profile }
      return .video // Default for YouTube
    }

    // Default detection based on OG tags
    if let type = try? doc.select("meta[property=og:type]").first()?.attr("content") {
      switch type {
      case "music.song": return .track
      case "music.album": return .album
      case "music.playlist": return .playlist
      case "profile": return .profile
      case "article": return .article
      case "video": return .video
      default: break
      }
    }

    return .other
  }

  private func detectPlatform(url: URL) -> String? {
    if let host = url.host?.lowercased() {
      if host.contains("spotify") { return "spotify" }
      if host.contains("apple.com/music") { return "apple_music" }
      if host.contains("youtube") || host.contains("youtu.be") { return "youtube" }
      if host.contains("soundcloud") { return "soundcloud" }
      if host.contains("pandora") { return "pandora" }
      // Add more platforms as needed

      // Extract domain for unknown platforms
      let components = host.split(separator: ".")
      if components.count >= 2 {
        return String(components[components.count - 2])
      }
    }
    return nil
  }

  // Update unfurl methods to return Content
  public func unfurl(url: URL) async throws -> Content {
    // ...existing code...

    let doc = try await fetchHTML(url: url)
    let contentType = detectContentType(url: url, doc: doc)
    let platform = detectPlatform(url: url)

    // Use existing handlers but convert Track to Content
    for (key, handler) in unfurlHandlers where host.contains(key) {
      let track = try await handler(url, doc)
      return convertTrackToContent(track, contentType: contentType, platform: platform)
    }

    let track = try await defaultUnfurl(url: url, doc: doc)
    return convertTrackToContent(track, contentType: contentType, platform: platform)
  }

  private func convertTrackToContent(_track: Track, contentType: Content.ContentType, platform: String?) -> Content {
    return Content(
      title: track.name,
      detail: track.artist,
      imgUrl: track.imgUrl,
      url: track.url,
      platform: platform,
      contentType: contentType,
      updatedAt: track.updatedAt,
      createdAt: track.createdAt
    )
  }
}
```

## 3. Update WriteViewModel

```swift
// CrateNFC/App/CrateNFC/Views/Write/WriteViewModel.swift
public enum WriteViewState {
  case empty
  case fetching
  case loaded(Content)
  case error(String)
}

@MainActor
public final class WriteViewModel: ObservableObject {
  // ...existing code...

  public func fetchContentInfo() async {
    do {
      state = .fetching
      guard let url = URL(string: viewState.currentURL) else {
        state = .error("Invalid URL")
        return
      }

      // Use client-side unfurl ONLY.
      let content = try await unfurlService.unfurl(url: url)
      viewState.imageURL = URL(string: content.imgUrl ?? "")
      viewState.platformImage = content.platform
      state = .loaded(content)

      // Still send to backend, but don't use the result for UI.
      try? await trackService.unfurl(url: viewState.currentURL)
    } catch {
      print("Error: \(error)")
      state = .error(error.localizedDescription)
    }
  }

  public func handleWriteButtonTapped() {
    let content = createContentFromCurrentState()
    viewState.lastWrittenContent = content

    // ...existing code to save to RecentCollection...

    writeNFCMessage(content)
  }

  private func createContentFromCurrentState() -> Content {
    switch state {
    case let .loaded(existingContent):
      return Content(
        title: existingContent.title,
        detail: existingContent.detail,
        imgUrl: viewState.imageURL?.absoluteString,
        url: viewState.enteredURL,
        platform: viewState.platformImage,
        contentType: existingContent.contentType,
        description: existingContent.description,
        duration: existingContent.duration,
        itemCount: existingContent.itemCount,
        updatedAt: Date(),
        createdAt: Date()
      )
    case .empty, .fetching, .error:
      return Content(
        url: viewState.enteredURL,
        platform: viewState.platformImage,
        contentType: .other,
        updatedAt: Date(),
        createdAt: Date()
      )
    }
  }

  public func writeNFCMessage(_ content: Content) {
    let messages: [(String, String)] = [
      (content.url ?? "", "U"),
      (createMetadataJSONFromContent(content), "T")
    ]
    print("Starting NFC write with messages: \(messages)")
    nfcWriter.write(messages)
  }

  public func createMetadataJSONFromContent(_ content: Content) -> String {
    var metadata: [String: String] = [:]
    metadata["created"] = ISO8601DateFormatter().string(from: Date())
    metadata["type"] = content.contentType.rawValue

    if showExtraDetails {
      if let detail = content.detail, !detail.isEmpty {
        metadata["detail"] = detail
      }
      if let title = content.title, !title.isEmpty {
        metadata["title"] = title
      }
      if !customCreator.isEmpty {
        metadata["writer"] = customCreator
      }
      if let imgUrl = content.imgUrl, !imgUrl.isEmpty {
        metadata["imgUrl"] = imgUrl
      }
      if let platform = content.platform, !platform.isEmpty {
        metadata["platform"] = platform
      }
      if let description = content.description, !description.isEmpty {
        metadata["description"] = description
      }
    }

    // ...existing JSON serialization code...
  }
}
```

## 4. Update UI Components - Badges

```swift
// CrateNFC/App/CrateNFC/Views/Write/ContentPreviewView.swift
struct ContentPreviewView: View {
  let content: Content

  var body: some View {
    VStack(alignment: .leading) {
      HStack {
        // Platform icon
        if let platform = content.platform {
          Image(platform)
            .resizable()
            .scaledToFit()
            .frame(width: 24, height: 24)
        }

        // Content type badge
        Text(content.contentType.rawValue.capitalized)
          .font(.caption)
          .padding(.horizontal, 8)
          .padding(.vertical, 4)
          .background(contentTypeColor(content.contentType))
          .foregroundColor(.white)
          .cornerRadius(8)
      }

      // Title and detail
      if let title = content.title {
        Text(title)
          .font(.headline)
          .lineLimit(2)
      }

      if let detail = content.detail {
        Text(detail)
          .font(.subheadline)
          .foregroundColor(.secondary)
      }

      // Additional metadata based on content type
      if let itemCount = content.itemCount, content.contentType == .playlist || content.contentType == .album {
        Text("\(itemCount) items")
          .font(.caption)
          .foregroundColor(.secondary)
      }

      if let duration = content.duration, content.contentType == .track || content.contentType == .video {
        Text(formatDuration(duration))
          .font(.caption)
          .foregroundColor(.secondary)
      }
    }
  }

  private func contentTypeColor(_ type: Content.ContentType) -> Color {
    switch type {
    case .track: return .blue
    case .album: return .purple
    case .playlist: return .green
    case .profile: return .orange
    case .article: return .gray
    case .video: return .red
    case .podcast: return .pink
    case .other: return .secondary
    }
  }

  private func formatDuration(_ seconds: Int) -> String {
    let minutes = seconds / 60
    let remainingSeconds = seconds % 60
    return "\(minutes):\(String(format: "%02d", remainingSeconds))"
  }
}
```

## 5. Migration Plan

1. Create the new Content model
2. Update the UnfurlService to support both old and new models during transition
3. Gradually update UI components to use the new model
4. Create a migration utility to convert existing Track data to Content
5. Update the NFC writing/reading logic to handle the new metadata format
6. Once all components are updated, remove the old Track model

This approach allows for a phased transition while maintaining backward compatibility with existing NFC tags.

**awbuga:**
Can you write me a quick summary about this?

**Augment:**

## Content Approach Summary

The plan transforms CrateNFC from a song-centric app to a universal digital content platform by:

- **Creating a flexible Content model** that replaces the current Track model, adding:
  - Content type classification (track, album, playlist, profile, video, etc.)
  - Platform identification (Spotify, YouTube, Apple Music, etc.)
  - Additional metadata fields for different content types
- **Enhancing the UnfurlService to:**
  - Automatically detect content types from URLs
  - Extract platform-specific information
  - Support richer metadata for various digital content formats
- **Updating the UI to:**
  - Display content-specific information
  - Use visual indicators for different content types
  - Show platform badges for better user context
- **Improving NFC writing to:**
  - Store content type in the metadata
  - Include platform information
  - Support additional content-specific details

This approach maintains backward compatibility while expanding CrateNFC's capabilities to handle virtually any URL-based digital content, making the app more versatile and future-proof.

**awbuga:**
You output the Summary, and example changes into a single markdown document so I can write a github issue?

**Augment:**

## Content Approach for CrateNFC

### Summary

The plan transforms CrateNFC from a song-centric app to a universal digital content platform by:

- **Creating a flexible Content model** that replaces the current Track model, adding:
  - Content type classification (track, album, playlist, profile, video, etc.)
  - Platform identification (Spotify, YouTube, Apple Music, etc.)
  - Additional metadata fields for different content types
- **Enhancing the UnfurlService to:**
  - Automatically detect content types from URLs
  - Extract platform-specific information
  - Support richer metadata for various digital content formats
- **Updating the UI to:**
  - Display content-specific information
  - Use visual indicators for different content types
  - Show platform badges for better user context
- **Improving NFC writing to:**
  - Store content type in the metadata
  - Include platform information
  - Support additional content-specific details

This approach maintains backward compatibility while expanding CrateNFC's capabilities to handle virtually any URL-based digital content, making the app more versatile and future-proof.

### Example Changes

### 1. New Content Model

```swift
// CrateNFC/App/CrateServices/Models/Content.swift
import Foundation
import SwiftData

@Model
public class Content {
    public var name: String?
    public var detail: String?
    public var imgUrl: String?
    public var url: String?
    public var platform: String?
    public var contentType: ContentType
    public var metadata: [String: String]?
    public var updatedAt: Date
    public var createdAt: Date

    public enum ContentType: String, Codable {
        case track
        case album
        case playlist
        case profile
        case video
        case article
        case other
    }

    public init(
        name: String? = nil,
        detail: String? = nil,
        imgUrl: String? = nil,
        url: String? = nil,
        platform: String? = nil,
        contentType: ContentType = .track,
        metadata: [String: String]? = nil,
        updatedAt: Date = Date(),
        createdAt: Date = Date()
    ) {
        self.name = name
        self.detail = detail
        self.imgUrl = imgUrl
        self.url = url
        self.platform = platform
        self.contentType = contentType
        self.metadata = metadata
        self.updatedAt = updatedAt
        self.createdAt = createdAt
    }
}
```

### 2. Enhanced UnfurlService

```swift
// CrateNFC/App/CrateServices/Services/UnfurlService.swift
public protocol UnfurlServiceProtocol {
    func unfurl(url: URL) async throws -> Content
}

public class UnfurlService: UnfurlServiceProtocol {
    private let httpClient: HTTPClient

    public init(httpClient: HTTPClient) {
        self.httpClient = httpClient
    }

    public func unfurl(url: URL) async throws -> Content {
        // Detect platform and content type from URL
        let (platform, contentType) = detectPlatformAndType(from: url)

        // Extract metadata based on platform and content type
        let metadata = try await extractMetadata(from: url, platform: platform, contentType: contentType)

        return Content(
            name: metadata["title"],
            detail: metadata["detail"],
            imgUrl: metadata["image"],
            url: url.absoluteString,
            platform: platform,
            contentType: contentType,
            metadata: metadata
        )
    }

    private func detectPlatformAndType(from url: URL) -> (String, Content.ContentType) {
        let urlString = url.absoluteString.lowercased()

        // Platform detection
        if urlString.contains("spotify.com") {
            let platform = "spotify"
            // Content type detection for Spotify
            if urlString.contains("/track/") {
                return (platform, .track)
            } else if urlString.contains("/album/") {
                return (platform, .album)
            } else if urlString.contains("/playlist/") {
                return (platform, .playlist)
            } else if urlString.contains("/artist/") {
                return (platform, .profile)
            }
            return (platform, .other)
        }

        // Add more platform detection logic...

        return ("unknown", .other)
    }

    private func extractMetadata(from url: URL, platform: String, contentType: Content.ContentType) async throws -> [String: String] {
        // Platform-specific metadata extraction
        // ...
    }
}
```

### 3. Updated WriteViewModel

```swift
// CrateNFC/App/CrateNFC/Views/Write/WriteViewModel.swift
@MainActor
public final class WriteViewModel: ObservableObject {
    @Published public var viewState = ViewState()
    @Published public var state: WriteViewState = .empty
    @Published public var activeFlags: Set<UIFlags> = []
    @Published public var nfcWriter = NFCWriter()
    @Published public var showExtraDetails: Bool = false
    @Published public var customCreator: String = "lilrobo"

    private let unfurlService: UnfurlServiceProtocol
    private let contentService: ContentServiceProtocol
    public var deepLinkHandler: DeepLinkHandler
    private let modelContext: ModelContext

    // ... existing code ...

    public func fetchContentInfo() async {
        do {
            state = .fetching
            guard let url = URL(string: viewState.currentURL) else {
                state = .error("Invalid URL")
                return
            }

            let content = try await unfurlService.unfurl(url: url)
            viewState.imageURL = URL(string: content.imgUrl ?? "")
            viewState.platformIcon = content.platform
            viewState.contentType = content.contentType
            state = .loaded(content)

            // Still send to backend, but don't use the result for UI
            try? await contentService.unfurl(url: viewState.currentURL)
        } catch {
            print("Error: \(error)")
            state = .error(error.localizedDescription)
        }
    }

    public func createMetadataJSONFromContent(_ content: Content) -> String {
        var metadata: [String: String] = [:]
        metadata["created"] = ISO8601DateFormatter().string(from: Date())
        metadata["contentType"] = content.contentType.rawValue
        metadata["platform"] = content.platform ?? "unknown"

        if showExtraDetails {
            if let detail = content.detail, !detail.isEmpty {
                metadata["detail"] = detail
            }
            if let name = content.name, !name.isEmpty {
                metadata["title"] = name
            }
            if !customCreator.isEmpty {
                metadata["nfcCreator"] = customCreator
            }
            if let imgUrl = content.imgUrl, !imgUrl.isEmpty {
                metadata["imgUrl"] = imgUrl
            }

            // Add any additional content-specific metadata
            content.metadata?.forEach { key, value in
                if !key.isEmpty && !value.isEmpty {
                    metadata[key] = value
                }
            }
        }

        guard let jsonData = try? JSONSerialization.data(withJSONObject: metadata),
              let jsonString = String(data: jsonData, encoding: .utf8)
        else {
            print("Error creating metadata JSON")
            return "{}"
        }

        print("Writing metadata: \(jsonString)")
        return jsonString
    }
}
```

### 4. Updated UI Components

```swift
// CrateNFC/App/CrateNFC/Views/Write/ContentPreviewView.swift
struct ContentPreviewView: View {
    let content: Content

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                // Platform icon
                if let platform = content.platform {
                    Image(platform)
                        .resizable()
                        .scaledToFit()
                        .frame(width: 24, height: 24)
                }

                // Content type badge
                ContentTypeBadge(type: content.contentType)

                Spacer()
            }

            // Content image
            if let imageUrlString = content.imgUrl, let imageUrl = URL(string: imageUrlString) {
                AsyncImage(url: imageUrl) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .foregroundColor(.gray.opacity(0.3))
                }
                .frame(height: 180)
                .cornerRadius(8)
            }

            // Content title
            if let name = content.name {
                Text(name)
                    .font(.headline)
                    .lineLimit(2)
            }

            // Detail information
            if let detail = content.detail {
                Text(detail)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            // Additional metadata based on content type
            if let metadata = content.metadata {
                ContentTypeSpecificInfo(contentType: content.contentType, metadata: metadata)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct ContentTypeBadge: View {
    let type: Content.ContentType

    var body: some View {
        Text(type.rawValue.capitalized)
            .font(.caption)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(badgeColor)
            .foregroundColor(.white)
            .cornerRadius(12)
    }

    var badgeColor: Color {
        switch type {
        case .track: return .blue
        case .album: return .purple
        case .playlist: return .green
        case .profile: return .orange
        case .video: return .red
        case .article: return .gray
        case .other: return .secondary
        }
    }
}
```

### Migration Plan

1. Create the new Content model alongside the existing Track model
2. Update the UnfurlService to return Content objects
3. Add a compatibility layer to convert between Track and Content
4. Update UI components to display the new content types
5. Update the NFC writing functionality to include the new metadata
6. Test with various content types and platforms
7. Once stable, deprecate the Track model and complete the migration

### Benefits

- Supports a wider range of digital content beyond just music tracks
- Provides better context to users about the content they're interacting with
- Makes the app more versatile and future-proof
- Improves the user experience with content-specific displays and information
- Maintains backward compatibility with existing NFC tags
