# PostgreSQL Database Design for Enum-like Content Types

Handling database tables in PostgreSQL when your frontend uses enum-like types (e.g., `Content.type` with values like song, album, playlist, etc.) depends on your application's requirements for scalability, flexibility, and performance. Below, I'll outline the best approaches to model this in PostgreSQL, considering your use case, and discuss whether maintaining multiple tables or using a single table with joins is optimal.

## Key Considerations

- **Data Structure**: Are the different types (song, album, etc.) structurally similar (same attributes) or do they have unique fields? For example, does a song have a duration, but a playlist has a track_count?

- **Query Patterns**: How will the frontend query the data? Will it often fetch all Content regardless of type, or mostly type-specific data?

- **Scalability**: Will the number of types grow over time? Will the schema for each type evolve?

- **Frontend Enum**: The frontend's enum suggests a controlled set of types, so the database should enforce this constraint reliably.

## Recommended Approaches

Here are the primary ways to handle this scenario in PostgreSQL, with pros and cons:

### 1. Single Table with Enum Type (Preferred for Simplicity)

Use a single Content table with a PostgreSQL ENUM type for type, and store type-specific attributes in shared or flexible columns.

#### Schema Example

```sql
CREATE TYPE content_type AS ENUM ('song', 'album', 'playlist', 'profile', 'webpage', 'video', 'other');

CREATE TABLE content (
    id SERIAL PRIMARY KEY,
    type content_type NOT NULL,
    title VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Shared attributes
    metadata JSONB, -- For type-specific attributes
    -- Other common fields
    CONSTRAINT valid_metadata CHECK (
        CASE
            WHEN type = 'song' THEN metadata ? 'duration'
            WHEN type = 'album' THEN metadata ? 'track_count'
            -- Add other type-specific checks
            ELSE TRUE
        END
    )
);
```

#### How It Works

- The `type` column uses a PostgreSQL ENUM to enforce valid values, mirroring the frontend's enum.

- Common attributes (e.g., `title`, `created_at`) are stored in dedicated columns.

- Type-specific attributes (e.g., duration for song, track_count for album) are stored in a JSONB column (`metadata`) for flexibility.

- Optionally, add CHECK constraints or triggers to validate JSONB fields per type.

#### Pros

- **Simplicity**: One table, no joins needed for basic queries.

- **Matches Frontend**: The ENUM aligns directly with the frontend's type system.

- **Flexible**: JSONB accommodates varying attributes per type without schema changes.

- **Performance**: Single-table queries are fast, especially with JSONB indexing (e.g., `CREATE INDEX ON content USING GIN (metadata)`).

- **Maintenance**: Easy to add new types by extending the ENUM and updating validation logic.

#### Cons

- **Schema Enforcement**: JSONB is schemaless, so you rely on application logic or constraints to enforce structure.

- **Bloat**: If types have many unique fields, the JSONB column can become large and harder to query efficiently.

- **Not Normalized**: Storing type-specific data in JSONB isn't as strict as dedicated tables.

#### When to Use

- Types share many common fields.

- Type-specific fields are few or flexible.

- You prioritize simplicity and fast iteration over strict normalization.

- Query patterns involve fetching mixed types (e.g., "get all content").

#### Frontend Integration

- Map the database ENUM directly to the frontend enum.

- Parse metadata into type-specific objects in the API layer (e.g., `content.metadata.duration` for song).

### 2. Multiple Tables with Joins (Table Per Type)

Create a separate table for each type (e.g., songs, albums, playlists) and a content table for shared attributes, joining them based on type.

#### Schema Design

```sql
CREATE TYPE content_type AS ENUM ('song', 'album', 'playlist', 'profile', 'webpage', 'video', 'other');

CREATE TABLE content (
    id SERIAL PRIMARY KEY,
    type content_type NOT NULL,
    title VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE songs (
    content_id INT PRIMARY KEY REFERENCES content(id),
    duration INT NOT NULL,
    artist VARCHAR(255)
);

CREATE TABLE albums (
    content_id INT PRIMARY KEY REFERENCES content(id),
    track_count INT NOT NULL,
    release_date DATE
);

CREATE TABLE playlists (
    content_id INT PRIMARY KEY REFERENCES content(id),
    track_count INT NOT NULL,
    is_public BOOLEAN
);
-- Similar tables for profile, webpage, video, other
```

#### How It Works

- The content table stores common fields and the type enum.

- Each type-specific table (songs, albums, etc.) has a content_id foreign key linking to content.id.

- To query, join the content table with the appropriate type table:

```sql
SELECT c.*, s.duration, s.artist
FROM content c
JOIN songs s ON c.id = s.content_id
WHERE c.type = 'song';
```

- Use views or application logic to simplify queries:

```sql
CREATE VIEW all_content AS
SELECT c.id, c.type, c.title, s.duration, NULL AS track_count
FROM content c
JOIN songs s ON c.id = s.content_id WHERE c.type = 'song'
UNION
SELECT c.id, c.type, c.title, NULL AS duration, a.track_count
FROM content c
JOIN albums a ON c.id = a.content_id WHERE c.type = 'album'
-- Add other types
;
```

#### Pros

- **Strong Typing**: Each type has a strict schema, enforced by the database.

- **Normalization**: No redundant or schemaless data like JSONB.

- **Query Efficiency**: Type-specific queries are fast and clean (no parsing JSONB).

- **Scalability**: Easier to add indexes or optimize per type.

#### Cons

- **Complexity**: More tables mean more joins, views, or unions, complicating queries.

- **Maintenance**: Adding a new type requires creating a new table and updating logic.

- **Performance**: Joins can be slower than single-table queries, especially for mixed-type queries.

- **Fragmentation**: Fetching all content requires unions or multiple queries.

#### When to Use

- Types have significantly different attributes (e.g., song vs. profile).

- You need strict schema enforcement for each type.

- Queries are mostly type-specific (e.g., "get all songs").

- You expect heavy indexing or optimization per type.

#### Frontend Integration

- The API layer maps `content.type` to the frontend enum.

- Serialize type-specific tables into unified objects (e.g., `{ type: 'song', title: 'Foo', duration: 180 }`).

- Handle joins or unions in the backend to present a single Content interface.

## Conclusion

For most applications with enum-like content types, the single table with JSONB approach (Option 1) offers the best balance of simplicity, flexibility, and performance. However, if your types have very different structures or you need strict schema enforcement, the multiple tables approach (Option 2) may be more appropriate.
