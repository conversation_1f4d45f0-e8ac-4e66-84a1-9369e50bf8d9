# Dynamic URL Redirect System for CrateNFC App

For your CrateNFC App, embedding a dynamic URL (e.g., a unique UUID-based shortened URL) on NFC cards instead of the content.url directly is a smart approach. It provides flexibility, trackability, and the ability to update the destination without reprogramming the NFC card. Your idea of creating a redirect table that maps a unique identifier to a content_id (with a cached destination URL for fast queries) is a solid starting point.

## Why Use a Dynamic URL with a Redirect Table?

Using a dynamic URL (e.g., `https://lilrobo.xyz/r/abc123`) instead of directly embedding content.url (e.g., `https://spotify.com/track/xyz`) offers several benefits:

- **Flexibility**: You can update the destination URL (e.g., change from Spotify to Apple Music) without rewriting the NFC card.
- **Analytics**: Track scans by logging redirects, providing insights into card usage.
- **Security**: Obfuscate the destination to prevent tampering or guessing.
- **Branding**: Use a short, branded URL (e.g., `lilrobo.xyz/r/abc123`) for a professional look.
- **Consistency**: Ensure all NFC cards link to your domain, improving user trust.

A redirect table is the right approach to manage these dynamic URLs, as it decouples the NFC card's embedded URL from the content table and enables efficient lookups.

## Schema Design for the Redirect Table

Create a redirects table to store the mapping between a unique identifier (UUID or short code), the content_id, and a cached destination URL. Here's the proposed schema:

```sql
CREATE TABLE redirects (
    id SERIAL PRIMARY KEY,
    short_code VARCHAR(36) UNIQUE NOT NULL, -- UUID or short code (e.g., 'abc123' or '550e8400-e29b-41d4-a716-************')
    content_id INT NOT NULL REFERENCES content(id),
    destination_url VARCHAR(255) NOT NULL, -- Cached content.url for fast lookups
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scan_count INT DEFAULT 0, -- Optional: Track number of scans
    metadata JSONB -- Optional: For additional data (e.g., campaign info, NFC tag ID)
);

-- Indexes for performance
CREATE INDEX idx_redirects_short_code ON redirects(short_code);
CREATE INDEX idx_redirects_content_id ON redirects(content_id);
```

### Field Explanations

- **short_code**: A unique identifier for the redirect URL (e.g., a UUID like `550e8400-e29b-41d4-a716-************` or a shorter code like `abc123`). This forms the dynamic URL (e.g., `https://lilrobo.xyz/r/<short_code>`).
- **content_id**: Foreign key to the content table, linking the redirect to a specific content item (e.g., a track or event).
- **destination_url**: A cached copy of the content.url (or a computed URL) at the time the redirect is created. This allows fast, single-table queries without joining to content.
- **created_at/updated_at**: Tracks when the redirect was created or last updated.
- **scan_count**: Optional field to increment each time the redirect is accessed, enabling analytics.
- **metadata**: Optional JSONB field for extra data, such as:
  - NFC tag ID (e.g., `{"nfc_tag_id": "xyz123"}`).
  - Campaign details (e.g., `{"campaign": "summer_festival_2025"}`).
  - Expiry date for temporary redirects (e.g., `{"expires_at": "2025-12-31"}`).

### Why Cache destination_url?

Caching content.url in redirects.destination_url avoids the need to join with the content table for every redirect request, improving performance. For example, a request to `https://lilrobo.xyz/r/abc123` can be resolved with a single query:

```sql
SELECT destination_url FROM redirects WHERE short_code = 'abc123';
```

This is especially important for high-traffic scenarios where NFC cards are scanned frequently, as joins add latency.

## Workflow for NFC Cards

Here's how the process works with NFC cards and the redirect table:

### Creating Content and Redirect

1. A user creates a content item in the content table (e.g., a track with url = '<https://spotify.com/track/xyz>').
2. The app generates a redirect entry:
   - Create a unique short_code (e.g., a UUID or a shorter random string).
   - Set content_id to the content.id.
   - Copy content.url to redirects.destination_url.
   - Optionally add metadata (e.g., NFC tag ID).

Example redirects row:

```sql
INSERT INTO redirects (short_code, content_id, destination_url, metadata)
VALUES ('abc123', 42, 'https://spotify.com/track/xyz', '{"nfc_tag_id": "tag789"}');
```

### Writing to NFC Card

1. The app constructs the dynamic URL (e.g., `https://lilrobo.xyz/r/abc123`).
2. Write this URL to the NFC card's payload using the CrateNFC App's NFC writing functionality.
3. NFC cards typically store URLs as NDEF records, which are universally readable by smartphones.

### Handling Scans

1. When a user scans the NFC card, their device reads `https://lilrobo.xyz/r/abc123` and sends a request to your server.
2. Your server queries the redirects table:

   ```sql
   SELECT destination_url FROM redirects WHERE short_code = 'abc123';
   ```

3. Return a 301/302 redirect to the destination_url (e.g., `https://spotify.com/track/xyz`).
4. Optionally increment scan_count:

   ```sql
   UPDATE redirects SET scan_count = scan_count + 1 WHERE short_code = 'abc123';
   ```

### Updating Destinations

1. If the content.url changes (e.g., from Spotify to Apple Music), update the redirects.destination_url:

   ```sql
   UPDATE redirects SET destination_url = 'https://music.apple.com/track/xyz' WHERE content_id = 42;
   ```

2. The NFC card's URL (`https://lilrobo.xyz/r/abc123`) remains unchanged.

## Generating the short_code

You have two options for the short_code:

### UUID

- Use a full UUID (e.g., `550e8400-e29b-41d4-a716-************`) for guaranteed uniqueness.
- **Pros**: No collision risk, simple to generate.
- **Cons**: Longer URLs (36 characters), less user-friendly for manual entry.
- Implementation in C#:

  ```csharp
  var shortCode = Guid.NewGuid().ToString();
  ```

### Short Random Code

- Generate a shorter code (e.g., 6-8 characters, like `abc123`) using random alphanumeric characters.
- **Pros**: Shorter, more user-friendly URLs (e.g., `lilrobo.xyz/r/abc123`).
- **Cons**: Risk of collisions, requires uniqueness checks.
- Implementation in C#:

  ```csharp
  private string GenerateShortCode(int length = 6)
  {
      const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
      var random = new Random();
      var code = new char[length];
      for (int i = 0; i < length; i++)
      {
          code[i] = chars[random.Next(chars.Length)];
      }
      return new string(code);
  }
  ```

- Check for uniqueness in the database:

  ```csharp
  string shortCode;
  do
  {
      shortCode = GenerateShortCode();
  } while (await dbContext.Redirects.AnyAsync(r => r.ShortCode == shortCode));
  ```

**Recommendation**: Start with UUIDs for simplicity and guaranteed uniqueness, especially during development. If URL length becomes a concern (e.g., for branding or manual entry), switch to short codes with a uniqueness check. A 6-character code (62 possible characters: A-Z, a-z, 0-9) offers 62^6 ≈ 56 billion combinations, sufficient for most use cases with low collision risk.

## Integration with .NET EF Core

Here's how to implement the redirect system in your .NET EF Core application:

### 1. Entity Models

Define the Redirect entity to match the redirects table:

```csharp
public class Redirect
{
    public int Id { get; set; }
    public string ShortCode { get; set; }
    public int ContentId { get; set; }
    public Content Content { get; set; } // Navigation property
    public string DestinationUrl { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int ScanCount { get; set; }
    public JsonDocument Metadata { get; set; } // Or use a custom type for JSONB
}
```

Update the Content entity to include a collection of redirects (optional, for reverse lookup):

```csharp
public class Content
{
    public int Id { get; set; }
    public ContentType Type { get; set; }
    public string Title { get; set; }
    public string Detail { get; set; }
    public string Url { get; set; }
    public string ImgUrl { get; set; }
    public string Platform { get; set; }
    public string Description { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public JsonDocument Metadata { get; set; }
    public List<Redirect> Redirects { get; set; } = new List<Redirect>();
}
```

### 2. DbContext Configuration

Configure the redirects table and relationships:

```csharp
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    modelBuilder.HasPostgresEnum<ContentType>();

    modelBuilder.Entity<Redirect>(entity =>
    {
        entity.Property(r => r.ShortCode).HasMaxLength(36).IsRequired();
        entity.Property(r => r.DestinationUrl).HasMaxLength(255).IsRequired();
        entity.Property(r => r.Metadata).HasColumnType("jsonb");
        entity.HasOne(r => r.Content)
              .WithMany(c => c.Redirects)
              .HasForeignKey(r => r.ContentId);
        entity.HasIndex(r => r.ShortCode).IsUnique();
        entity.HasIndex(r => r.ContentId);
    });
}
```

### 3. Creating a Redirect

When a user creates content and wants to write an NFC card:

```csharp
public async Task<string> CreateRedirectForContent(int contentId, string nfcTagId, DbContext dbContext)
{
    var content = await dbContext.Content.FindAsync(contentId);
    if (content == null) throw new Exception("Content not found");

    var shortCode = Guid.NewGuid().ToString(); // Or use GenerateShortCode()
    var redirect = new Redirect
    {
        ShortCode = shortCode,
        ContentId = contentId,
        DestinationUrl = content.Url,
        Metadata = JsonDocument.Parse($"{{ \"nfc_tag_id\": \"{nfcTagId}\" }}")
    };

    dbContext.Redirects.Add(redirect);
    await dbContext.SaveChangesAsync();

    return $"https://lilrobo.xyz/r/{shortCode}";
}
```

### 4. Handling Redirect Requests

In your ASP.NET Core app, create a controller to handle redirect requests:

```csharp
[Route("r/{shortCode}")]
public class RedirectController : Controller
{
    private readonly YourDbContext _dbContext;

    public RedirectController(YourDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpGet]
    public async Task<IActionResult> RedirectToContent(string shortCode)
    {
        var redirect = await _dbContext.Redirects
            .FirstOrDefaultAsync(r => r.ShortCode == shortCode);

        if (redirect == null) return NotFound();

        // Increment scan count (optional)
        redirect.ScanCount++;
        await _dbContext.SaveChangesAsync();

        // Redirect to cached destination
        return Redirect(redirect.DestinationUrl);
    }
}
```

### 5. Writing to NFC Card

Use an NFC library (e.g., NdefLibrary for .NET) to write the dynamic URL to the NFC card. Example (simplified):

```csharp
public async Task WriteNfcCard(string nfcTagId, int contentId, DbContext dbContext)
{
    var dynamicUrl = await CreateRedirectForContent(contentId, nfcTagId, dbContext);

    // Pseudo-code for NFC writing (depends on platform, e.g., Xamarin, MAUI)
    var ndefMessage = new NdefMessage
    {
        new NdefUriRecord { Uri = dynamicUrl }
    };
    await NfcService.WriteAsync(ndefMessage); // Platform-specific NFC API
}
```

### 6. Updating Redirects

If the content.url changes, update all related redirects:

```csharp
public async Task UpdateRedirectsForContent(int contentId, string newUrl, DbContext dbContext)
{
    var redirects = await dbContext.Redirects
        .Where(r => r.ContentId == contentId)
        .ToListAsync();

    foreach (var redirect in redirects)
    {
        redirect.DestinationUrl = newUrl;
        redirect.UpdatedAt = DateTime.UtcNow;
    }

    await dbContext.SaveChangesAsync();
}
```

## Performance Considerations

- **Fast Lookups**: The idx_redirects_short_code index ensures O(1) lookups for redirect requests (`SELECT destination_url FROM redirects WHERE short_code = 'abc123'`).
- **Cached URL**: Storing destination_url in redirects avoids joins with content, critical for low-latency redirects.
- **Scan Count Updates**: Incrementing scan_count adds a write operation. If performance is a concern, batch updates or use a separate analytics table:

  ```sql
  CREATE TABLE redirect_scans (
      id SERIAL PRIMARY KEY,
      redirect_id INT REFERENCES redirects(id),
      scanned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
  ```

  Log scans with INSERT and aggregate later:

  ```sql
  SELECT redirect_id, COUNT(*) AS scan_count
  FROM redirect_scans
  GROUP BY redirect_id;
  ```

- **Scalability**: For high traffic, consider caching redirect mappings in memory (e.g., Redis) to reduce database load:

  ```csharp
  var cacheKey = $"redirect:{shortCode}";
  var cachedUrl = await cache.GetStringAsync(cacheKey);
  if (cachedUrl == null)
  {
      var redirect = await dbContext.Redirects
          .FirstOrDefaultAsync(r => r.ShortCode == shortCode);
      if (redirect != null)
      {
          await cache.SetStringAsync(cacheKey, redirect.DestinationUrl, TimeSpan.FromHours(1));
          cachedUrl = redirect.DestinationUrl;
      }
  }
  ```

## Security Considerations

- **Short Code Guessing**: UUIDs are resistant to guessing, but short codes (e.g., abc123) could be brute-forced. Use longer codes (8+ characters) or rate-limit requests to lilrobo.xyz/r/*.
- **URL Validation**: Validate content.url and redirects.destination_url to prevent malicious redirects (e.g., to phishing sites). Example:

  ```csharp
  if (!Uri.TryCreate(url, UriKind.Absolute, out var uri) || 
      !uri.Scheme.StartsWith("http"))
  {
      throw new Exception("Invalid URL");
  }
  ```

- **Access Control**: Ensure only authorized users can create redirects. Add an owner_id to redirects if needed:

  ```sql
  ALTER TABLE redirects ADD COLUMN owner_id INT REFERENCES users(id);
  ```

## Alignment with CrateNFC App

This approach fits your CrateNFC App's goals:

- **NFC Cards**: Dynamic URLs make NFC cards reusable and updatable, ideal for music, events, and collectibles.
- **Analytics**: scan_count or a redirect_scans table supports tracking engagement, aligning with your interest in digital assets and user interaction (per your X posts).
- **Flexibility**: The metadata JSONB field allows storing NFC-specific data (e.g., tag ID, campaign), complementing the hybrid design's flexibility.
- **Performance**: Cached destination_url and indexes ensure fast redirects, critical for a seamless user experience when scanning cards.

## Alternative Approaches

Instead of a redirects table, you could:

### Store Short Code in content Table

- Add a short_code column to content:

  ```sql
  ALTER TABLE content ADD COLUMN short_code VARCHAR(36) UNIQUE;
  ```

- **Pros**: Simplifies schema, no extra table.
- **Cons**: Less flexible (one short code per content item), harder to track multiple NFC cards per content item or analytics per redirect.

### Use an External URL Shortener

- Use a service like Bitly or TinyURL to generate short URLs.
- **Pros**: No need to manage redirects yourself.
- **Cons**: Dependency on third-party service, less control over analytics, potential costs.

### Encode content_id Directly

- Use `https://lilrobo.xyz/content/<content_id>` on NFC cards.
- **Pros**: Simplest approach, no extra table.
- **Cons**: Exposes content_id, less flexible for updates or analytics.

The redirects table is the best balance of flexibility, performance, and control, especially for NFC cards where multiple cards might link to the same content item.

## Final Schema and Implementation

### Updated Schema

```sql
CREATE TABLE redirects (
    id SERIAL PRIMARY KEY,
    short_code VARCHAR(36) UNIQUE NOT NULL,
    content_id INT NOT NULL REFERENCES content(id),
    destination_url VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scan_count INT DEFAULT 0,
    metadata JSONB
);

CREATE INDEX idx_redirects_short_code ON redirects(short_code);
CREATE INDEX idx_redirects_content_id ON redirects(content_id);
```

### Example Workflow

1. Create Content:

   ```sql
   INSERT INTO content (type, title, url, platform)
   VALUES ('track', 'Cool Song', 'https://spotify.com/track/xyz', 'spotify')
   RETURNING id; -- Returns 42
   ```

2. Create Redirect:

   ```sql
   INSERT INTO redirects (short_code, content_id, destination_url, metadata)
   VALUES ('abc123', 42, 'https://spotify.com/track/xyz', '{"nfc_tag_id": "tag789"}');
   ```

3. Write NFC Card:
   - Write `https://lilrobo.xyz/r/abc123` to the NFC card.

4. Handle Scan:

   ```sql
   SELECT destination_url FROM redirects WHERE short_code = 'abc123'; -- Returns 'https://spotify.com/track/xyz'
   UPDATE redirects SET scan_count = scan_count + 1 WHERE short_code = 'abc123';
   ```

### EF Core Controller Example

```csharp
[Route("r")]
public class RedirectController : Controller
{
    private readonly YourDbContext _dbContext;

    public RedirectController(YourDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpGet("{shortCode}")]
    public async Task<IActionResult> RedirectToContent(string shortCode)
    {
        var redirect = await _dbContext.Redirects
            .FirstOrDefaultAsync(r => r.ShortCode == shortCode);

        if (redirect == null) return NotFound();

        redirect.ScanCount++;
        await _dbContext.SaveChangesAsync();

        return Redirect(redirect.DestinationUrl);
    }
}
```

## Conclusion

**Recommendation**: Create a redirects table to manage dynamic URLs for NFC cards, as you suggested. Include a short_code (UUID or short code), content_id, and cached destination_url for fast, single-table queries. This approach provides:

- **Flexibility**: Update destinations without rewriting NFC cards.
- **Performance**: Cached destination_url and indexes ensure low-latency redirects.
- **Analytics**: Track scans with scan_count or a separate table.
- **Integration**: Seamless with your hybrid design and .NET EF Core.

### Next Steps

1. Add the redirects table to your schema with the proposed design.
2. Implement redirect creation and handling in EF Core, using UUIDs for short_code initially.
3. Write dynamic URLs to NFC cards using your NFC library.
4. Add analytics (e.g., scan_count) and consider caching for high traffic.
5. Validate URLs and secure the redirect endpoint.

This solution aligns perfectly with the CrateNFC App's goals of linking digital content to NFC cards, offering both functionality and scalability for your music and digital asset-focused platform.
