# Content table, using JSONB and Type-Specific Tables

This document introduces a hybrid PostgreSQL schema replacing a single Tracks table for URLs with a unified content table using JSONB for flexible, schemaless data and dedicated type-specific tables for structured querying. Moving away from Tracks eliminates its restrictive structure, unable to accommodate diverse content types like albums or profiles. The new design ensures scalability, supports varied query patterns, and enables progressive schema evolution while maintaining performance and simplicity.

## Design Goals

- Unified content table with shared fields and JSONB for adaptability
- Common title and detail fields for consistent UI and data access
- Dedicated type-specific tables for structured data and optimized queries
- Enable both flexible, schemaless and structured query patterns
- Defer JSONB constraints for initial flexibility, with option to add later

## Schema Design

### Current Schema ERD

```mermaid
erDiagram
    TRACKS {
        int id PK
        string title
        string artist
        string album_name
        int duration
        string url
        string img_url
        string platform
        datetime created_at
        datetime updated_at
    }
```

### Planned Schema ERD

```mermaid
erDiagram
    CONTENT {
        int id PK
        string type
        string title
        string detail
        string url
        string img_url
        string platform
        string description
        datetime created_at
        datetime updated_at
        jsonb metadata
    }

    TRACKS {
        int content_id PK, FK
        int duration
        string artist
        string album_name
        int release_year
    }

    ALBUMS {
        int content_id PK, FK
        string artist
        int track_count
        date release_date
        string label
    }

    PLAYLISTS {
        int content_id PK, FK
        string creator
        int track_count
        boolean is_public
        datetime last_updated
    }

    PROFILES {
        int content_id PK, FK
        string username
        text bio
        int followers
    }

    CONTENT ||--o{ TRACKS : "has"
    CONTENT ||--o{ ALBUMS : "has"
    CONTENT ||--o{ PLAYLISTS : "has"
    CONTENT ||--o{ PROFILES : "has"

```

### Core Content Table

```sql
CREATE TYPE content_type AS ENUM ('track', 'album', 'playlist', 'profile', 'webpage', 'video', 'other');

-- consider more content_types for future migrations
-- CREATE TYPE content_type AS ENUM ('track', 'album', 'playlist', 'profile', 'webpage', 'video', 'ebook', 'course', 'event', 'collectible', 'coupon', 'social', 'podcast', 'merch', 'app_link', 'other');

CREATE TABLE content (
    id SERIAL PRIMARY KEY,
    type content_type NOT NULL,
    title VARCHAR(255),
    detail VARCHAR(255),  -- Additional common information
    url VARCHAR(255),    -- Original URL that was unfurled
    img_url VARCHAR(255), -- Image URL for content thumbnail/cover
    platform VARCHAR(100), -- Platform source (spotify, apple_music, youtube, etc.)
    description TEXT,    -- Longer description of the content
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB       -- For flexible, type-specific attributes without constraints
);

-- Index for efficient JSONB queries
CREATE INDEX idx_content_metadata ON content USING GIN (metadata);
```

### Type-Specific Tables

```sql
-- Tracks table for structured track data
CREATE TABLE tracks (
    content_id INT PRIMARY KEY REFERENCES content(id),
    duration INT NOT NULL,
    artist VARCHAR(255),
    album_name VARCHAR(255),
    release_year INT
);

-- Albums table for structured album data
CREATE TABLE albums (
    content_id INT PRIMARY KEY REFERENCES content(id),
    artist VARCHAR(255),
    track_count INT NOT NULL,
    release_date DATE,
    label VARCHAR(255)
);

-- Playlists table for structured playlist data
CREATE TABLE playlists (
    content_id INT PRIMARY KEY REFERENCES content(id),
    creator VARCHAR(255),
    track_count INT NOT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    last_updated TIMESTAMP
);

-- Additional tables for other content types
CREATE TABLE profiles (
    content_id INT PRIMARY KEY REFERENCES content(id),
    username VARCHAR(255) NOT NULL,
    bio TEXT,
    followers INT DEFAULT 0
);

-- And so on for other types...
```

## How It Works

This hybrid approach combines the benefits of both methods:

1. **Common Data Storage**:
   - All content has an entry in the `content` table with common fields
   - Core fields include `title`, `detail`, `url`, `img_url`, and `platform`
   - The `description` field provides space for longer text content
   - The `metadata` JSONB field stores additional flexible, type-specific data

2. **Structured Data Storage**:
   - Each content type has its own dedicated table with a foreign key to `content.id`
   - Type-specific fields are stored in their respective tables with proper data types
   - No constraint checking on the JSONB field initially, allowing maximum flexibility

3. **Data Flow**:
   - When creating content, insert into both the `content` table and the appropriate type-specific table
   - The application can decide which fields go into `metadata` vs. the type-specific table

## Query Patterns

### Basic Content Queries

```sql
-- Get all content regardless of type
SELECT id, type, title, detail, url, img_url, platform, created_at, metadata
FROM content
ORDER BY created_at DESC;

-- Get content of a specific type
SELECT id, type, title, detail, url, img_url, platform, created_at, metadata
FROM content
WHERE type = 'track'
ORDER BY created_at DESC;

-- Search across all content types
SELECT id, type, title, detail, url, img_url, platform, description
FROM content
WHERE title ILIKE '%search term%'
   OR detail ILIKE '%search term%'
   OR description ILIKE '%search term%';
```

### Type-Specific Queries with Structured Data

```sql
-- Get tracks with structured data
SELECT c.id, c.title, c.detail, c.url, c.img_url, c.platform, t.artist, t.duration, c.metadata
FROM content c
JOIN tracks t ON c.id = t.content_id
WHERE c.type = 'track'
ORDER BY t.artist;

-- Get public playlists with more than 10 tracks
SELECT c.id, c.title, c.detail, c.url, c.img_url, c.platform, c.description, p.creator, p.track_count
FROM content c
JOIN playlists p ON c.id = p.content_id
WHERE c.type = 'playlist' AND p.is_public = TRUE AND p.track_count > 10;
```

### Hybrid Queries Using Both JSONB and Structured Data

```sql
-- Find tracks with a specific tag in metadata and by a specific artist
SELECT c.id, c.title, c.detail, c.url, c.img_url, c.platform, t.artist, t.duration
FROM content c
JOIN tracks t ON c.id = t.content_id
WHERE c.type = 'track'
  AND c.metadata @> '{"tags": ["rock"]}'
  AND t.artist = 'The Beatles';

-- Find albums released after 2020 with a specific producer in metadata
SELECT c.id, c.title, c.detail, c.url, c.img_url, c.platform, a.artist, a.release_date
FROM content c
JOIN albums a ON c.id = a.content_id
WHERE c.type = 'album'
  AND a.release_date >= '2020-01-01'
  AND c.metadata @> '{"producer": "Rick Rubin"}';
```

### Union Queries Across Multiple Content Types

```sql
-- Unified search across tracks, albums, and profiles
SELECT
    c.id,
    c.type,
    c.title,
    c.detail,
    c.url,
    c.img_url,
    c.platform,
    c.description,
    t.artist AS creator,
    'track' AS content_category,
    t.duration AS numeric_attribute,
    c.metadata
FROM content c
JOIN tracks t ON c.id = t.content_id
WHERE c.type = 'track'

UNION ALL

SELECT
    c.id,
    c.type,
    c.title,
    c.detail,
    c.url,
    c.img_url,
    c.platform,
    c.description,
    a.artist AS creator,
    'album' AS content_category,
    a.track_count AS numeric_attribute,
    c.metadata
FROM content c
JOIN albums a ON c.id = a.content_id
WHERE c.type = 'album'

UNION ALL

SELECT
    c.id,
    c.type,
    c.title,
    c.detail,
    c.url,
    c.img_url,
    c.platform,
    c.description,
    p.username AS creator,
    'profile' AS content_category,
    p.followers AS numeric_attribute,
    c.metadata
FROM content c
JOIN profiles p ON c.id = p.content_id
WHERE c.type = 'profile'
ORDER BY title;

-- Search across multiple content types with filtering
SELECT * FROM (
    -- Tracks by a specific artist
    SELECT
        c.id,
        c.type,
        c.title,
        c.detail,
        c.url,
        c.img_url,
        c.platform,
        c.description,
        t.artist,
        t.duration,
        NULL::date AS release_date,
        NULL::varchar AS username
    FROM content c
    JOIN tracks t ON c.id = t.content_id
    WHERE c.type = 'track' AND t.artist = 'Radiohead'

    UNION ALL

    -- Albums from a specific year
    SELECT
        c.id,
        c.type,
        c.title,
        c.detail,
        c.url,
        c.img_url,
        c.platform,
        c.description,
        a.artist,
        NULL::int AS duration,
        a.release_date,
        NULL::varchar AS username
    FROM content c
    JOIN albums a ON c.id = a.content_id
    WHERE c.type = 'album' AND EXTRACT(YEAR FROM a.release_date) = 2022

    UNION ALL

    -- Profiles with more than 1000 followers
    SELECT
        c.id,
        c.type,
        c.title,
        c.detail,
        c.url,
        c.img_url,
        c.platform,
        c.description,
        NULL::varchar AS artist,
        NULL::int AS duration,
        NULL::date AS release_date,
        p.username
    FROM content c
    JOIN profiles p ON c.id = p.content_id
    WHERE c.type = 'profile' AND p.followers > 1000
) AS unified_content
ORDER BY type, title;
```

## Benefits of This Hybrid Approach

1. **Flexibility and Structure**:
   - JSONB provides flexibility for evolving requirements and edge cases
   - Type-specific tables provide structure and type safety for core attributes

2. **Query Efficiency**:
   - Common queries can use the main content table without joins
   - Type-specific queries can leverage proper indexes and data types
   - Complex queries can combine both approaches

3. **Progressive Enhancement**:
   - Start with minimal structure and add more as patterns emerge
   - Move fields from JSONB to structured tables as they become standardized
   - Add JSONB constraints later when field requirements stabilize

4. **Best of Both Worlds**:
   - Maintains the simplicity of a single content table for basic operations
   - Provides the benefits of normalization for type-specific data
   - Supports both schema-on-read and schema-on-write patterns

## Implementation Strategy

1. **Initial Setup**:
   - Create the core `content` table with the `detail` field and JSONB `metadata`
   - Create type-specific tables for the most common content types
   - Set up basic indexes on frequently queried fields

2. **Data Migration Path**:
   - For new content types, decide whether they need a dedicated table
   - For existing content, consider moving frequently queried JSONB fields to structured tables
   - Use database views to present a unified interface to applications

3. **Application Integration**:
   - Create data access objects that handle the dual-write pattern
   - Implement query builders that can leverage both JSONB and structured data
   - Use transactions to ensure consistency between related tables

## Conclusion

This hybrid approach offers the best of both worlds: the flexibility of JSONB for evolving requirements and the structure of dedicated tables for efficient querying. By including both a `detail` VARCHAR field and a schemaless JSONB `metadata` field, while also maintaining type-specific tables, the database can support a wide range of query patterns and evolve over time as requirements change.

The approach is particularly well-suited for applications where:

- Some content types are well-defined while others are still evolving
- Query patterns vary between general content browsing and type-specific operations
- The application needs both flexibility for rapid iteration and structure for performance
