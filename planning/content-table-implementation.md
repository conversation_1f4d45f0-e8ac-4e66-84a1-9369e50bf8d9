# Content Table Implementation Plan

This document outlines the steps required to implement the hybrid PostgreSQL approach with a content table and type-specific tables as described in `planning/hybrid-approach.md`.

## Checklist

- [ ] Create Models and Migrations
- [ ] Create DTOs and Mappers
- [ ] Update Controllers
- [ ] Update Unfurl Endpoints
- [ ] Update Collections Handling
- [ ] Test and Validate Changes
- [ ] Migrate Existing Data

## Overview

The implementation will follow these main steps:

1. Create Models and Migrations
2. Create DTOs and Mappers
3. Update Controllers
4. Update Unfurl Endpoints
5. Update Collections Handling

Each section below provides detailed implementation guidance.

## 1. Creating Models and Migrations

### Motivation

The core Content model and type-specific models are essential to generalize the system for handling various types of digital content. This step ensures that the database schema supports flexibility and scalability for future content types.

### Core Content Model

```csharp
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CrateApi.Data.Models.Interfaces;

namespace CrateApi.Data.Models;

public enum ContentType
{
    Track,
    Album,
    Playlist,
    Profile,
    Webpage,
    Video,
    Other
}

public class Content : IUserIdentifier, ICreatedDate, IUpdatedDate
{
    [Required]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    [Required]
    public ContentType Type { get; set; }

    [Required]
    [StringLength(255)]
    public string Title { get; set; } = "";

    [StringLength(255)]
    public string Detail { get; set; } = "";

    [Url]
    public string Url { get; set; } = "";

    [Url]
    public string ImgUrl { get; set; } = "";

    public string Platform { get; set; } = "";

    public string Description { get; set; } = "";

    [Required]
    public DateTime Created { get; set; }

    [Required]
    public DateTime Updated { get; set; }

    [Required]
    public Guid UserId { get; set; }

    [Column(TypeName = "jsonb")]
    public string Metadata { get; set; } = "{}";
}
```

### Type-Specific Models

```csharp
namespace CrateApi.Data.Models;

public class TrackContent
{
    [Key]
    public int ContentId { get; set; }

    public int? Duration { get; set; }

    [StringLength(255)]
    public string Artist { get; set; } = "";

    [StringLength(255)]
    public string AlbumName { get; set; } = "";

    public int? ReleaseYear { get; set; }

    [ForeignKey("ContentId")]
    public Content Content { get; set; }
}
```

Similar models should be created for other content types (Album, Playlist, etc.).

### Update DbContext

```csharp
public class CrateDbContext : DbContext
{
    // Existing DbSets...

    public DbSet<Content> Contents { get; set; }
    public DbSet<TrackContent> TrackContents { get; set; }
    // Add other content type DbSets...

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.HasDefaultSchema("crate");

        // Existing configurations...

        modelBuilder.Entity<Content>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Title).IsRequired();
            entity.Property(e => e.Type).IsRequired();
            entity.Property(e => e.Updated).IsRequired();
            entity.Property(e => e.Created).IsRequired();
            entity.HasIndex(e => e.UserId);
            entity.HasOne<User>().WithMany().HasForeignKey(t => t.UserId).OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<TrackContent>(entity =>
        {
            entity.HasKey(e => e.ContentId);
            entity.HasOne(e => e.Content)
                .WithOne()
                .HasForeignKey<TrackContent>(e => e.ContentId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure other content type entities...
    }
}
```

### Create Migration

```bash
dotnet ef migrations add AddContentTables --project CrateApi.Data
dotnet ef database update --project CrateApi.Data
```

## 2. Create DTOs and Mappers

### Motivation

DTOs and mappers bridge the gap between the database models and the API responses. They ensure that the data is presented in a user-friendly and consistent format while maintaining separation of concerns.

### Response DTOs

```csharp
namespace CrateApi.Common.Dto.Response;

public record ContentResponseDto
{
    public int Id { get; set; }
    public string Type { get; set; }
    public string Title { get; set; }
    public string Detail { get; set; }
    public string Url { get; set; }
    public string ImgUrl { get; set; }
    public string Platform { get; set; }
    public string Description { get; set; }
    public DateTime Created { get; set; }
    public DateTime Updated { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public record TrackContentResponseDto : ContentResponseDto
{
    public int? Duration { get; set; }
    public string Artist { get; set; }
    public string AlbumName { get; set; }
    public int? ReleaseYear { get; set; }
}

// Create similar DTOs for other content types
```

### Request DTOs

```csharp
namespace CrateApi.Common.Dto.Request;

public record AddContentRequestDto
{
    [Required]
    public string Type { get; set; }

    [Required]
    public string Title { get; set; }

    public string Detail { get; set; }
    public string Url { get; set; }
    public string ImgUrl { get; set; }
    public string Platform { get; set; }
    public string Description { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();

    // Type-specific properties
    public int? Duration { get; set; }  // For tracks
    public string Artist { get; set; }  // For tracks/albums
    public string AlbumName { get; set; }  // For tracks
    public int? ReleaseYear { get; set; }  // For tracks/albums
    // Add other type-specific properties as needed
}

public record UpdateContentRequestDto
{
    public string Title { get; set; }
    public string Detail { get; set; }
    public string ImgUrl { get; set; }
    public string Description { get; set; }
    public Dictionary<string, object> Metadata { get; set; }

    // Type-specific properties
    public int? Duration { get; set; }
    public string Artist { get; set; }
    public string AlbumName { get; set; }
    public int? ReleaseYear { get; set; }
    // Add other type-specific properties as needed

    [Range(typeof(bool), "true", "true", ErrorMessage = "No values to update")]
    public bool IsValidUpdate =>
        !string.IsNullOrEmpty(Title) ||
        !string.IsNullOrEmpty(Detail) ||
        !string.IsNullOrEmpty(ImgUrl) ||
        !string.IsNullOrEmpty(Description) ||
        Metadata != null ||
        Duration.HasValue ||
        !string.IsNullOrEmpty(Artist) ||
        !string.IsNullOrEmpty(AlbumName) ||
        ReleaseYear.HasValue;
}
```

### Mappers

```csharp
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Data.Models;
using LanguageExt;
using LanguageExt.Traits;
using Riok.Mapperly.Abstractions;
using System.Text.Json;

namespace CrateApi.Services.Mappings;

[Mapper]
public partial class ContentMapper
{
    private static readonly ContentMapper mapper = new ContentMapper();
    public static ContentMapper Instance => mapper;
    private ContentMapper() { }

    // Basic content mapping
    public partial ContentResponseDto ToContentResponseDto(Content entity);

    // Track-specific mapping
    public TrackContentResponseDto ToTrackContentResponseDto(Content content, TrackContent trackContent)
    {
        var dto = ToContentResponseDto(content);
        return new TrackContentResponseDto
        {
            Id = dto.Id,
            Type = dto.Type,
            Title = dto.Title,
            Detail = dto.Detail,
            Url = dto.Url,
            ImgUrl = dto.ImgUrl,
            Platform = dto.Platform,
            Description = dto.Description,
            Created = dto.Created,
            Updated = dto.Updated,
            Metadata = dto.Metadata,
            Duration = trackContent.Duration,
            Artist = trackContent.Artist,
            AlbumName = trackContent.AlbumName,
            ReleaseYear = trackContent.ReleaseYear
        };
    }

    // Add similar methods for other content types

    // Request DTO to model conversion
    public (Content, TrackContent) FromAddTrackContentRequestDto(AddContentRequestDto dto)
    {
        var content = new Content
        {
            Type = Enum.Parse<ContentType>(dto.Type, true),
            Title = dto.Title,
            Detail = dto.Detail ?? "",
            Url = dto.Url ?? "",
            ImgUrl = dto.ImgUrl ?? "",
            Platform = dto.Platform ?? "",
            Description = dto.Description ?? "",
            Metadata = dto.Metadata != null ? JsonSerializer.Serialize(dto.Metadata) : "{}"
        };

        var trackContent = new TrackContent
        {
            Duration = dto.Duration,
            Artist = dto.Artist ?? "",
            AlbumName = dto.AlbumName ?? "",
            ReleaseYear = dto.ReleaseYear
        };

        return (content, trackContent);
    }

    // Add similar methods for other content types
}

// Extension for monadic operations
public static class ContentMapper<M> where M : Monad<M>, Fallible<M>
{
    public static K<M, ContentResponseDto> ToContentResponseDto(Content entity) =>
        M.Pure(ContentMapper.Instance.ToContentResponseDto(entity));

    public static K<M, TrackContentResponseDto> ToTrackContentResponseDto(Content content, TrackContent trackContent) =>
        M.Pure(ContentMapper.Instance.ToTrackContentResponseDto(content, trackContent));

    // Add similar methods for other content types and operations
}
```

## 3. Update Controllers

### Motivation

Controllers serve as the entry point for API requests. Updating them ensures that the new content model is fully integrated into the API, providing endpoints for managing content effectively.

### Content Controller

```csharp
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Exceptions;
using CrateApi.Data;
using CrateApi.Infrastructure;
using CrateApi.Services;
using CrateApi.Services.Database;
using CrateApi.Services.Runtimes;
using LanguageExt;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using ContentService = CrateApi.Services.Database.ContentService<LanguageExt.Eff<CrateApi.Services.Runtimes.DbRuntime>, CrateApi.Services.Runtimes.DbRuntime>;

namespace CrateApi.Controllers;

[Route("api/v1/[controller]")]
[RequiresApiKey]
[ApiController]
public class ContentController(DbRuntimeEnv env, ILogger<ContentController> logger) : ControllerBase
{
    private DbRuntime Rt => DbRuntime.Create(env);

    /// <summary>
    /// Get all content items paginated
    /// </summary>
    [Authorize]
    [HttpGet]
    [ProducesResponseType(typeof(List<ContentResponseDto>), 200)]
    public async Task<IActionResult> GetContent([FromQuery] int start, [FromQuery] int size = 20, [FromQuery] string type = null, CancellationToken token = default) =>
        (await ContentService.Paged(start, size, type).RunAsync(Rt))
            .Match(Ok, ExceptionResult.HandleErr(logger));

    /// <summary>
    /// Get content by ID
    /// </summary>
    [Authorize]
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(ContentResponseDto), 200)]
    public async Task<IActionResult> GetById([FromRoute] int id, CancellationToken token = default) =>
        (await ContentService.GetById(id).RunAsync(Rt))
            .Match(Ok, ExceptionResult.HandleErr(logger));

    /// <summary>
    /// Add new content
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType(typeof(ContentResponseDto), 200)]
    public async Task<IActionResult> Add([FromBody] AddContentRequestDto content, CancellationToken token = default) =>
        (await ContentService.Add(content).RunAsync(Rt))
            .Match(Ok, ExceptionResult.HandleErr(logger));

    /// <summary>
    /// Update existing content
    /// </summary>
    [Authorize]
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(ContentResponseDto), 200)]
    public async Task<IActionResult> Update([FromRoute] int id, [FromBody] UpdateContentRequestDto content, CancellationToken token = default) =>
        (await ContentService.Update(id, content).RunAsync(Rt))
            .Match(Ok, ExceptionResult.HandleErr(logger));

    /// <summary>
    /// Delete content
    /// </summary>
    [Authorize]
    [HttpDelete("{id}")]
    [ProducesResponseType(typeof(void), 204)]
    public async Task<IActionResult> Delete([FromRoute] int id, CancellationToken token = default) =>
        (await ContentService.Delete(id).RunAsync(Rt))
            .Match(_ => NoContent(), ExceptionResult.HandleErr(logger));
}
```

## 4. Update Unfurl Endpoints

### Motivation

The unfurl endpoints need to support the new content model to handle various types of digital content. This step ensures that metadata extraction and storage are aligned with the updated architecture.

```csharp
using CrateApi.Common;
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Extensions;
using CrateApi.Data;
using CrateApi.Data.Models;
using CrateApi.Services.Database;
using CrateApi.Services.DomainTypes;
using CrateApi.Services.Logic;
using CrateApi.Services.Mappings;
using LanguageExt;
using LanguageExt.Traits;
using LanguageExt.UnsafeValueAccess;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using static LanguageExt.Prelude;

namespace CrateApi.Services;

public static class UnfurlService<M, RT>
    where RT :
        Has<M, IHttpClientFactory>,
        Has<M, ILogger>,
        Has<M, IUserManager>,
        Has<M, CrateDbContext>,
        Has<M, DomainUnfurlUrl>,
        Has<M, ServiceSettings>
    where M :
        Monad<M>,
        Fallible<M>
{
    public static K<M, Option<UnfurledTrackResponseDto>> UnfurlTrackUrl(UnfurlUrlRequestDto request) =>
        from unfurl      in Has<M, RT, DomainUnfurlUrl>.ask
        from um          in Has<M, RT, IUserManager>.ask
        from l0          in Has<M, RT, ILogger>.ask
        from _0          in l0.LogInformationIO("[UNFURL] UNFURL CALLED for UserId: {User} | Url: {Url}", um.CurrentUser.UserId, unfurl.Url)
        from track       in GetUnfurledTrack(request)
        from response    in track.Match(Some: SaveContentToDatabase,
                                        None: M.Pure(Option<UnfurledTrackResponseDto>.None))
        from _1          in l0.LogInformationIO("[UNFURL] UNFURL COMPLETED for UserId: {User} | Url: {Url}", um.CurrentUser.UserId, unfurl.Url)
        select response;

    public static K<M, Option<UnfurledTrackResponseDto>> UnfurlTrackUrlAnonymous(UnfurlUrlRequestDto request) =>
        from unfurl      in Has<M, RT, DomainUnfurlUrl>.ask
        from l0          in Has<M, RT, ILogger>.ask
        from _0          in l0.LogInformationIO("[UNFURL] ANONYMOUS UNFURL CALLED: {Url}", unfurl.Url)
        from track       in GetUnfurledTrack(request)
        from response    in MapAnonymousTrack(track)
        from _1          in l0.LogInformationIO("[UNFURL] ANONYMOUS UNFURL COMPLETED FOR: {Url}", unfurl.Url)
        select response;

    private static K<M, Option<ITrack>> GetUnfurledTrack(UnfurlUrlRequestDto request) =>
        from f0          in Has<M, RT, IHttpClientFactory>.ask
        from url         in Has<M, RT, DomainUnfurlUrl>.ask
        from logger      in Has<M, RT, ILogger>.ask
        from settings    in Has<M, RT, ServiceSettings>.ask
        from serviceUrl  in M.Pure($"{settings.UnfurlServiceUrl}/api/v1/unfurl/{url.Endpoint}")
        from _0          in logger.LogInformationIO("[UNFURL] ATTEMPTING UNFURL ON ENDPOINT URL: {Url}", serviceUrl)
        from track       in UnfurlClient<M, RT>.PostJson(request, url.UrlType, serviceUrl)
        from _2          in logger.LogInformationIO("[UNFURL] SUCCESSFUL UNFURL ON ENDPOINT URL: {Url} Result: {Option}", serviceUrl, track)
        select track;

    private static K<M, Option<UnfurledTrackResponseDto>> SaveContentToDatabase(ITrack track) =>
        from logger      in Has<M, RT, ILogger>.ask
        from unfurlUrl   in Has<M, RT, DomainUnfurlUrl>.ask
        from _0          in logger.LogInformationIO("[UNFURL] Saving content to database... {track}", track)
        from content     in CreateContentFromTrack(track, unfurlUrl.UrlType)
        from addedContent in ContentService<M, RT>.Add(content)
        from _1          in logger.LogInformationIO("[UNFURL] Content saved! Continuing unfurl flow... ContentId: {contentId}", addedContent.Id)
        from trackResult in M.Pure(MapToUnfurledTrackResponseDto(addedContent))
        select Optional(trackResult);

    private static K<M, AddContentRequestDto> CreateContentFromTrack(ITrack track, UrlType urlType) =>
        from metadata    in M.Pure(new Dictionary<string, object>())
        from _           in M.Pure(() => metadata.Add("urlType", urlType.ToString()))
        from content     in M.Pure(new AddContentRequestDto
        {
            Type = "Track",
            Title = track.Title,
            Detail = track.Artist,
            Url = track.Url,
            ImgUrl = track.ImageUrl,
            Platform = GetPlatformFromUrlType(urlType),
            Description = track.Description ?? "",
            Metadata = metadata,
            Duration = track.Duration,
            Artist = track.Artist,
            // Map other track-specific properties
        })
        select content;

    private static string GetPlatformFromUrlType(UrlType urlType) =>
        urlType switch
        {
            UrlType.Spotify => "spotify",
            UrlType.Apple => "apple_music",
            UrlType.YouTube => "youtube",
            _ => "other"
        };

    private static K<M, Option<UnfurledTrackResponseDto>> MapAnonymousTrack(Option<ITrack> track) =>
        track.Match(
            Some: t => M.Pure(Optional(MapToUnfurledTrackResponseDto(t))),
            None: () => M.Pure(Option<UnfurledTrackResponseDto>.None));

    private static UnfurledTrackResponseDto MapToUnfurledTrackResponseDto(ITrack track) =>
        new UnfurledTrackResponseDto
        {
            Title = track.Title,
            Artist = track.Artist,
            Url = track.Url,
            ImageUrl = track.ImageUrl,
            Duration = track.Duration,
            Description = track.Description
        };

    private static UnfurledTrackResponseDto MapToUnfurledTrackResponseDto(ContentResponseDto content) =>
        content is TrackContentResponseDto trackContent
            ? new UnfurledTrackResponseDto
            {
                Id = content.Id,
                Title = content.Title,
                Artist = trackContent.Artist,
                Url = content.Url,
                ImageUrl = content.ImgUrl,
                Duration = trackContent.Duration,
                Description = content.Description
            }
            : new UnfurledTrackResponseDto
            {
                Id = content.Id,
                Title = content.Title,
                Artist = content.Detail,  // Use detail field for artist if not a track
                Url = content.Url,
                ImageUrl = content.ImgUrl,
                Description = content.Description
            };
}
```

Update the UnfurlController to use the new service:

```csharp
[ApiController]
[Route("api/v1/[controller]")]
[RequiresApiKey]
public class UnfurlController(CrateDbContext context, IUserManager userManager, IHttpClientFactory factory, ILogger<UnfurlController> logger, ServiceSettings settings) : ControllerBase
{
    private UnfurlRuntime Rt(DomainUnfurlUrl url) =>
        UnfurlRuntime.Create(factory, url, logger, settings, context, userManager);

    /// <summary>
    /// Unfurl a URL and save it as content in the database
    /// </summary>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(UnfurledTrackResponseDto), 200)]
    public async Task<IActionResult> UnfurlUrl([FromBody] UnfurlUrlRequestDto model)
    {
        var url = DomainUnfurlUrl.From(model.Url).ThrowIfFail();

        var result = await UnfurlService<Eff<UnfurlRuntime>, UnfurlRuntime>.UnfurlTrackUrl(model).RunAsync(Rt(url));
        return result.Match<IActionResult>(
            response => response.Match<IActionResult>(Ok, NoContent),
            ExceptionResult.HandleErr(logger)
        );
    }

    /// <summary>
    /// Unfurl a URL without saving it to the database
    /// </summary>
    [HttpPost("anonymous")]
    [ProducesResponseType(typeof(UnfurledTrackResponseDto), 200)]
    public async Task<IActionResult> UnfurlUrlAnonymous([FromBody] UnfurlUrlRequestDto model)
    {
        var url = DomainUnfurlUrl.From(model.Url).ThrowIfFail();

        var result = await UnfurlService<Eff<UnfurlRuntime>, UnfurlRuntime>.UnfurlTrackUrlAnonymous(model).RunAsync(Rt(url));
        return result.Match<IActionResult>(
            response => response.Match<IActionResult>(Ok, NoContent),
            ExceptionResult.HandleErr(logger)
        );
    }
}
```

## 5. Update Collections Handling

### Motivation

Collections are a key feature for organizing content. Updating the collection handling logic ensures compatibility with the new content model and provides a seamless user experience.

### Update Collection Model

```csharp
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CrateApi.Data.Models.Interfaces;

namespace CrateApi.Data.Models;

public class Collection : IUserIdentifier, ICreatedDate, IUpdatedDate
{
    [Required]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int? Id { get; set; }

    [Required]
    [StringLength(256)]
    public string? Name { get; set; } = "";

    [Url]
    public string? Thumbnail { get; set; } = "";

    [Required]
    public DateTime Created { get; set; }

    [Required]
    public DateTime Updated { get; set; }

    [Required]
    public Guid UserId { get; set; }

    public List<CollectionContentMapping> CollectionContents { get; set; } = new List<CollectionContentMapping>();
}
```

### Create Collection-Content Mapping

```csharp
using System.ComponentModel.DataAnnotations;

namespace CrateApi.Data.Models;

public class CollectionContentMapping
{
    [Required]
    public int CollectionId { get; set; }

    [Required]
    public int ContentId { get; set; }

    public Collection Collection { get; set; }
    public Content Content { get; set; }

    public static CollectionContentMapping Create(int collectionId, int contentId) =>
        new CollectionContentMapping
        {
            ContentId = contentId,
            CollectionId = collectionId,
        };
}
```

### Configure Collection-Content Relationship

```csharp
// In CrateDbContext.cs
public DbSet<CollectionContentMapping> CollectionContentMappings { get; set; }

// In OnModelCreating method
modelBuilder.Entity<CollectionContentMapping>(entity =>
{
    entity.HasKey(cc => new { cc.CollectionId, cc.ContentId });

    entity.HasOne(cc => cc.Collection)
        .WithMany(c => c.CollectionContents)
        .HasForeignKey(cc => cc.CollectionId)
        .OnDelete(DeleteBehavior.Cascade);

    entity.HasOne(cc => cc.Content)
        .WithMany()
        .HasForeignKey(cc => cc.ContentId)
        .OnDelete(DeleteBehavior.Cascade);
});
```

### Update Collection Service

```csharp
public static K<M, CollectionResponseDto> AddContentsToCollection(int collectionId, List<int> contentIds) =>
    from context               in Has<M, RT, CrateDbContext>.ask
    from um                    in Has<M, RT, IUserManager>.ask
    from _l                    in Has<M, RT, ILogger>.ask
    from startLog              in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(AddContentsToCollection), OL.Start)
    from collection            in GetCollection(collectionId)
    from _                     in guard(collection != null, () => Error.New($"Collection with ID {collectionId} not found"))
    from existingContentIds    in IO.lift(() => collection.CollectionContents.Select(cc => cc.ContentId).ToList())
    from contentsNotAlreadyAdded in IO.lift(() => contentIds.Where(id => !existingContentIds.Contains(id)).ToList())
    from _0                    in _l.LogInformationIO("Adding {Count} contents to collection with id: {Id}", contentsNotAlreadyAdded.Count, collectionId)
    from contents              in GetContents(contentsNotAlreadyAdded)
    from _2                    in IUpdatedDate.AssignUpdatedNow(collection)
    from _3                    in IO.lift(() => collection.CollectionContents.AddRange(CreateMappings(contents, collectionId)))
    from updatedCollection     in context.UpdateAndSave(collection)
    from _4                    in _l.LogInformationIO("Successfully added contents to collection with id: {Id}", collectionId)
    from mapped                in CollectionMapper<M>.ToCollectionResponseDtoWithContents(updatedCollection)
    from endLog                in _l.LogMethod(nameof(CollectionService<M, RT>), nameof(AddContentsToCollection), OL.End)
    select mapped;

private static K<M, Collection> GetCollection(int collectionId) =>
    from cn in Has<M, RT, CrateDbContext>.ask
    from um in Has<M, RT, IUserManager>.ask
    from cl in IO.liftAsync(() => cn.Collections
        .Include(c => c.CollectionContents)
        .ThenInclude(c => c.Content)
        .FirstOrDefaultAsync(c => c.Id == collectionId && c.UserId == um.CurrentUser.UserId))
    select cl;

private static K<M, List<Content>> GetContents(List<int> contentIds) =>
    from cn in Has<M, RT, CrateDbContext>.ask
    from contents in IO.liftAsync(() => cn.Contents.Where(c => contentIds.Contains(c.Id)).ToListAsync())
    select contents;

private static List<CollectionContentMapping> CreateMappings(List<Content> contents, int collectionId) =>
    contents.Select(content => CollectionContentMapping.Create(collectionId, content.Id)).ToList();
```

### Update Collection Response DTO

```csharp
public record CollectionResponseDto
{
    public int Id { get; set; }
    public string? Name { get; set; }
    public string? Thumbnail { get; set; }
    public DateTime Created { get; set; }
    public DateTime Updated { get; set; }
    public List<ContentResponseDto> Contents { get; set; } = new List<ContentResponseDto>();
}
```

### Update Collection Mapper

```csharp
[Mapper]
public partial class CollectionMapper
{
    private static readonly CollectionMapper mapper = new CollectionMapper();
    public static CollectionMapper Instance => mapper;
    private CollectionMapper() { }

    public partial Collection FromAddCollectionDto(AddCollectionRequestDto entity);

    [MapperIgnoreTarget(nameof(CollectionResponseDto.Contents))]
    public partial CollectionResponseDto ToCollectionResponseDto(Collection entity);

    public CollectionResponseDto ToCollectionResponseDtoWithContents(Collection collection)
    {
        var dto = ToCollectionResponseDto(collection);

        if (collection.CollectionContents != null)
        {
            foreach (var mapping in collection.CollectionContents)
            {
                if (mapping.Content != null)
                {
                    var contentDto = ContentMapper.Instance.ToContentResponseDto(mapping.Content);
                    dto.Contents.Add(contentDto);
                }
            }
        }

        return dto;
    }
}

public static class CollectionMapper<M> where M : Monad<M>, Fallible<M>
{
    // Existing methods...

    public static K<M, CollectionResponseDto> ToCollectionResponseDtoWithContents(Collection entity) =>
        M.Pure(CollectionMapper.Instance.ToCollectionResponseDtoWithContents(entity));
}
```

### Update Collection Controller

```csharp
/// <summary>
/// Add contents to an already existing collection by Id. If a contentID provided already exists in the collection, the content will not be added.
/// </summary>
[Authorize]
[HttpPost("{id}/add")]
[ProducesResponseType(typeof(CollectionResponseDto), 200)]
public async Task<IActionResult> AddContents([FromRoute] int id, [FromBody, Required] List<int> contentIds, CancellationToken token = default) =>
    (await Collections.AddContentsToCollection(id, contentIds).RunAsync(Rt))
        .Match(Ok, ExceptionResult.HandleErr(logger));

/// <summary>
/// Removes contents by Id for a collection. If a contentId provided does not exist in a collection,
/// removing a content which does not have a contentId assigned to the collection has no effect.
/// </summary>
[Authorize]
[HttpPost("{id}/remove")]
[ProducesResponseType(typeof(CollectionResponseDto), 200)]
public async Task<IActionResult> RemoveContents([FromRoute] int id, [FromBody, Required] List<int> contentIds, CancellationToken token = default) =>
    (await Collections.RemoveContentsFromCollection(id, contentIds).RunAsync(Rt))
        .Match(Ok, ExceptionResult.HandleErr(logger));
```

## 6. Migration Strategy

### Motivation

A phased migration strategy minimizes disruption to existing users while transitioning to the new content model. This ensures data integrity and backward compatibility during the migration process.

To safely migrate from the existing Track-based system to the new Content-based system, follow these steps:

1. **Create New Tables**: Add the new Content and type-specific tables without modifying existing tables.

2. **Implement New Endpoints**: Create the new Content endpoints alongside existing Track endpoints.

3. **Data Migration**: Create a migration script to copy data from Track tables to Content tables:

   ```csharp
   // Example migration logic
   foreach (var track in context.Tracks.ToList())
   {
       var content = new Content
       {
           Type = ContentType.Track,
           Title = track.TrackTitle,
           Detail = track.ArtistName,
           Url = track.Url,
           ImgUrl = track.MediaUrl ?? "",
           Platform = GetPlatformName(track.PlatformType),
           Description = "",
           UserId = track.UserId,
           Created = track.Created,
           Updated = track.Updated,
           Metadata = JsonSerializer.Serialize(new
           {
               isrc = track.Isrc,
               domainUrl = track.DomainUrl
           })
       };

       context.Contents.Add(content);
       await context.SaveChangesAsync();

       var trackContent = new TrackContent
       {
           ContentId = content.Id,
           Duration = track.Duration,
           Artist = track.ArtistName,
           // Map other fields
       };

       context.TrackContents.Add(trackContent);
       await context.SaveChangesAsync();
   }
   ```

4. **Collection Migration**: Update collection mappings to point to new Content records.

5. **Testing**: Thoroughly test both systems in parallel before switching over.

6. **Switchover**: Once confident, update client applications to use the new Content endpoints.

7. **Cleanup**: After a stable period, remove the old Track tables and endpoints.

## 7. Conclusion

Implementing the hybrid Content table approach provides several benefits:

1. **Flexibility**: The system can now handle various content types beyond just tracks.

2. **Structured Data**: Type-specific tables maintain strong typing and efficient querying.

3. **Common Interface**: All content types share a common interface for basic operations.

4. **Future-Proof**: New content types can be added with minimal changes to the core system.

5. **Improved Queries**: Complex queries across content types are now possible.

This implementation aligns with the design goals outlined in the hybrid approach document and provides a solid foundation for future expansion of the application's content handling capabilities.
