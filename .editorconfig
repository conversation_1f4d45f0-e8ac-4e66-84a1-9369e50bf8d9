# EditorConfig is awesome: https://EditorConfig.org
# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 4
insert_final_newline = true
trim_trailing_whitespace = true
max_line_length = 100

# C# files
[*.cs]
#dotnet_analyzer_diagnostic.category-Globalization.severity = warning
dotnet_analyzer_diagnostic.category-Style.severity = none
dotnet_analyzer_diagnostic.category-CodeStyle.severity = none
dotnet_analyzer_diagnostic.category-Performance.severity = none
dotnet_analyzer_diagnostic.category-Reliability.severity = none
dotnet_analyzer_diagnostic.category-Security.severity = none
dotnet_analyzer_diagnostic.category-Usage.severity = none

# Braces and control flow
#csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_between_query_expression_clauses = true
csharp_indent_case_contents = true
csharp_indent_switch_labels = true
csharp_indent_labels = no_change

# Spaces
csharp_space_after_cast = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_around_binary_operators = before_and_after
# LoggerMessage delegate
dotnet_diagnostic.CA1848.severity = none

# Prefer expression body
csharp_style_expression_bodied_methods = when_on_single_line:suggestion
csharp_style_expression_bodied_properties = true:suggestion
csharp_style_expression_bodied_accessors = true:suggestion

# XML project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_size = 2
indent_style = space
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

# XML config files
[*.{props,targets,ruleset,config,nuspec,resx,vsixmanifest,vsct}]
indent_size = 2
indent_style = space

# JSON files
[*.{json,json5}]
indent_size = 4

# YAML files
[*.{yml,yaml}]
indent_size = 4

# XML files
[*.{xml,xaml}]
indent_size = 4

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# Web files
[*.{htm,html,js,ts,css,scss,less}]
indent_size = 4
max_line_length = 200

# Shell scripts
[*.sh]
end_of_line = lf

# Windows scripts
[*.{cmd,bat,ps1}]
end_of_line = crlf
