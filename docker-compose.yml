#Test comment build
services:
  crateapi:
    build:
      context: .
      dockerfile: CrateApi/Dockerfile
    ports:
      - 8000:8000
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8000
      - POSTGRES_HOST=db
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=postgres
      - UnfurlServiceUrl=http://unfurlapi:8001
    depends_on:
      db:
        condition: service_healthy
      unfurlapi:
        condition: service_started

  unfurlapi:
    build:
      context: .
      dockerfile: UnfurlApi/Dockerfile
    ports:
      - "8001:8001"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8001

  db:
    container_name: pg_container
    image: postgres:16-alpine
    environment:
      - POSTGRES_DB=cratedb
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 5s
      timeout: 5s
      retries: 5
