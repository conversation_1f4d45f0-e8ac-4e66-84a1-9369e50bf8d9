.PHONY: build docker-build clean restore watch run run2 dbu dba docker-up docker-down docker-run

# .NET builds
build:
	dotnet build
clean:
	dotnet clean
restore:
	dotnet restore
watch:
	dotnet watch --project ./CrateApi/CrateApi.csproj run
run:
	dotnet run --project ./CrateApi/CrateApi.csproj
run2:
	dotnet run --project ./UnfurlApi/UnfurlApi.csproj
dbu:
	dotnet ef database update --project ./CrateApi/
dba:
	dotnet ef migrations add $(name) --project CrateApi.Data --startup-project CrateA<PERSON>

# Docker builds
docker-build:
	docker compose build --no-cache --pull

docker-up:
	docker compose up -d

docker-down:
	docker compose down

docker-run:
	docker compose up; docker compose rm -f -v; rm -rf ./data
