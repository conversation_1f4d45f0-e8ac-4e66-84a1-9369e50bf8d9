#!/bin/bash

# Variables (customize these)
RESOURCE_GROUP="rg-cratenfc-testflight-westus"
SERVER_NAME="cratenfc-postgres-westus"
LOCATION="westus"
ADMIN_USER="postgresadmin"
ADMIN_PASSWORD="SecureP@ssw0rd123"  # Replace with a strong password
SKU_NAME="B_Standard_B1ms"           # Burstable tier, adjust as needed (e.g., GP_Standard_D2s_v3 for General Purpose)
STORAGE_SIZE="32"                    # Storage in GB

# Create the PostgreSQL Flexible Server
az postgres flexible-server create \
    --resource-group "$RESOURCE_GROUP" \
    --name "$SERVER_NAME" \
    --location "$LOCATION" \
    --admin-user "$ADMIN_USER" \
    --admin-password "$ADMIN_PASSWORD" \
    --sku-name "$SKU_NAME" \
    --tier Burstable \
    --storage-size "$STORAGE_SIZE" \
    --version 15 \
    --public-access 0.0.0.0-***************  # Allow all IPs for now (tighten later)

# Output the connection details
echo "PostgreSQL server created. Hostname: $SERVER_NAME.postgres.database.azure.com"
echo "Admin username: $ADMIN_USER"
echo "Admin password: $ADMIN_PASSWORD"


# Create a database named 'cratedb'
az postgres flexible-server db create \
    --resource-group "$RESOURCE_GROUP" \
    --server-name "$SERVER_NAME" \
    --database-name "cratedb"

# Update firewall to allow Azure services
az postgres flexible-server firewall-rule create \
    --resource-group "$RESOURCE_GROUP" \
    --name "$SERVER_NAME" \
    --rule-name "AllowAllAzureIPs" \
    --start-ip-address 0.0.0.0 \
    --end-ip-address 0.0.0.0

az webapp show --resource-group $RESOURCE_GROUP --name app-cratenfc-testflight-westus --query outboundIpAddresses


az keyvault create \
    --resource-group "$RESOURCE_GROUP" \
    --name "cratenfc-kv-westus" \
    --location "$LOCATION"

az keyvault secret set \
    --vault-name "cratenfc-kv-westus" \
    --name "PostgresPassword" \
    --value "SecureP@ssw0rd123"

az webapp identity assign \
    --resource-group "$RESOURCE_GROUP" \
    --name "app-cratenfc-testflight-westus"

PRINCIPAL_ID=$(az webapp identity show --resource-group "$RESOURCE_GROUP" --name "app-cratenfc-testflight-westus" --query principalId -o tsv)

az keyvault set-policy \
    --resource-group "$RESOURCE_GROUP" \
    --name "cratenfc-kv-westus" \
    --object-id "$PRINCIPAL_ID" \
    --secret-permissions get

Using keyvault in docker-compose app

environment:
  - ConnectionStrings__DefaultConnection=Host=cratenfc-postgres-westus.postgres.database.azure.com;Database=cratedb;Username=postgresadmin;Password=@Microsoft.KeyVault(VaultName=cratenfc-kv-westus;SecretName=PostgresPassword);SSL Mode=Require
