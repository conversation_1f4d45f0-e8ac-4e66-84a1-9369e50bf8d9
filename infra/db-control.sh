#!/bin/bash

# Default variables (customize these)
RESOURCE_GROUP="rg-cratenfc-testflight-westus"
SERVER_NAME="cratenfc-postgres-westus"

# Function to display usage/help
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Manage an Azure PostgreSQL Flexible Server."
    echo ""
    echo "Options:"
    echo "  -p, --postgres-action ACTION  Action to perform: start, stop, delete, show"
    echo "  -h, --help                   Display this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -p start    # Start the PostgreSQL server"
    echo "  $0 -p stop     # Stop the PostgreSQL server"
    echo "  $0 -p delete   # Delete the PostgreSQL server"
    echo "  $0 -p show     # Show server status"
}

# Function to check if az CLI is installed
check_az() {
    if ! command -v az &> /dev/null; then
        echo "Error: Azure CLI (az) is not installed. Please install it first."
        exit 1
    fi
}

# Function to start the server
start_server() {
    echo "Starting PostgreSQL server: $SERVER_NAME..."
    az postgres flexible-server start \
        --resource-group "$RESOURCE_GROUP" \
        --name "$SERVER_NAME"
    if [ $? -eq 0 ]; then
        echo "Server started successfully."
    else
        echo "Failed to start server."
        exit 1
    fi
}

# Function to stop the server
stop_server() {
    echo "Stopping PostgreSQL server: $SERVER_NAME..."
    az postgres flexible-server stop \
        --resource-group "$RESOURCE_GROUP" \
        --name "$SERVER_NAME"
    if [ $? -eq 0 ]; then
        echo "Server stopped successfully."
    else
        echo "Failed to stop server."
        exit 1
    fi
}

# Function to delete the server
delete_server() {
    echo "WARNING: This will permanently delete $SERVER_NAME and all its data."
    read -p "Are you sure? (y/N): " confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        echo "Deleting PostgreSQL server: $SERVER_NAME..."
        az postgres flexible-server delete \
            --resource-group "$RESOURCE_GROUP" \
            --name "$SERVER_NAME" \
            --yes
        if [ $? -eq 0 ]; then
            echo "Server deleted successfully."
        else
            echo "Failed to delete server."
            exit 1
        fi
    else
        echo "Deletion cancelled."
    fi
}

# Function to show server status
show_server() {
    echo "Fetching status for PostgreSQL server: $SERVER_NAME..."
    az postgres flexible-server show \
        --resource-group "$RESOURCE_GROUP" \
        --name "$SERVER_NAME" \
        --query "{Name:name, State:state, Host:fullyQualifiedDomainName, Version:version}" \
        --output table
    if [ $? -ne 0 ]; then
        echo "Server not found or error occurred."
        exit 1
    fi
}

# Check if az CLI is available
check_az

# Parse command-line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -p|--postgres-action)
            ACTION="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate and execute the action
if [ -z "$ACTION" ]; then
    echo "Error: No action specified."
    usage
    exit 1
fi

case "$ACTION" in
    start)
        start_server
        ;;
    stop)
        stop_server
        ;;
    delete)
        delete_server
        ;;
    show)
        show_server
        ;;
    *)
        echo "Error: Invalid action '$ACTION'. Valid actions: start, stop, delete, show"
        usage
        exit 1
        ;;
esac

exit 0
