#!/bin/bash

# az webapp config appsettings set \
#     --resource-group rg-cratenfc-testflight-westus \
#     --name app-cratenfc-testflight-westus \
#     --settings \
#     POSTGRES_HOST="@Microsoft.KeyVault(SecretUri=https://kv-cratenfc-testflight.vault.azure.net/secrets/PostgresHost/)" \
#     POSTGRES_USERNAME="@Microsoft.KeyVault(SecretUri=https://kv-cratenfc-testflight.vault.azure.net/secrets/PostgresUsername/)" \
#     POSTGRES_PASSWORD="@Microsoft.KeyVault(SecretUri=https://kv-cratenfc-testflight.vault.azure.net/secrets/PostgresPassword/)" 


# This script is used to deploy the docker-compose stack to the server
az webapp config container set \
    --resource-group rg-cratenfc-testflight-westus \
    --name app-cratenfc-testflight-westus \
    --multicontainer-config-type compose \
    --multicontainer-config-file docker-compose-azure.yml 


az webapp restart \
    --resource-group rg-cratenfc-testflight-westus \
    --name app-cratenfc-testflight-westus

az webapp log tail \
    --resource-group rg-cratenfc-testflight-westus \
    --name app-cratenfc-testflight-westus
