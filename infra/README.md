# Working with Azure Deployment

## Installing `az`, the Azure CLI

Refer to [Azure CLI Official Docs](https://learn.microsoft.com/en-us/cli/azure/)

```bash
# osx
brew install azure-cli
```

### Upgrading `az`

```sh
az upgrade
az version
```

## Logging into a Azure Subscription

Azure Subscriptions are containers for managing Azure Resources Groups. 
They are often used to segregate Azure Resources with single billing schemes.
For example, a company may have a production env subscription and a development env subscription.

```sh
az login
```

This should open the browser and log you in.
Then you can select the correct subscription.

```sh
az account list --all

az account set --subscription "fef123f1-e781-4d6b-8c16-edc2d84188d2"

az account show 
```

## Renaming the subscription name

Add a descriptive name to the subscription. It should describe the stack, application, or env.

```sh
az account subscription rename \
  --subscription-id "fef123f1-e781-4d6b-8c16-edc2d84188d2" \
  --name "CrateNfc-TestFlight"

az account list --refresh
```

## Creating a Resource Group

```sh
az group create \
  --name "rg-cratenfc-testflight-westus" \
  --location "East US"
```

----

# Azure Naming Convention for CrateNFC Project

This document outlines the naming convention for Azure resources used in the CrateNFC project, including resource groups, App Service plans, and App Service instances. The convention is designed to ensure consistency, readability, and scalability across multiple environments and accounts (e.g., two companies managed by the project team).

## Overview
The naming convention follows a structured format that includes the resource type, project identifier, environment, and region. This approach aligns with [Azure's best practices for resource naming](https://learn.microsoft.com/en-us/azure/cloud-adoption-framework/ready/azure-best-practices/resource-naming) and supports the project's multi-container deployment (`crateapi` and `unfurlapi`) on Azure App Service.

## Naming Convention Structure
The general format is:  
`<resource-type>-<project>-<environment>-<region>`  
- **resource-type**: A prefix indicating the resource category (e.g., `rg` for resource group).
- **project**: The project or application name (e.g., `cratenfc` for CrateNFC).
- **environment**: The deployment environment (e.g., `testflight` for iOS TestFlight beta testing).
- **region**: The Azure region (e.g., `westus` for East US).

### Rules
- Use hyphens (`-`) as separators for readability.
- Keep names under 90 characters (Azure's limit for resource group names; other resources may have stricter limits).
- Use lowercase letters for consistency (Azure names are case-insensitive).
- Avoid special characters except hyphens and underscores where allowed.
- Ensure names are unique where required (e.g., App Service names must be globally unique).

## Specific Naming Conventions

### Resource Groups
- **Format**: `rg-<project>-<environment>-<region>`
- **Example**: `rg-cratenfc-testflight-westus`
- **Description**: 
  - `rg`: Indicates a resource group.
  - `cratenfc`: The CrateNFC project (combining "crate" and "nfc" for brevity).
  - `testflight`: Refers to the iOS TestFlight beta testing environment.
  - `westus`: The Azure region (East US).
- **Use Case**: Used to group resources for the TestFlight environment in East US.

### App Service Plans
- **Format**: `plan-<project>-<environment>-<region>`
- **Example**: `plan-cratenfc-testflight-westus`
- **Description**: 
  - `plan`: Indicates an App Service plan.
  - Follows the same project, environment, and region structure as resource groups.
- **Use Case**: Defines the compute resources for the App Service hosting `crateapi` and `unfurlapi`.

### App Service Instances
- **Format**: `app-<project>-<environment>-<region>` (with optional suffix if needed)
- **Example**: `app-cratenfc-testflight-westus` or `app-cratenfc-testflight-westus-001`
- **Description**: 
  - `app`: Indicates an App Service instance.
  - Must be globally unique; append a numeric suffix (e.g., `-001`) if the base name is taken.
- **Use Case**: Hosts the multi-container deployment for the TestFlight environment.

### Subscriptions (Optional)
- **Format**: `<project>-<environment>`
- **Example**: `CrateNfc-Testflight`
- **Description**: 
  - Capitalized for readability (Azure is case-insensitive).
  - Aligns with resource naming for consistency.
- **Use Case**: Identifies the subscription for the CrateNFC TestFlight environment.

## Examples
| Resource Type      | Name                        | Description                          |
|---------------------|-----------------------------|--------------------------------------|
| Resource Group      | `rg-cratenfc-testflight-westus` | TestFlight environment in East US   |
| App Service Plan    | `plan-cratenfc-testflight-westus` | Compute plan for TestFlight         |
| App Service         | `app-cratenfc-testflight-westus` | Web app for TestFlight (unique)     |
| Subscription        | `CrateNfc-Testflight` | Subscription for TestFlight (optional) |

## Multi-Account Considerations
- For the second company/account, prepend a company identifier (e.g., `org1`) to distinguish resources:
  - Resource Group: `rg-org1-cratenfc-testflight-westus`
  - App Service Plan: `plan-org1-cratenfc-testflight-westus`
  - App Service: `app-org1-cratenfc-testflight-westus`
- Adjust the region (e.g., `westeurope`) if the second company has different geographic requirements.

## Future Expansions
- **Production Environment**: Use `prod` instead of `testflight` (e.g., `rg-cratenfc-prod-westus`).
- **Additional Regions**: Add new resource groups with different regions (e.g., `rg-cratenfc-testflight-westeurope`).
- **Other Resource Types**: Extend the convention (e.g., `acr-cratenfc-westus` for Azure Container Registry).

## Example for Creating a Resources

- Create resources using Azure CLI:

  ```bash
  az group create --name rg-cratenfc-testflight-westus --location westus
  az appservice plan create --name plan-cratenfc-testflight-westus --resource-group rg-cratenfc-testflight-westus --sku B1 --is-linux
  az webapp create --resource-group rg-cratenfc-testflight-westus --plan plan-cratenfc-testflight-westus --name app-cratenfc-testflight-westus --multicontainer-config-type compose --multicontainer-config-file docker-compose.yml
  ```

---

# Additional Setup - Providers

Azure resource providers (e.g., Microsoft.Web) need to be registered with a subscription before you can deploy resources that depend on them. When you first use a service (like App Service) in a subscription, Azure may not automatically register the provider, especially if you’re using the CLI or ARM templates directly. The registration process enables the subscription to interact with the Microsoft.Web namespace, which supports Web Apps, App Service Plans, and related features.

1. List Resource Providers
Check the current registration status of all providers in your subscription:  

    ```sh
    az provider list --output table
    ```

Look for Microsoft.Web in the namespace column. If the registrationState is NotRegistered, you’ll need to register it.

2. Register the Microsoft.Web Provider
Register the Microsoft.Web namespace:  

    ```sh
    az provider register --namespace Microsoft.Web
    az provider register --namespace Microsoft.Quota
    az provider register --namespace Microsoft.Compute
    ```
    * This command initiates the registration process, which can take a few minutes (typically 5-10 minutes).


    ```sh
    az provider show --namespace Microsoft.Web --query registrationState --output tsv
    az provider show --namespace Microsoft.Quota --query registrationState --output tsv
    az provider show --namespace Microsoft.Compute --query registrationState --output tsv
    ```

    Once registered, we can create the App Service Plan (in the steps below).

---

# Deploying the CrateNFC API to Azure App Service

This guide details how to deploy the CrateNFC multi-container application (`crateapi` and `unfurlapi`) to Azure App Service using images from GitHub Container Registry (GHCR). The deployment targets a TestFlight environment for iOS beta testing in the East US region.

## Overview
- **Images**:
  - `ghcr.io/lilrobo/cratenfcapi-crateapi:latest`
  - `ghcr.io/lilrobo/cratenfcapi-unfurlapi:latest`
- **Azure Resources**:
  - Resource Group: `rg-cratenfc-testflight-westus`
  - App Service Plan: `plan-cratenfc-testflight-westus`
  - App Service: `app-cratenfc-testflight-westus` (or a unique variation)
- **Environment**: TestFlight (iOS beta testing)
- **Region**: East US (`westus`)

## Prerequisites
1. **Azure CLI**: Install the Azure CLI ([instructions](https://docs.microsoft.com/en-us/cli/azure/install-azure-cli)).
2. **GitHub PAT**: Create a Personal Access Token with `read:packages` scope to access GHCR ([GitHub Docs](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token)).
3. **Azure Subscription**: Ensure you have access to the subscription (`CrateNfc-Testflight` or equivalent).

## Deployment Steps

### 1. Set Up Azure Resources
1. **Set the Subscription**  
   Ensure you’re using the correct subscription:  
   ```bash
   az account list --output table
   az account set --subscription "CrateNfc-Testflight"
   ```
2. Create the Resource Group  
    ```sh
    az group create --name rg-cratenfc-testflight-westus --location westus
    ```
3. Create the App Service Plan  
    The **App Service Plan** defines the underlying compute resources (e.g., VM size, number of instances) required to host your application in the Azure App Service environment. For the CrateNFC TestFlight deployment, this plan supports the multi-container setup (`crateapi` and `unfurlapi`) in the East US region.

    ```sh
    az appservice plan create --name plan-cratenfc-testflight-westus --resource-group rg-cratenfc-testflight-westus --sku F1 --is-linux
    ```
    
    * F1 is the free tier; upgrade (e.g., to B1 or S1) for better performance and scaling.
       *SKU F1: This is the free tier with 1 GB RAM and a shared vCPU, suitable for basic testing or proof-of-concept deployments. It has limited compute time (60 minutes/day) and does not support custom domains or SSL. Best for experimentation but not suitable for production. 
    * B1 is the basic tier; scale up (e.g., to S1) for production.
    * SKU B1: This is the basic tier with 1.75 GB RAM and 1 vCPU, suitable for testing or low-traffic environments like TestFlight. It’s cost-effective for initial deployment.

    > *Scaling Advice*: 
    >
    > Scale up to S1 (or higher) for production or if you experience performance issues (e.g., high CPU usage or slow response times). Use the Azure Portal under "Scale Up" to adjust.
    >
    > Monitor usage via Azure Monitor to determine scaling needs.
    >
    > Notes: Ensure the `--is-linux` flag is included, as your containers run on a Linux-based App Service.


4. Create the Web App  
    The Web App is the Azure App Service instance that hosts your multi-container application (`crateapi` and `unfurlapi`) using Docker Compose. It relies on the App Service Plan (`plan-cratenfc-testflight-westus`) created earlier to provide the compute resources. This step deploys the GHCR images to the TestFlight environment.

    Create the Web App:

    ```sh
    az webapp create \
      --resource-group rg-cratenfc-testflight-westus \
      --plan plan-cratenfc-testflight-westus \
      --name app-cratenfc-testflight-westus \
      --multicontainer-config-type compose \
      --multicontainer-config-file docker-compose.yml
    ```

      Set the container configuration:
    
    ```sh
    az webapp config container set \
      --resource-group rg-cratenfc-testflight-westus \
      --name app-cratenfc-testflight-westus \
      --container-registry-url https://ghcr.io \
      --container-registry-user liladas \
      --container-registry-password $GITHUB_PAT_LILROBO_AZURE_PULL \
      --multicontainer-config-type compose \
      --multicontainer-config-file docker-compose-azure.yml
    ```

    Show the container configuration:

    ```sh
    az webapp config container show \
      --resource-group rg-cratenfc-testflight-westus \
      --name app-cratenfc-testflight-westus \
      --output table
    ```

    Restart the Web App:

    ```sh
    az webapp restart \
      --resource-group rg-cratenfc-testflight-westus \
      --name app-cratenfc-testflight-westus
    ```

    View the logs:

    ```sh
    az webapp log tail --resource-group rg-cratenfc-testflight-westus --name app-cratenfc-testflight-westus
    ```