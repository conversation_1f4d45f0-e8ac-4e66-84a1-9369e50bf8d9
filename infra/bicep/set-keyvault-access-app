#!/bin/bash

# === Configuration ===
SUBSCRIPTION_ID="fef123f1-e781-4d6b-8c16-edc2d84188d2"
RESOURCE_GROUP="rg-cratenfc-testflight-westus"
APP_NAME="app-cratenfc-testflight-westus"
KV_NAME="kv-cratenfc-testflight"
KEYVAULT_SCOPE="/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.KeyVault/vaults/$KV_NAME"

# === Assign managed identity to App Service ===
echo "🔐 Assigning system-managed identity to $APP_NAME..."
az webapp identity assign \
  --resource-group $RESOURCE_GROUP \
  --name $APP_NAME

# === Get the managed identity's principal ID ===
echo "📛 Fetching principal ID for $APP_NAME..."
PRINCIPAL_ID=$(az webapp show \
  --name $APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --query identity.principalId \
  --output tsv)

echo "✅ Principal ID: $PRINCIPAL_ID"

# === Assign Role to Managed Identity for Key Vault Access ===
echo "🔑 Assigning 'Key Vault Secrets User' role to $APP_NAME at scope: $KEYVAULT_SCOPE..."
az role assignment create \
  --assignee-object-id $PRINCIPAL_ID \
  --assignee-principal-type ServicePrincipal \
  --role "Key Vault Secrets User" \
  --scope "$KEYVAULT_SCOPE"

echo "🎉 Key Vault access granted."
