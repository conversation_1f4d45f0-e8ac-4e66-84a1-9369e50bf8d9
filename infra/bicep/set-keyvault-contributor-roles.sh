#!/bin/bash

# List of user email addresses
users=(
    "<EMAIL>"
    "<EMAIL>"
    "<EMAIL>"
)

# Key Vault scope
KEYVAULT_SCOPE="/subscriptions/fef123f1-e781-4d6b-8c16-edc2d84188d2/resourceGroups/rg-cratenfc-testflight-westus/providers/Microsoft.KeyVault/vaults/kv-cratenfc-testflight"

# Loop over the users and assign roles
for user in "${users[@]}"; do
  echo "Assigning role to $user..."
  USER_OBJECT_ID=$(az ad user show --id "$user" --query id -o tsv)
  if [ -n "$USER_OBJECT_ID" ]; then
    # Assign Key Vault Contributor role (management plane access)
    az role assignment create \
        --role "Key Vault Contributor" \
        --assignee "$user" \
        --scope "$KEYVAULT_SCOPE"
    echo "Assigned Key Vault Contributor role to $user"
    
    # Assign Key Vault Secrets Officer role (data plane access to view secrets)
    az role assignment create \
        --role "Key Vault Secrets Officer" \
        --assignee "$user" \
        --scope "$KEYVAULT_SCOPE"
    echo "Assigned Key Vault Secrets Officer role to $user"
  else
    echo "Failed to find object ID for $user"
  fi
done
