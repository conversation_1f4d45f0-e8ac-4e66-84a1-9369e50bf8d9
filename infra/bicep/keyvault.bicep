// Azure Bicep file to create a Key Vault with a list of secrets
/*
    az deployment group create \
        --resource-group "rg-cratenfc-testflight-westus" \
        --template-file keyvault.bicep \
        --mode Incremental \
        --name "deployment-bicep-keyvault-cratenfc-testflight-westus"
*/

// Add Additional RBAC Policy "Key Vault Contributor" to View & Edit the Key Vault
// See: set-keyvault-contributor-roles.sh
/* 
az role assignment create \
    --role "Key Vault Contributor" \
    --assignee "<EMAIL>" \
    --scope "/subscriptions/fef123f1-e781-4d6b-8c16-edc2d84188d2/resourceGroups/rg-cratenfc-testflight-westus/providers/Microsoft.KeyVault/vaults/cratenfc-kv-westus"
*/

param location string = 'westus'
param keyVaultName string = 'kv-cratenfc-testflight'

// Define the list of secrets to stage (without values)
var keys = [
  'PostgresPassword'
  'VmSshPublicKey'
  'VmSshPrivateKey'
  'GitHubPat'
  'ShopifyApiKey'
  'CustomApiKey'
]

resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' = {
  name: keyVaultName
  location: location
  properties: {
    sku: {
      family: 'A'
      name: 'standard'
    }
    tenantId: subscription().tenantId
    enableSoftDelete: true
    enablePurgeProtection: true
    accessPolicies: [] // Keep empty since we're using RBAC
    enableRbacAuthorization: true // Enable Azure RBAC for Key Vault
  }
}

// Stage each secret with a placeholder value
resource stagedSecrets 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = [
  for secretName in keys: {
    parent: keyVault
    name: secretName
    properties: {
      value: 'placeholder'
    }
  }
]

output keyVaultId string = keyVault.id
