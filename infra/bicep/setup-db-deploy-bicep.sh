KEYVAULT_ID=$(az keyvault show \
    --resource-group "rg-cratenfc-testflight-westus" \
    --name "kv-cratenfc-testflight" \
    --query id -o tsv)

az keyvault secret set \
    --vault-name "kv-cratenfc-testflight" \
    --name "DockerRegistryServerUsername" \
    --value "liladas"
    
az keyvault secret set \
    --vault-name "kv-cratenfc-testflight" \
    --name "DockerRegistryServerPassword" \
    --value "****************************************"

az keyvault secret set \
    --vault-name "kv-cratenfc-testflight" \
    --name "PostgresAdminPassword" \
    --value "2UxMZpK&^5it@qW9"

az keyvault secret set \
    --vault-name "kv-cratenfc-testflight" \
    --name "PostgresAdminUsername" \
    --value "cratenfcadmin"

az keyvault secret set --name PostgresPassword --value "&^MZpK5it@33qW9" --vault-name kv-cratenfc-testflight

az keyvault secret set \
    --vault-name "kv-cratenfc-testflight" \
    --name "PostgresPassword" \
    --value "&^MZpK5it@33qW9"

az keyvault secret set \
    --vault-name "kv-cratenfc-testflight" \
    --name "PostgresUsername" \
    --value "cratenfc"


ADMIN_PASSWORD=$(az keyvault secret show \
    --vault-name "kv-cratenfc-testflight" \
    --name "PostgresAdminPassword" \
    --query value -o tsv)


ADMIN_USERNAME=$(az keyvault secret show \
    --vault-name "kv-cratenfc-testflight" \
    --name "PostgresAdminUsername" \
    --query value -o tsv)


az deployment group create \
    --resource-group "rg-cratenfc-testflight-westus" \
    --template-file postgres.bicep \
    --parameters keyVaultId="$KEYVAULT_ID" adminPassword="$ADMIN_PASSWORD" adminUsername="$ADMIN_USERNAME" \
    --mode Incremental \
    --name "deployment-bicep-db-cratenfc-testflight-westus"

## Get deployment outputs

POSTGRES_FQDN=$(az deployment group show \
    --resource-group "rg-cratenfc-testflight-westus" \
    --name "deployment-bicep-db-cratenfc-testflight-westus" \
    --query properties.outputs.postgresServerFqdn.value -o tsv)
POSTGRES_DB_NAME=$(az deployment group show \
    --resource-group "rg-cratenfc-testflight-westus" \
    --name "deployment-bicep-db-cratenfc-testflight-westus" \
    --query properties.outputs.postgresDatabaseName.value -o tsv)

echo "PostgreSQL Server FQDN: $POSTGRES_FQDN"
echo "Database Name: $POSTGRES_DB_NAME"


### Setup Firewall Rules
## Punch in through firewall
IP_ADDR=$(curl -s ipv4.icanhazip.com)
az postgres flexible-server firewall-rule create \
    --resource-group "rg-cratenfc-testflight-westus" \
    --name "psql-cratenfc-testflight-westus" \
    --rule-name "AllowDeveloperMachine-Adam" \
    --start-ip-address "$IP_ADDR" \
    --end-ip-address "$IP_ADDR"

az postgres flexible-server firewall-rule list \
    --resource-group "rg-cratenfc-testflight-westus" \
    --name "psql-cratenfc-testflight-westus" \
    --query "[].name" \
    --output tsv

APP_SERVICE_OUTBOUND_IPS=$(az webapp show \
    --resource-group "rg-cratenfc-testflight-westus" \
    --name "app-cratenfc-testflight-westus" \
    --query "outboundIpAddresses" \
    --output tsv)
echo $APP_SERVICE_OUTBOUND_IPS


# Convert the comma-separated list to an array
IFS=',' read -r -a IPS <<< "$APP_SERVICE_OUTBOUND_IPS"

# Loop over the IPs and add a firewall rule for each
for ip in "${IPS[@]}"; do
  echo "Adding firewall rule for IP $ip..."
  az postgres flexible-server firewall-rule create \
      --resource-group "rg-cratenfc-testflight-westus" \
      --name "psql-cratenfc-testflight-westus" \
      --rule-name "AllowAppServiceIP-$ip" \
      --start-ip-address "$ip" \
      --end-ip-address "$ip"
done
