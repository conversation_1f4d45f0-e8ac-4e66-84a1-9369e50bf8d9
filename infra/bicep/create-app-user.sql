CREATE ROLE cratenfc WITH LOGIN PASSWORD '&^MZpK5it@33qW9';
GRANT CONNECT ON DATABASE cratedb TO cratenfc;
GRANT USAGE ON SCHEMA public TO cratenfc;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO cratenfc;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO cratenfc;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO cratenfc;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO cratenfc;
