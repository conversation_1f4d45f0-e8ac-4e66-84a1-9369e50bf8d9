// Description: Bicep file to deploy a PostgreSQL server and database in Azure
// Usage: az deployment group create --resource-group "rg-cratenfc-testflight-westus" --template-file db.bicep --parameters adminPassword="your_password_here"
// Warning: this configuration doesn't include a vnet rule for the PostgreSQL server
//          and allows all Azure services to connect to the server. This is for setup/testing only.
//          For production, you should restrict access to specific IP addresses or subnets.
// Note: Basic Tier App Plan doesn't include
param location string = 'westus'
param postgresServerName string = 'psql-cratenfc-testflight-westus'
param postgresDatabaseName string = 'cratedb'
param adminUsername string // Retrieved from Key Vault
@secure()
param adminPassword string // Retrieved from Key Vault
param keyVaultId string

resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' existing = {
  name: last(split(keyVaultId, '/'))
}

// Create the PostgreSQL Flexible Server
resource postgresServer 'Microsoft.DBforPostgreSQL/flexibleServers@2021-06-01' = {
  name: postgresServerName
  location: location
  sku: {
    name: 'Standard_B1ms' // Basic tier, 1 vCore, 2 GB RAM (matches your current setup)
    tier: 'Burstable'
  }
  properties: {
    version: '16' // PostgreSQL 16
    administratorLogin: adminUsername
    administratorLoginPassword: adminPassword
    network: {}
    storage: {
      storageSizeGB: 32 // 32 GB storage
    }
    backup: {
      backupRetentionDays: 7
      geoRedundantBackup: 'Disabled'
    }
    availabilityZone: ''
  }
}

// Create the database inside the PostgreSQL server
resource postgresDatabase 'Microsoft.DBforPostgreSQL/flexibleServers/databases@2021-06-01' = {
  parent: postgresServer
  name: postgresDatabaseName
  properties: {
    charset: 'UTF8'
    collation: 'en_US.utf8'
  }
}

// Store the admin username in Key Vault
resource adminUsernameSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  parent: keyVault
  name: 'PostgresAdminUsername'
  properties: {
    value: adminUsername
  }
}

// Configure firewall rule to allow Azure services (temporary, for setup/testing)
resource firewallRule 'Microsoft.DBforPostgreSQL/flexibleServers/firewallRules@2021-06-01' = {
  parent: postgresServer
  name: 'AllowAzureServices'
  properties: {
    startIpAddress: '0.0.0.0'
    endIpAddress: '0.0.0.0'
  }
}

output postgresServerFqdn string = postgresServer.properties.fullyQualifiedDomainName
output postgresDatabaseName string = postgresDatabase.name
