APP_SERVICE_OUTBOUND_IPS=$(az webapp show \
    --resource-group "rg-cratenfc-testflight-westus" \
    --name "app-cratenfc-testflight-westus" \
    --query "outboundIpAddresses" \
    --output tsv)
echo $APP_SERVICE_OUTBOUND_IPS


# Convert the comma-separated list to an array
IFS=',' read -r -a IPS <<< "$APP_SERVICE_OUTBOUND_IPS"

# Loop over the IPs and add a firewall rule for each
for ip in "${IPS[@]}"; do
  # Sanitize the IP address for the rule name by replacing dots with underscores
  rule_name="AllowAppServiceIP-$(echo $ip | tr '.' '_')"
  echo "Adding firewall rule for IP $ip with rule name $rule_name..."
  az postgres flexible-server firewall-rule create \
      --resource-group "rg-cratenfc-testflight-westus" \
      --name "psql-cratenfc-testflight-westus" \
      --rule-name "$rule_name" \
      --start-ip-address "$ip" \
      --end-ip-address "$ip"
done

az postgres flexible-server firewall-rule list \
    --resource-group "rg-cratenfc-testflight-westus" \
    --name "psql-cratenfc-testflight-westus" \
    --query "[].{name:name, startIpAddress:startIpAddress, endIpAddress:endIpAddress}" \
    --output table
