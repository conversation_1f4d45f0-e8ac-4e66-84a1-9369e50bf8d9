This file is a merged representation of the entire codebase, combined into a single document by Repomix.

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded

Additional Info:
----------------

================================================================
Directory Structure
================================================================
.github/
  workflows/
    lint-and-build.yml
CrateApi/
  Controllers/
    ChallengeController.cs
    CollectionController.cs
    HealthController.cs.cs
    TrackController.cs
    UnfurlController.cs
    UserController.cs
  Helpers/
    TokenGenerator.cs
  Infrastructure/
    ApiKeyMiddleware.cs
    AuthenticationExtensions.cs
    HttpClientExtensions.cs
    SwaggerExtensions.cs
  Models/
    Challenge.cs
    JwtSettings.cs
  Properties/
    launchSettings.json
  appsettings.json
  CrateApi.csproj
  CrateApi.http
  DiConfig.cs
  Dockerfile
  Program.cs
CrateApi.Common/
  Attributes/
    UrlValidator.cs
  Dto/
    Request/
      TrackRequestDto.cs
      UnfurlUrlRequestDto.cs
    Response/
      ErrorResponseDto.cs
      TrendingTrackResponseDto.cs
      UnfurlTrackResponseDto.cs
  Exceptions/
    ExceptionResultHandler.cs
    ScraperException.cs
  Unfurl/
    AppleTrack.cs
    GenericTrack.cs
    ITrack.cs
    SpotifyTrack.cs
    YouTubeMusicTrack.cs
  CrateApi.Common.csproj
  ServiceSettings.cs
CrateApi.Data/
  Migrations/
    20250125135804_new.cs
    20250125135804_new.Designer.cs
    20250125152127_new2.cs
    20250125152127_new2.Designer.cs
    20250125181807_new3.cs
    20250125181807_new3.Designer.cs
    20250126015948_new5.cs
    20250126015948_new5.Designer.cs
    20250126131245_new6.cs
    20250126131245_new6.Designer.cs
    20250205002728_mig01.cs
    20250205002728_mig01.Designer.cs
    20250209185541_AddChallenge.cs
    20250209185541_AddChallenge.Designer.cs
    CrateDbContextModelSnapshot.cs
  Models/
    Challenge.cs
    Collection.cs
    NfcCard.cs
    Track.cs
    User.cs
  CrateApi.Data.csproj
  CrateDbContext.cs
CrateApi.Services/
  Abstractions/
    IClient.cs
  Database/
    CollectionService.cs
    DatabaseService.cs
    TrackService.cs
  Logic/
    DubCoClient.cs
    UnfurlService.cs
  Mappings/
    TrackMapper.cs
    TrendingTrackMapper.cs
  CrateApi.Services.csproj
  UserManager.cs
UnfurlApi/
  Controllers/
    ErrorController.cs
    UnfurlController.cs
  Infrastructure/
    HttpClientExtensions.cs
    SwaggerExtensions.cs
  Properties/
    launchSettings.json
  appsettings.json
  DiConfig.cs
  Dockerfile
  Program.cs
  UnfurlApi.csproj
UnfurlApi.Services/
  Clients/
    AppleClient.cs
    GenericClient.cs
    SpotifyClient.cs
    YouTubeMusicClient.cs
  Platforms/
    AppleService.cs
    GenericService.cs
    SpotifyService.cs
    YouTubeMusicService.cs
  UnfurlApi.Services.csproj
.editorconfig
.gitattributes
.gitignore
CLAUDE.md
CrateCS.sln
docker-compose.yml
Makefile
README.md

================================================================
Files
================================================================

================
File: .github/workflows/lint-and-build.yml
================
name: Lint and Build Docker Images

on:
  pull_request:
  push:
    branches:
      - main
    tags:
      - '*'
  workflow_dispatch:
    inputs:
      tag:
        description: 'Optional custom tag for Docker images (e.g., my-test-tag)'
        required: false
        default: ''

permissions:
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io
  CRATEAPI_IMAGE_NAME: ghcr.io/lilrobo/cratenfcapi-crateapi
  UNFURLAPI_IMAGE_NAME: ghcr.io/lilrobo/cratenfcapi-unfurlapi

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.x'
      - name: Restore dependencies
        run: dotnet restore
      - name: Build with dotnet
        run: dotnet build --no-restore /warnaserror

  build-crateapi:
    runs-on: ubuntu-latest
    needs: lint  # Waits for lint to complete
    if: always()  # Runs even if lint fails
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata for CrateApi
        id: meta-crateapi
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.CRATEAPI_IMAGE_NAME }}
          flavor: |
            latest=false
          tags: |
            type=raw,value=latest,enable=${{ github.event_name == 'push' && github.ref_name == 'main' }},event=push
            type=semver,pattern={{version}},enable=${{ github.event_name == 'push' }}
            type=ref,event=tag,enable=${{ github.event_name == 'push' }}
            type=ref,event=branch,enable=${{ github.event_name == 'push' && github.ref_name != 'main' }}
            type=raw,value={{inputs.tag}},event=workflow_dispatch,enable=${{ github.event_name == 'workflow_dispatch' }}
            type=ref,event=pr,enable=${{ github.event_name == 'pull_request' }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push CrateApi
        uses: docker/build-push-action@v5
        with:
          context: .
          file: CrateApi/Dockerfile
          # platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta-crateapi.outputs.tags }}

  build-unfurlapi:
    runs-on: ubuntu-latest
    needs: lint  # Waits for lint to complete
    if: always()  # Runs even if lint fails
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata for UnfurlApi
        id: meta-unfurlapi
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.UNFURLAPI_IMAGE_NAME }}
          flavor: |
            latest=false
          tags: |
            type=raw,value=latest,enable=${{ github.event_name == 'push' && github.ref_name == 'main' }},event=push
            type=semver,pattern={{version}},enable=${{ github.event_name == 'push' }}
            type=ref,event=tag,enable=${{ github.event_name == 'push' }}
            type=ref,event=branch,enable=${{ github.event_name == 'push' && github.ref_name != 'main' }}
            type=raw,value={{inputs.tag}},event=workflow_dispatch,enable=${{ github.event_name == 'workflow_dispatch' }}
            type=ref,event=pr,enable=${{ github.event_name == 'pull_request' }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push UnfurlApi
        uses: docker/build-push-action@v5
        with:
          context: .
          file: UnfurlApi/Dockerfile
          # platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta-unfurlapi.outputs.tags }}

================
File: CrateApi/Controllers/ChallengeController.cs
================
using System.Security.Cryptography;
using System.Text;
using CrateApi.Data;
using CrateApi.Data.Models;
using CrateApi.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CrateApi.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class ChallengeController(CrateDbContext context) : ControllerBase
{
    private readonly CrateDbContext _context = context;
    private const int CHALLENGE_LENGTH = 32;
    private const int CHALLENGE_EXPIRY_MINUTES = 5;

    [HttpPost]
    [ProducesResponseType(typeof(ChallengeResponse), 200)]
    public async Task<IActionResult> GetChallenge([FromBody] GetChallengeRequest request)
    {
        if (string.IsNullOrEmpty(request.Uid) || string.IsNullOrEmpty(request.Atr))
        {
            return BadRequest("UID and ATR are required");
        }

        var challenge = GenerateChallenge();

        var nfcCard = await _context.NfcCards.FirstOrDefaultAsync(c => c.Uid == request.Uid && c.Atr == request.Atr);

        if (nfcCard == null)
        {
            nfcCard = new NfcCard
            {
                Uid = request.Uid,
                Atr = request.Atr,
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
            };
            _context.NfcCards.Add(nfcCard);
            await _context.SaveChangesAsync();
        }

        var cardChallenge = new Challenge
        {
            NfcCardId = nfcCard.Id,
            ChallengeValue = challenge,
            CreatedAt = DateTime.UtcNow,
            IsUsed = false,
        };

        await _context.Challenges.AddAsync(cardChallenge);
        await _context.SaveChangesAsync();

        return Ok(new ChallengeResponse { Challenge = challenge });
    }

    [HttpPost("verify")]
    [ProducesResponseType(typeof(bool), 200)]
    public async Task<IActionResult> VerifyChallenge([FromBody] VerifyChallengeRequest request)
    {
        if (
            string.IsNullOrEmpty(request.Uid)
            || string.IsNullOrEmpty(request.Atr)
            || string.IsNullOrEmpty(request.Challenge)
            || string.IsNullOrEmpty(request.Response)
        )
        {
            return BadRequest("All fields are required");
        }

        var cardChallenge = await _context
            .Challenges.Include(c => c.NfcCard)
            .Where(c =>
                c.NfcCard!.Uid == request.Uid
                && c.ChallengeValue == request.Challenge
                && !c.IsUsed
                && c.CreatedAt > DateTime.UtcNow.AddMinutes(-CHALLENGE_EXPIRY_MINUTES)
            )
            .OrderByDescending(c => c.CreatedAt)
            .FirstOrDefaultAsync();

        if (cardChallenge == null)
        {
            return BadRequest("Invalid or expired challenge");
        }

        var expectedResponse = CalculateResponse(request.Uid, request.Atr, request.Challenge);

        cardChallenge.IsUsed = true;
        if (cardChallenge.NfcCard != null)
        {
            cardChallenge.NfcCard.LastUsed = DateTime.UtcNow;
        }
        await _context.SaveChangesAsync();

        if (request.Response == expectedResponse)
        {
            return Ok(true);
        }

        return BadRequest("Invalid response");
    }

    // Debug endpoints
    [HttpGet("cards")]
    public async Task<IActionResult> GetAllCards()
    {
        var cards = await _context.NfcCards.Include(c => c.Challenges).ToListAsync();
        return Ok(cards);
    }

    [HttpPost("cards")]
    public async Task<IActionResult> CreateCard([FromBody] NfcCard card)
    {
        card.CreatedAt = DateTime.UtcNow;
        _context.NfcCards.Add(card);
        await _context.SaveChangesAsync();
        return Ok(card);
    }

    // Helper endpoint for testing hash calculation
    [HttpPost("hash")]
    public IActionResult CalculateHash([FromBody] HashTestRequest request)
    {
        var response = CalculateResponse(request.Uid, request.Atr, request.Challenge);
        return Ok(new { hash = response });
    }

    private string GenerateChallenge()
    {
        var challengeBytes = new byte[CHALLENGE_LENGTH];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(challengeBytes);
        return Convert.ToHexString(challengeBytes);
    }

    private string CalculateResponse(string uid, string atr, string challenge)
    {
        using var sha256 = SHA256.Create();
        var input = $"{uid}{atr}{challenge}";
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
        return Convert.ToHexString(hashBytes);
    }
}

================
File: CrateApi/Controllers/CollectionController.cs
================
using CrateApi.Data.Models;
using CrateApi.Services.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CrateApi.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class CollectionController(ICollectionService collectionService) : ControllerBase
{
    [HttpGet]
    [Authorize]
    public async Task<IActionResult> GetCollections([FromQuery] int start, [FromQuery] int size = 20)
    {
        var collections = await collectionService.GetPaged(start, size);
        return Ok(collections);
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> Add([FromBody] Collection collection)
    {
        var result = await collectionService.AddAsync(collection);
        return Ok(result);
    }

    [HttpPost("{id}/add")]
    [Authorize]
    public async Task<IActionResult> AddTracks(int id, [FromBody] List<int> trackIds)
    {
        var result = await collectionService.AddTracksToCollection(id, trackIds);
        return Ok();
    }

    [HttpDelete("{id}")]
    [Authorize]
    public async Task<IActionResult> Delete([FromRoute] int id)
    {
        await collectionService.DeleteAsync(id);
        return NoContent();
    }
}

================
File: CrateApi/Controllers/HealthController.cs.cs
================
using Microsoft.AspNetCore.Mvc;

namespace CrateApi.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
public class HealthController : ControllerBase
{
    [HttpGet]
    public IActionResult Health()
    {
        return Ok("Test");
    }
}

================
File: CrateApi/Controllers/TrackController.cs
================
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Services.Database;
using CrateApi.Services.Mappings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CrateApi.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class TrackController(ITrackService service) : ControllerBase
{
    [HttpGet("trending")]
    [ProducesResponseType(typeof(List<TrendingTrackResponseDto>), 200)]
    public async Task<IActionResult> GetTrending([FromQuery] int start = 0, [FromQuery] int size = 20)
    {
        var results = await service.GetTrendingAsync(start, size);
        return Ok(results);
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> Add([FromBody] TrackRequestDto track)
    {
        var mapper = new TrackMapper();
        await service.AddAsync(mapper.ToEntity(track));
        return Ok();
    }

    [HttpDelete("{id}")]
    [Authorize]
    public async Task<IActionResult> Delete([FromRoute] int id)
    {
        await service.DeleteAsync(id);
        return NoContent();
    }

    [HttpDelete]
    [Authorize]
    public async Task<IActionResult> DeleteAll()
    {
        await service.DeleteAllAsync();
        return NoContent();
    }
}

================
File: CrateApi/Controllers/UnfurlController.cs
================
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Exceptions;
using CrateApi.Services;
using LanguageExt.Pipes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using LanguageExt;
using static LanguageExt.Prelude;
namespace CrateApi.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class UnfurlController(IUnfurlService unfurlService, ILogger<UnfurlController> logger) : ControllerBase
{
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(UnfurledTrackResponseDto), 200)]
    public async Task<IActionResult> UnfurlUrl([FromBody] UnfurlUrlRequestDto model)
    {
        var response = await TryAsync(async () => await unfurlService.UnfurlTrackUrl(model)).Try();
        return response.Match<IActionResult>(
            result => result.Match<IActionResult>(Ok, NoContent),
            ExceptionResult.Handle(logger)
        );
    }

    [HttpPost("anonymous")]
    [ProducesResponseType(typeof(UnfurledTrackResponseDto), 200)]
    public async Task<IActionResult> UnfurlUrlAnonymous([FromBody] UnfurlUrlRequestDto model)
    {
        var response = await TryAsync(async () => await unfurlService.UnfurlTrackUrlAnonymous(model)).Try();
        return response.Match<IActionResult>(
            result => result.Match<IActionResult>(Ok, NoContent),
            ExceptionResult.Handle(logger)
        );
    }
}

================
File: CrateApi/Controllers/UserController.cs
================
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using CrateApi.Data;
using CrateApi.Data.Models;
using CrateApi.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CrateApi.Controllers;

public record LoginRequest(string Email, string Password);

public record CreateUserRequest(string Email, string Password, string Username);

public record UserModel(string UserId, string Email, string Username, string Token);

[ApiController]
[Route("api/v1/[controller]")]
public class UserController(TokenGenerator generator, CrateDbContext context) : ControllerBase
{
    [HttpPost("login")]
    [ProducesResponseType(typeof(UserModel), 200)]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        var user = await context.Users.FirstOrDefaultAsync(u => u.Email == request.Email && u.Password == request.Password);

        if (user == null)
        {
            return Unauthorized("Invalid credentials");
        }

        var token = generator.GenerateToken(user.Id.ToString(), user.Email);

        generator.DecodeToken(token);

        return Ok(new UserModel(user.Id.ToString(), user.Email, user.Username, token));
    }

    [HttpPost("create")]
    [ProducesResponseType(typeof(User), 200)]
    public async Task<IActionResult> Create([FromBody] CreateUserRequest request)
    {
        var user = new User
        {
            Email = request.Email,
            Username = request.Username,
            Password = request.Password,
        };

        var dbUser = await context.Users.AddAsync(user);
        await context.SaveChangesAsync();
        return Ok(dbUser.Entity);
    }

    [HttpGet]
    [ProducesResponseType(typeof(List<User>), 200)]
    public async Task<IActionResult> ListUsers()
    {
        var dbUser = await context.Users.ToListAsync();
        return Ok(dbUser);
    }

    [Authorize]
    [HttpPost("refresh")]
    public IActionResult RefreshToken()
    {
        var email = User.FindFirst(ClaimTypes.Email)?.Value ?? "";
        System.Console.WriteLine("\n\n CLAIMS \n\n\n");
        System.Console.WriteLine(string.Join("\n", User.Claims.Select(e => $"{e.Type} {e.Value}")));
        if (email is null)
        {
            return Unauthorized();
        }
        var userId = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value ?? "";
        var token = generator.GenerateToken(userId, email);
        return Ok(new { token });
    }
}

================
File: CrateApi/Helpers/TokenGenerator.cs
================
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using CrateApi.Models;
using Microsoft.IdentityModel.Tokens;

namespace CrateApi.Helpers;

public class TokenGenerator
{
    private readonly JwtSettings _jwtSettings;

    public TokenGenerator(IConfiguration configuration)
    {
        _jwtSettings = configuration.GetSection("Jwt").Get<JwtSettings>()!;
    }

    public string GenerateToken(string userId, string email)
    {
        var secretKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key));

        var signingCredentials = new SigningCredentials(secretKey, SecurityAlgorithms.HmacSha256);
        signingCredentials.Key.KeyId = "1";
        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Sub, userId),
            new Claim(JwtRegisteredClaimNames.Email, email),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, DateTime.UtcNow.ToString()),
            new Claim("uid", userId), // Custom claim
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpiryInMinutes),
            Issuer = _jwtSettings.Issuer,
            Audience = _jwtSettings.Audience,
            SigningCredentials = signingCredentials,
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        return tokenString;
    }

    public void DecodeToken(string token)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadToken(token) as JwtSecurityToken;

        Console.WriteLine("Token Header:");
        foreach (var header in jwtToken!.Header)
        {
            Console.WriteLine($"{header.Key}: {header.Value}");
        }

        Console.WriteLine("\nToken Claims:");
        foreach (var claim in jwtToken.Claims)
        {
            Console.WriteLine($"{claim.Type}: {claim.Value}");
        }
    }
}

================
File: CrateApi/Infrastructure/ApiKeyMiddleware.cs
================
namespace CrateApi.Infrastructure;

public class ApiKeyMiddleware
{
    private readonly RequestDelegate _next;
    private readonly string _apiKey;

    public ApiKeyMiddleware(RequestDelegate next, IConfiguration configuration)
    {
        _next = next;
        _apiKey = configuration["ApiKey"] ?? ""; //if null assume debug build and dont validate
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var providedApiKey = context.Request.Headers["ApiKey"].FirstOrDefault();

        if (string.IsNullOrEmpty(_apiKey))
        {
            await _next(context);
            return;
        }

        if (string.IsNullOrEmpty(providedApiKey) || providedApiKey != _apiKey)
        {
            context.Response.StatusCode = 401;
            await context.Response.WriteAsync("Invalid or missing API Key");
            return;
        }

        await _next(context);
    }
}

================
File: CrateApi/Infrastructure/AuthenticationExtensions.cs
================
using System.Text;
using CrateApi.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;

namespace CrateApi.Infrastructure;

public static class AuthenticationExtensions
{
    public static IServiceCollection ConfigureAuthenticationExt(this IServiceCollection services, IConfiguration configuration)
    {
        var jwtSettings =
            configuration.GetSection("Jwt").Get<JwtSettings>() ?? throw new InvalidOperationException("JWT settings are not configured");

        var signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.Key));

        services
            .AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = jwtSettings.Issuer,
                    ValidAudience = jwtSettings.Audience,
                    IssuerSigningKey = signingKey,
                    ClockSkew = TimeSpan.Zero, // Disable default 5 mins clock skew
                };

                // Add debugging
                options.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        Console.WriteLine($"Authentication failed: {context.Exception.Message}");
                        return Task.CompletedTask;
                    },
                    OnTokenValidated = context =>
                    {
                        Console.WriteLine("Token validated");
                        var claims = context.Principal?.Claims.Select(c => $"{c.Type}: {c.Value}");
                        Console.WriteLine($"Claims: {string.Join(", ", claims ?? Array.Empty<string>())}");
                        return Task.CompletedTask;
                    },
                    OnMessageReceived = context =>
                    {
                        Console.WriteLine($"Token received: {context.Token}");
                        return Task.CompletedTask;
                    },
                };
            });

        services.AddAuthorization();
        return services;
    }
}

================
File: CrateApi/Infrastructure/HttpClientExtensions.cs
================
namespace CrateApi.Infrastructure;

public static class HttpClientExtensions
{
    public static IServiceCollection ConfigureHttpClientExt(this IServiceCollection services)
    {
        services.AddHttpClient(
            "SpotifyClient",
            client =>
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0");
                client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
                client.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9");
                client.DefaultRequestHeaders.Add("Accept-Encoding", "identity");
                client.DefaultRequestHeaders.Add("Connection", "keep-alive");
                client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
            }
        );
        return services;
    }
}

================
File: CrateApi/Infrastructure/SwaggerExtensions.cs
================
using Microsoft.OpenApi.Models;

namespace CrateApi.Infrastructure;

public static class SwaggerExtensions
{
    public static IServiceCollection ConfigureSwaggerExt(this IServiceCollection services)
    {
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "CrateApi", Version = "v1" });
            c.CustomSchemaIds(type => $"{type.ToString()}");

            c.AddSecurityDefinition(
                "Bearer",
                new OpenApiSecurityScheme
                {
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Description = "Please enter token: 'Bearer test-token'",
                    Scheme = "Bearer",
                }
            );

            // Add API Key config
            c.AddSecurityDefinition(
                "ApiKey",
                new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.ApiKey,
                    In = ParameterLocation.Header,
                    Name = "ApiKey",
                    Description = "API Key authentication",
                }
            );

            // Add security requirements
            c.AddSecurityRequirement(
                new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "Bearer" },
                        },
                        Array.Empty<string>()
                    },
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "ApiKey" },
                        },
                        Array.Empty<string>()
                    },
                }
            );
        });
        return services;
    }
}

================
File: CrateApi/Models/Challenge.cs
================
namespace CrateApi.Models;

public record GetChallengeRequest
{
    public required string Uid { get; set; }
    public required string Atr { get; set; }
}

public record ChallengeResponse
{
    public required string Challenge { get; set; }
}

public record VerifyChallengeRequest
{
    public required string Uid { get; set; }
    public required string Atr { get; set; }
    public required string Challenge { get; set; }
    public required string Response { get; set; }
}

public record HashTestRequest
{
    public required string Uid { get; set; }
    public required string Atr { get; set; }
    public required string Challenge { get; set; }
}

================
File: CrateApi/Models/JwtSettings.cs
================
namespace CrateApi.Models;

public record JwtSettings
{
    public string Key { get; init; } = "";
    public string Issuer { get; init; } = "";
    public string Audience { get; init; } = "";
    public int ExpiryInMinutes { get; init; }
}

================
File: CrateApi/Properties/launchSettings.json
================
{
    "$schema": "http://json.schemastore.org/launchsettings.json",
    "iisSettings": {
        "windowsAuthentication": false,
        "anonymousAuthentication": true,
        "iisExpress": {
            "applicationUrl": "http://localhost:57457",
            "sslPort": 44363
        }
    },
    "profiles": {
        "https": {
            "commandName": "Project",
            "dotnetRunMessages": true,
            "launchBrowser": true,
            "launchUrl": "swagger",
            "applicationUrl": "http://localhost:8000",
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            }
        },
        "IIS Express": {
            "commandName": "IISExpress",
            "launchBrowser": true,
            "launchUrl": "swagger",
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            }
        }
    }
}

================
File: CrateApi/appsettings.json
================
{
    "ConnectionStrings": {
        "DefaultConnection": "Data Source=../CrateApi.Data/Data/cratedb.db"
    },
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
    "AllowedHosts": "*",
    "ApiKey": "",
    "DubCoEnabled" : false,
    "UnfurlServiceUrl": "http://localhost:8001",
    "Jwt": {
        "Key": "c29tZSB0ZXN0IHRva2VuIDEyMyAxMjM=",
        "Issuer": "crateapi.com",
        "Audience": "crateapi.com",
        "ExpiryInMinutes": 3600
    }
}

================
File: CrateApi/CrateApi.csproj
================
<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <IsDefaultProject>true</IsDefaultProject>
    <WarningsAsErrors>true</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.13" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.13" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="Npgsql" Version="9.0.2" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
    <PackageReference Include="OneOf" Version="3.0.271" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CrateApi.Data\CrateApi.Data.csproj" />
    <ProjectReference Include="..\CrateApi.Common\CrateApi.Common.csproj" />
    <ProjectReference Include="..\CrateApi.Services\CrateApi.Services.csproj" />
  </ItemGroup>

</Project>

================
File: CrateApi/CrateApi.http
================
@CrateApi_HostAddress = http://localhost:5147

GET {{CrateApi_HostAddress}}/weatherforecast/
Accept: application/json

###

================
File: CrateApi/DiConfig.cs
================
using CrateApi.Common;
using CrateApi.Data;
using CrateApi.Helpers;
using CrateApi.Infrastructure;
using CrateApi.Models;
using CrateApi.Services;
using CrateApi.Services.Database;
using CrateApi.Services.Logic;
using Microsoft.EntityFrameworkCore;

namespace CrateApi;

public static class Di
{
    public static void Configure(WebApplicationBuilder builder)
    {
        var services = builder.Services;
        var config = builder.Configuration;

        // Required Microsoft Dependencies
        services.ConfigureSwaggerExt();
        services.AddControllers();
        services.AddRouting(options => options.LowercaseUrls = true);
        services.AddDbContext<CrateDbContext>(options =>
            options
                .UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection"))
                .LogTo(_ => { }, LogLevel.None)
        );
        services.AddScoped<DbContext>(provider => provider.GetService<CrateDbContext>()!);
        services.AddHttpContextAccessor();
        services.ConfigureAuthenticationExt(config);

        // Configuration Helpers
        var serviceSettings = new ServiceSettings(config.GetValue<string>("UnfurlServiceUrl", string.Empty)!);
        services.AddSingleton(serviceSettings);
        // Our service code
        services.ConfigureHttpClientExt();
        services.AddScoped<TokenGenerator>();
        services.AddScoped<ITrackService, TrackService>();
        services.AddScoped<ICollectionService, CollectionService>();
        services.AddScoped<IUserManager, UserManager>();
        services.AddScoped<IUnfurlService, UnfurlService>();
        services.AddSingleton<DubCoClient>();
    }
}

================
File: CrateApi/Dockerfile
================
# CrateApi/Dockerfile

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Install EF Core CLI tools
RUN dotnet tool install --global dotnet-ef
ENV PATH="${PATH}:/root/.dotnet/tools"

# Copy application source files
COPY . .

# Downloads and installs packages dependencies
RUN dotnet restore CrateApi/CrateApi.csproj

# Compile App
WORKDIR /src/CrateApi
RUN dotnet publish -c Release -o /app/publish

# Build and publish the migration bundle
RUN dotnet ef migrations bundle -o /app/publish/efbundle

# Multi-stage build for minimal size and dependencies
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app
COPY --from=build /app/publish .

ENTRYPOINT ["dotnet", "CrateApi.dll"]

================
File: CrateApi/Program.cs
================
using CrateApi;
using CrateApi.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

Di.Configure(builder);

var app = builder.Build();

var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("CrateApi is starting up...");
logger.LogInformation("Environment: {Environment}", app.Environment.EnvironmentName);

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    logger.LogInformation("Running in Development mode");
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseRouting();
app.UseMiddleware<ApiKeyMiddleware>();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

logger.LogInformation("CrateApi is ready to accept requests");
await app.RunAsync();

================
File: CrateApi.Common/Attributes/UrlValidator.cs
================
using System.ComponentModel.DataAnnotations;

namespace CrateApi.Common.Attributes;

public class CustomUrlAttribute : ValidationAttribute
{
    public override bool IsValid(object? value)
    {
        if (value is null || value is not string)
        {
            return false;
        }
        var result = Uri.TryCreate(value.ToString(), UriKind.Absolute, out Uri? uri);

        if (!result || uri is null)
        {
            return false;
        }

        return true;
    }
}

================
File: CrateApi.Common/Dto/Request/TrackRequestDto.cs
================
namespace CrateApi.Common.Dto.Request;

public record TrackRequestDto(
    int? Id = null,
    string? TrackTitle = null,
    string? ArtistName = null,
    string? MediaUrl = null,
    string? DomainUrl = null
);

================
File: CrateApi.Common/Dto/Request/UnfurlUrlRequestDto.cs
================
using System.ComponentModel.DataAnnotations;
using CrateApi.Common.Attributes;

namespace CrateApi.Common.Dto.Request;

public enum UrlType
{
    Spotify,
    AppleMusic,
    Bandcamp,
    Pandora,
    YouTubeMusic,
    Unsupported,
}

public record UnfurlUrlRequestDto([Required] [CustomUrl] string Url = "")
{
    public UrlType GetUrlType()
    {
        var uri = new Uri(Url);
        var hostname = uri.Host.ToLower();
        return hostname switch
        {
            "spotify.com" => UrlType.Spotify,
            "open.spotify.com" => UrlType.Spotify,
            "music.apple.com" => UrlType.AppleMusic,
            "bandcamp.com" => UrlType.Bandcamp,
            "pandora.com" => UrlType.Pandora,
            "music.youtube.com" => UrlType.YouTubeMusic,
            "youtube.com" when uri.AbsolutePath.Contains("/music/") => UrlType.YouTubeMusic,
            _ => UrlType.Unsupported,
        };
    }
}

================
File: CrateApi.Common/Dto/Response/ErrorResponseDto.cs
================
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CrateApi.Common.Dto.Response;

public record ErrorResponseDto(string Message);

================
File: CrateApi.Common/Dto/Response/TrendingTrackResponseDto.cs
================
namespace CrateApi.Common.Dto.Response;

public record TrendingTrackResponseDto(
    int Id,
    string TrackTitle,
    string ArtistName,
    string Url,
    string MediaUrl,
    string DomainUrl,
    DateTime? Created,
    DateTime? Updated
);

================
File: CrateApi.Common/Dto/Response/UnfurlTrackResponseDto.cs
================
using CrateApi.Data.Models;

namespace CrateApi.Common.Dto.Response;

public record AlbumMetadata(string Title, string Artist, string MediaUrl);

public record UnfurledTrackResponseDto(int? Id, string Artist, string Title, string MediaUrl, string Url)
{
    public static UnfurledTrackResponseDto FromTrack(Track track)
    {
        return new UnfurledTrackResponseDto(
            track.Id,
            track.ArtistName ?? "",
            track.TrackTitle ?? "",
            track.MediaUrl ?? "",
            track.Url ?? ""
        );
    }
}

================
File: CrateApi.Common/Exceptions/ExceptionResultHandler.cs
================
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace CrateApi.Common.Exceptions;

public static class ExceptionResult
{
    private static IActionResult UnprocessableEntity(string message, Exception ex, ILogger logger)
    {
        logger.LogWarning(ex, "Returning unprocessable entity: {Message}", message);

        return new UnprocessableEntityObjectResult(new ErrorResponseDto(message));
    }
    private static IActionResult RequestTimeout(TaskCanceledException ex, ILogger logger)
    {
        logger.LogDebug(ex, "Request timed out or was cancelled");

        return new StatusCodeResult(StatusCodes.Status408RequestTimeout);
    }
    private static IActionResult InternalServerError(Exception ex, ILogger logger)
    {
        logger.LogError(ex, "Returning Internal Server error");

        return new StatusCodeResult(500);
    }

    public static Func<Exception, IActionResult> Handle(ILogger logger) =>
        e => e switch
        {
            ScraperException ex => UnprocessableEntity($"An error occured while processing: {ex.Message}", ex, logger),
            TaskCanceledException ex => RequestTimeout(ex, logger),
            _ => InternalServerError(e, logger)
        };

}

================
File: CrateApi.Common/Exceptions/ScraperException.cs
================
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CrateApi.Common.Exceptions;

public class ScraperException : Exception
{
    public ScraperException(string message)
      : base(message)
    {
    }
    public ScraperException(string message, Exception innerException)
      : base(message, innerException)
    {
    }
}

================
File: CrateApi.Common/Unfurl/AppleTrack.cs
================
namespace CrateApi.Common;

public record AppleTrack(string Title, string Artist, string MediaUrl) : ITrack;

================
File: CrateApi.Common/Unfurl/GenericTrack.cs
================
namespace CrateApi.Common;

public record GenericTrack(string Title, string Artist, string MediaUrl) : ITrack;

================
File: CrateApi.Common/Unfurl/ITrack.cs
================
namespace CrateApi.Common;

public interface IIsrc
{
    // Isrc Number
    public string Isrc { get; }
}

public interface IDuration
{
    // Duration in ms
    public int Duration { get; }
}

public interface ITrack
{
    public string Title { get; }
    public string Artist { get; }
    public string MediaUrl { get; }
}

================
File: CrateApi.Common/Unfurl/SpotifyTrack.cs
================
using Newtonsoft.Json;

namespace CrateApi.Common;

public record SpotifyTrack : ITrack, IIsrc, IDuration
{
    [JsonProperty("id")]
    public string Id { get; init; } = default!;

    [JsonProperty("name")]
    public string Name { get; init; } = default!;

    [JsonProperty("type")]
    public string Type { get; init; } = default!;

    [JsonProperty("uri")]
    public string Uri { get; init; } = default!;

    [JsonProperty("href")]
    public string Href { get; init; } = default!;

    [JsonProperty("duration_ms")]
    public int DurationMs { get; init; }

    [JsonProperty("explicit")]
    public bool Explicit { get; init; }

    [JsonProperty("popularity")]
    public int Popularity { get; init; }

    [JsonProperty("preview_url")]
    public string? PreviewUrl { get; init; }

    [JsonProperty("track_number")]
    public int TrackNumber { get; init; }

    [JsonProperty("disc_number")]
    public int DiscNumber { get; init; }

    [JsonProperty("external_urls")]
    public ExternalUrls ExternalUrls { get; init; } = default!;

    [JsonProperty("external_ids")]
    public ExternalIds ExternalIds { get; init; } = default!;

    [JsonProperty("album")]
    public Album Album { get; init; } = default!;

    [JsonProperty("artists")]
    public IReadOnlyList<Artist> Artists { get; init; } = default!;

    [JsonProperty("available_markets")]
    public IReadOnlyList<string> AvailableMarkets { get; init; } = default!;

    [JsonProperty("is_local")]
    public bool IsLocal { get; init; }

    public string Title => Name;

    public string Artist => Artists.First().Name;

    public string MediaUrl => Album.Images.First().Url;

    public string Isrc => ExternalIds.Isrc;

    public int Duration => DurationMs;
}

public record Album
{
    [JsonProperty("id")]
    public string Id { get; init; } = default!;

    [JsonProperty("name")]
    public string Name { get; init; } = default!;

    [JsonProperty("type")]
    public string Type { get; init; } = default!;

    [JsonProperty("uri")]
    public string Uri { get; init; } = default!;

    [JsonProperty("href")]
    public string Href { get; init; } = default!;

    [JsonProperty("album_type")]
    public string AlbumType { get; init; } = default!;

    [JsonProperty("total_tracks")]
    public int TotalTracks { get; init; }

    [JsonProperty("release_date")]
    public string ReleaseDate { get; init; } = default!;

    [JsonProperty("release_date_precision")]
    public string ReleaseDatePrecision { get; init; } = default!;

    [JsonProperty("images")]
    public IReadOnlyList<Image> Images { get; init; } = default!;

    [JsonProperty("external_urls")]
    public ExternalUrls ExternalUrls { get; init; } = default!;

    [JsonProperty("artists")]
    public IReadOnlyList<Artist> Artists { get; init; } = default!;

    [JsonProperty("available_markets")]
    public IReadOnlyList<string> AvailableMarkets { get; init; } = default!;
}

public record Artist
{
    [JsonProperty("id")]
    public string Id { get; init; } = default!;

    [JsonProperty("name")]
    public string Name { get; init; } = default!;

    [JsonProperty("type")]
    public string Type { get; init; } = default!;

    [JsonProperty("uri")]
    public string Uri { get; init; } = default!;

    [JsonProperty("href")]
    public string Href { get; init; } = default!;

    [JsonProperty("external_urls")]
    public ExternalUrls ExternalUrls { get; init; } = default!;
}

public record Image
{
    [JsonProperty("url")]
    public string Url { get; init; } = default!;

    [JsonProperty("width")]
    public int Width { get; init; }

    [JsonProperty("height")]
    public int Height { get; init; }
}

public record ExternalUrls
{
    [JsonProperty("spotify")]
    public string Spotify { get; init; } = default!;
}

public record ExternalIds
{
    [JsonProperty("isrc")]
    public string Isrc { get; init; } = default!;
}

================
File: CrateApi.Common/Unfurl/YouTubeMusicTrack.cs
================
using Newtonsoft.Json;

namespace CrateApi.Common;

public record YouTubeMusicTrack : ITrack
{
    [JsonProperty("videoId")]
    public string VideoId { get; init; } = default!;
    
    [JsonProperty("title")]
    public string Title { get; init; } = default!;
    
    [JsonProperty("artist")]
    public string Artist { get; init; } = default!;
    
    [JsonProperty("album")]
    public string Album { get; init; } = default!;
    
    [JsonProperty("duration_ms")]
    public int? DurationMs { get; init; }
    
    [JsonProperty("isrc")]
    public string? Isrc { get; init; }
    
    [JsonProperty("thumbnailUrl")]
    public string ThumbnailUrl { get; init; } = default!;
    
    [JsonProperty("url")]
    public string Url { get; init; } = default!;
    
    public string MediaUrl => ThumbnailUrl;
}

================
File: CrateApi.Common/CrateApi.Common.csproj
================
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LineEndings>LF</LineEndings>
    <WarningsAsErrors>true</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
      <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
      <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
      <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="8.0.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="System.Text.Json" Version="8.0.5" />
    </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CrateApi.Data\CrateApi.Data.csproj" />
  </ItemGroup>
</Project>

================
File: CrateApi.Common/ServiceSettings.cs
================
namespace CrateApi.Common;

public record ServiceSettings(string UnfurlServiceUrl);

================
File: CrateApi.Data/Migrations/20250125135804_new.cs
================
using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class @new : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Collections",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 256, nullable: false),
                    Thumbnail = table.Column<string>(type: "TEXT", nullable: true),
                    Created = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Updated = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Collections", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TrendingTrack",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    TrackTitle = table.Column<string>(type: "TEXT", maxLength: 256, nullable: false),
                    ArtistName = table.Column<string>(type: "TEXT", maxLength: 256, nullable: false),
                    Url = table.Column<string>(type: "TEXT", nullable: true),
                    MediaUrl = table.Column<string>(type: "TEXT", nullable: true),
                    DomainUrl = table.Column<string>(type: "TEXT", nullable: true),
                    CollectionId = table.Column<int>(type: "INTEGER", nullable: true),
                    Created = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Updated = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TrendingTrack", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TrendingTrack_Collections_CollectionId",
                        column: x => x.CollectionId,
                        principalTable: "Collections",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_TrendingTrack_CollectionId",
                table: "TrendingTrack",
                column: "CollectionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TrendingTrack");

            migrationBuilder.DropTable(
                name: "Collections");
        }
    }
}

================
File: CrateApi.Data/Migrations/20250125135804_new.Designer.cs
================
// <auto-generated />
using System;
using CrateApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CrateApi.Data.Migrations
{
    [DbContext(typeof(CrateDbContext))]
    [Migration("20250125135804_new")]
    partial class @new
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("Thumbnail")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Collections");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ArtistName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CollectionId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("DomainUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("MediaUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("TrackTitle")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.ToTable("TrendingTrack");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Collection", null)
                        .WithMany("Tracks")
                        .HasForeignKey("CollectionId");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Navigation("Tracks");
                });
#pragma warning restore 612, 618
        }
    }
}

================
File: CrateApi.Data/Migrations/20250125152127_new2.cs
================
using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class new2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Username = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Email = table.Column<string>(type: "TEXT", nullable: false),
                    Password = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Users_Email",
                table: "Users",
                column: "Email",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Users");
        }
    }
}

================
File: CrateApi.Data/Migrations/20250125152127_new2.Designer.cs
================
// <auto-generated />
using System;
using CrateApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CrateApi.Data.Migrations
{
    [DbContext(typeof(CrateDbContext))]
    [Migration("20250125152127_new2")]
    partial class new2
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("Thumbnail")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Collections");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ArtistName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CollectionId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("DomainUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("MediaUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("TrackTitle")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.ToTable("TrendingTrack");
                });

            modelBuilder.Entity("CrateApi.Data.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Collection", null)
                        .WithMany("Tracks")
                        .HasForeignKey("CollectionId");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Navigation("Tracks");
                });
#pragma warning restore 612, 618
        }
    }
}

================
File: CrateApi.Data/Migrations/20250125181807_new3.cs
================
using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class new3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "UserId",
                table: "TrendingTrack",
                type: "TEXT",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<Guid>(
                name: "UserId",
                table: "Collections",
                type: "TEXT",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_TrendingTrack_UserId",
                table: "TrendingTrack",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Collections_UserId",
                table: "Collections",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_Collections_Users_UserId",
                table: "Collections",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TrendingTrack_Users_UserId",
                table: "TrendingTrack",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Collections_Users_UserId",
                table: "Collections");

            migrationBuilder.DropForeignKey(
                name: "FK_TrendingTrack_Users_UserId",
                table: "TrendingTrack");

            migrationBuilder.DropIndex(
                name: "IX_TrendingTrack_UserId",
                table: "TrendingTrack");

            migrationBuilder.DropIndex(
                name: "IX_Collections_UserId",
                table: "Collections");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "TrendingTrack");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "Collections");
        }
    }
}

================
File: CrateApi.Data/Migrations/20250125181807_new3.Designer.cs
================
// <auto-generated />
using System;
using CrateApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CrateApi.Data.Migrations
{
    [DbContext(typeof(CrateDbContext))]
    [Migration("20250125181807_new3")]
    partial class new3
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("Thumbnail")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Collections");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ArtistName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CollectionId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("DomainUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("MediaUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("TrackTitle")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("UserId");

                    b.ToTable("TrendingTrack");
                });

            modelBuilder.Entity("CrateApi.Data.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Collection", null)
                        .WithMany("Tracks")
                        .HasForeignKey("CollectionId");

                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Navigation("Tracks");
                });
#pragma warning restore 612, 618
        }
    }
}

================
File: CrateApi.Data/Migrations/20250126015948_new5.cs
================
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class new5 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TrendingTrack_Collections_CollectionId",
                table: "TrendingTrack");

            migrationBuilder.DropForeignKey(
                name: "FK_TrendingTrack_Users_UserId",
                table: "TrendingTrack");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TrendingTrack",
                table: "TrendingTrack");

            migrationBuilder.RenameTable(
                name: "TrendingTrack",
                newName: "Tracks");

            migrationBuilder.RenameIndex(
                name: "IX_TrendingTrack_UserId",
                table: "Tracks",
                newName: "IX_Tracks_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_TrendingTrack_CollectionId",
                table: "Tracks",
                newName: "IX_Tracks_CollectionId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Tracks",
                table: "Tracks",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Tracks_Collections_CollectionId",
                table: "Tracks",
                column: "CollectionId",
                principalTable: "Collections",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Tracks_Users_UserId",
                table: "Tracks",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Tracks_Collections_CollectionId",
                table: "Tracks");

            migrationBuilder.DropForeignKey(
                name: "FK_Tracks_Users_UserId",
                table: "Tracks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Tracks",
                table: "Tracks");

            migrationBuilder.RenameTable(
                name: "Tracks",
                newName: "TrendingTrack");

            migrationBuilder.RenameIndex(
                name: "IX_Tracks_UserId",
                table: "TrendingTrack",
                newName: "IX_TrendingTrack_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_Tracks_CollectionId",
                table: "TrendingTrack",
                newName: "IX_TrendingTrack_CollectionId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TrendingTrack",
                table: "TrendingTrack",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TrendingTrack_Collections_CollectionId",
                table: "TrendingTrack",
                column: "CollectionId",
                principalTable: "Collections",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TrendingTrack_Users_UserId",
                table: "TrendingTrack",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}

================
File: CrateApi.Data/Migrations/20250126015948_new5.Designer.cs
================
// <auto-generated />
using System;
using CrateApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CrateApi.Data.Migrations
{
    [DbContext(typeof(CrateDbContext))]
    [Migration("20250126015948_new5")]
    partial class new5
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("Thumbnail")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Collections");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ArtistName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CollectionId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("DomainUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("MediaUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("TrackTitle")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("UserId");

                    b.ToTable("Tracks");
                });

            modelBuilder.Entity("CrateApi.Data.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Collection", null)
                        .WithMany("Tracks")
                        .HasForeignKey("CollectionId");

                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Navigation("Tracks");
                });
#pragma warning restore 612, 618
        }
    }
}

================
File: CrateApi.Data/Migrations/20250126131245_new6.cs
================
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class new6 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}

================
File: CrateApi.Data/Migrations/20250126131245_new6.Designer.cs
================
// <auto-generated />
using System;
using CrateApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CrateApi.Data.Migrations
{
    [DbContext(typeof(CrateDbContext))]
    [Migration("20250126131245_new6")]
    partial class new6
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("Thumbnail")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Collections");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ArtistName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CollectionId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("DomainUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("MediaUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("TrackTitle")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("UserId");

                    b.ToTable("Tracks");
                });

            modelBuilder.Entity("CrateApi.Data.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Collection", null)
                        .WithMany("Tracks")
                        .HasForeignKey("CollectionId");

                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Navigation("Tracks");
                });
#pragma warning restore 612, 618
        }
    }
}

================
File: CrateApi.Data/Migrations/20250205002728_mig01.cs
================
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class mig01 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Duration",
                table: "Tracks",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Isrc",
                table: "Tracks",
                type: "TEXT",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Duration",
                table: "Tracks");

            migrationBuilder.DropColumn(
                name: "Isrc",
                table: "Tracks");
        }
    }
}

================
File: CrateApi.Data/Migrations/20250205002728_mig01.Designer.cs
================
// <auto-generated />
using System;
using CrateApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CrateApi.Data.Migrations
{
    [DbContext(typeof(CrateDbContext))]
    [Migration("20250205002728_mig01")]
    partial class mig01
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("Thumbnail")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Collections");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ArtistName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CollectionId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("DomainUrl")
                        .HasColumnType("TEXT");

                    b.Property<int?>("Duration")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Isrc")
                        .HasColumnType("TEXT");

                    b.Property<string>("MediaUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("TrackTitle")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("UserId");

                    b.ToTable("Tracks");
                });

            modelBuilder.Entity("CrateApi.Data.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Collection", null)
                        .WithMany("Tracks")
                        .HasForeignKey("CollectionId");

                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Navigation("Tracks");
                });
#pragma warning restore 612, 618
        }
    }
}

================
File: CrateApi.Data/Migrations/20250209185541_AddChallenge.cs
================
using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddChallenge : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "NfcCards",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Uid = table.Column<string>(type: "TEXT", maxLength: 32, nullable: false),
                    Atr = table.Column<string>(type: "TEXT", maxLength: 64, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    LastUsed = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NfcCards", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Challenges",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ChallengeValue = table.Column<string>(type: "TEXT", maxLength: 64, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsUsed = table.Column<bool>(type: "INTEGER", nullable: false),
                    NfcCardId = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Challenges", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Challenges_NfcCards_NfcCardId",
                        column: x => x.NfcCardId,
                        principalTable: "NfcCards",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Challenges_NfcCardId",
                table: "Challenges",
                column: "NfcCardId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Challenges");

            migrationBuilder.DropTable(
                name: "NfcCards");
        }
    }
}

================
File: CrateApi.Data/Migrations/20250209185541_AddChallenge.Designer.cs
================
// <auto-generated />
using System;
using CrateApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CrateApi.Data.Migrations
{
    [DbContext(typeof(CrateDbContext))]
    [Migration("20250209185541_AddChallenge")]
    partial class AddChallenge
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            modelBuilder.Entity("CrateApi.Data.Models.Challenge", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ChallengeValue")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("INTEGER");

                    b.Property<int>("NfcCardId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("NfcCardId");

                    b.ToTable("Challenges");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("Thumbnail")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Collections");
                });

            modelBuilder.Entity("CrateApi.Data.Models.NfcCard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Atr")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastUsed")
                        .HasColumnType("TEXT");

                    b.Property<string>("Uid")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("NfcCards");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ArtistName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CollectionId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("DomainUrl")
                        .HasColumnType("TEXT");

                    b.Property<int?>("Duration")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Isrc")
                        .HasColumnType("TEXT");

                    b.Property<string>("MediaUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("TrackTitle")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("UserId");

                    b.ToTable("Tracks");
                });

            modelBuilder.Entity("CrateApi.Data.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Challenge", b =>
                {
                    b.HasOne("CrateApi.Data.Models.NfcCard", "NfcCard")
                        .WithMany("Challenges")
                        .HasForeignKey("NfcCardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NfcCard");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Collection", null)
                        .WithMany("Tracks")
                        .HasForeignKey("CollectionId");

                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Navigation("Tracks");
                });

            modelBuilder.Entity("CrateApi.Data.Models.NfcCard", b =>
                {
                    b.Navigation("Challenges");
                });
#pragma warning restore 612, 618
        }
    }
}

================
File: CrateApi.Data/Migrations/CrateDbContextModelSnapshot.cs
================
// <auto-generated />
using System;
using CrateApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CrateApi.Data.Migrations
{
    [DbContext(typeof(CrateDbContext))]
    partial class CrateDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            modelBuilder.Entity("CrateApi.Data.Models.Challenge", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ChallengeValue")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("INTEGER");

                    b.Property<int>("NfcCardId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("NfcCardId");

                    b.ToTable("Challenges");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("Thumbnail")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Collections");
                });

            modelBuilder.Entity("CrateApi.Data.Models.NfcCard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Atr")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastUsed")
                        .HasColumnType("TEXT");

                    b.Property<string>("Uid")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("NfcCards");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.Property<int?>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ArtistName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CollectionId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("DomainUrl")
                        .HasColumnType("TEXT");

                    b.Property<int?>("Duration")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Isrc")
                        .HasColumnType("TEXT");

                    b.Property<string>("MediaUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("TrackTitle")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("UserId");

                    b.ToTable("Tracks");
                });

            modelBuilder.Entity("CrateApi.Data.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Challenge", b =>
                {
                    b.HasOne("CrateApi.Data.Models.NfcCard", "NfcCard")
                        .WithMany("Challenges")
                        .HasForeignKey("NfcCardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NfcCard");
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Track", b =>
                {
                    b.HasOne("CrateApi.Data.Models.Collection", null)
                        .WithMany("Tracks")
                        .HasForeignKey("CollectionId");

                    b.HasOne("CrateApi.Data.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CrateApi.Data.Models.Collection", b =>
                {
                    b.Navigation("Tracks");
                });

            modelBuilder.Entity("CrateApi.Data.Models.NfcCard", b =>
                {
                    b.Navigation("Challenges");
                });
#pragma warning restore 612, 618
        }
    }
}

================
File: CrateApi.Data/Models/Challenge.cs
================
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CrateApi.Data.Models;

public class Challenge
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    [Required]
    [MaxLength(64)]
    public required string ChallengeValue { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [DefaultValue(false)]
    public bool IsUsed { get; set; } = false;

    public int NfcCardId { get; set; }

    [ForeignKey("NfcCardId")]
    public virtual NfcCard? NfcCard { get; set; }
}

================
File: CrateApi.Data/Models/Collection.cs
================
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CrateApi.Data.Models;

public class Collection
{
    [Required]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int? Id { get; set; }

    [Required]
    [StringLength(256)]
    public string? Name { get; set; } = "";

    [Url]
    public string? Thumbnail { get; set; } = "";
    public List<Track> Tracks { get; set; } = new List<Track>();

    [Required]
    public DateTime Created { get; set; }

    [Required]
    public DateTime Updated { get; set; }

    [Required]
    public Guid UserId { get; set; }
}

================
File: CrateApi.Data/Models/NfcCard.cs
================
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CrateApi.Data.Models;

public class NfcCard
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    [Required]
    [MaxLength(32)]
    public required string Uid { get; set; }

    [Required]
    [MaxLength(64)]
    public required string Atr { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? LastUsed { get; set; }

    [DefaultValue(true)]
    public bool IsActive { get; set; } = true;

    public virtual ICollection<Challenge> Challenges { get; set; } = new List<Challenge>();
}

================
File: CrateApi.Data/Models/Track.cs
================
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CrateApi.Data.Models;

public class Track
{
    [Required]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int? Id { get; set; }

    [Required]
    [StringLength(256)]
    public string? TrackTitle { get; set; } = "";

    [Required]
    [StringLength(256)]
    public string? ArtistName { get; set; } = "";

    [Url]
    [DisplayName("Url")]
    public string? Url { get; set; } = "";

    [Url]
    [DisplayName("Media URL")]
    public string? MediaUrl { get; set; } = "";

    [Url]
    [DisplayName("Domain URL")]
    public string? DomainUrl { get; set; } = "";

    [DisplayName("Collection Id")]
    public int? CollectionId { get; set; }

    [DisplayName("Duration")]
    public int? Duration { get; set; } = null;

    [DisplayName("ISRC")]
    public string? Isrc { get; set; } = null;

    [Required]
    [DisplayName("User Id")]
    public Guid UserId { get; set; }

    [Required]
    [DisplayName("Created")]
    public DateTime Created { get; set; }

    [Required]
    [DisplayName("Updated")]
    public DateTime Updated { get; set; }
}

================
File: CrateApi.Data/Models/User.cs
================
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CrateApi.Data.Models;

public class User
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid Id { get; set; }

    [Required]
    [StringLength(50)]
    public string Username { get; set; } = "";

    [Required]
    [EmailAddress]
    public string Email { get; set; } = "";

    [Required]
    public string Password { get; set; } = "";
}

================
File: CrateApi.Data/CrateApi.Data.csproj
================
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.13">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.13">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.13" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

</Project>

================
File: CrateApi.Data/CrateDbContext.cs
================
using CrateApi.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace CrateApi.Data;

public class CrateDbContext : DbContext
{
    public CrateDbContext(DbContextOptions<CrateDbContext> options)
        : base(options) { }

    public DbSet<Track> Tracks { get; set; }

    public DbSet<Collection> Collections { get; set; }

    public DbSet<User> Users { get; set; }

    public DbSet<NfcCard> NfcCards { get; set; }

    public DbSet<Challenge> Challenges { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<Track>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TrackTitle).IsRequired();
            entity.Property(e => e.ArtistName).IsRequired();
            entity.Property(e => e.Updated).IsRequired();
            entity.Property(e => e.Created).IsRequired();
            entity.HasIndex(e => e.UserId);
            entity.HasOne<Collection>().WithMany(c => c.Tracks).HasForeignKey(t => t.CollectionId);
            entity.HasOne<User>().WithMany().HasForeignKey(t => t.UserId).OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<Collection>(entity =>
        {
            entity.Property(e => e.UserId).IsRequired();
            entity.HasIndex(e => e.UserId);
            entity.HasOne<User>().WithMany().HasForeignKey(c => c.UserId).OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<User>().HasIndex(u => u.Email).IsUnique();
    }
}

================
File: CrateApi.Services/Abstractions/IClient.cs
================
namespace CrateApi.Services.Abstractions;

public interface IClient
{
    Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default);
    Task<T?> PostAsync<T>(string endpoint, object? data = null, CancellationToken cancellationToken = default);
    Task<T?> PutAsync<T>(string endpoint, object? data = null, CancellationToken cancellationToken = default);
}

================
File: CrateApi.Services/Database/CollectionService.cs
================
using CrateApi.Data;
using CrateApi.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace CrateApi.Services.Database;

public interface ICollectionService
{
    Task<IEnumerable<Collection>> GetPaged(int start, int size);
    Task<Collection> AddAsync(Collection Collection);
    Task DeleteAsync(int id);
    Task<Collection> AddTracksToCollection(int collectionId, List<int> trackIds);
}

public class CollectionService : DatabaseService<Collection>, ICollectionService
{
    private readonly CrateDbContext context;
    private readonly IUserManager userManager;

    public CollectionService(CrateDbContext context, IUserManager userManager)
        : base(context)
    {
        this.context = context;
        this.userManager = userManager;
    }

    public async Task<Collection> AddTracksToCollection(int collectionId, List<int> trackIds)
    {
        var collection =
            await context
                .Collections.Include(c => c.Tracks)
                .FirstOrDefaultAsync(c => c.Id == collectionId && c.UserId == userManager.CurrentUser.UserId)
            ?? throw new KeyNotFoundException($"Collection {collectionId} not found");

        var tracks = await context.Tracks.Where(t => trackIds.Contains(t.Id!.Value)).ToListAsync();
        if (tracks.Count != trackIds.Count)
            throw new KeyNotFoundException("Some tracks were not found");
        collection.Tracks.AddRange(tracks);
        collection.Updated = DateTime.UtcNow;
        await context.SaveChangesAsync();
        return collection!;
    }

    public async Task<IEnumerable<Collection>> GetPaged(int start, int size)
    {
        return await context
            .Collections.Include(e => e.Tracks)
            .Where(e => e.UserId == userManager.CurrentUser.UserId)
            .OrderBy(e => e.Updated)
            .Skip(start)
            .Take(size)
            .ToListAsync();
    }

    public override async Task<Collection> AddAsync(Collection collection)
    {
        collection.UserId = userManager.CurrentUser.UserId;
        return await base.AddAsync(collection);
    }

    public async Task DeleteAsync(int id)
    {
        var collection = await context.Collections.FirstOrDefaultAsync(c => c.Id == id && c.UserId == userManager.CurrentUser.UserId);
        if (collection is not null)
        {
            await base.RemoveAsync(collection);
        }
    }
}

================
File: CrateApi.Services/Database/DatabaseService.cs
================
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;

namespace CrateApi.Services.Database
{
    public interface IDatabaseService<TEntity>
        where TEntity : class
    {
        Task<TEntity?> GetByIdAsync(object id);
        Task<IEnumerable<TEntity>> GetAllAsync();
        Task<IEnumerable<TEntity>> FindAsync(Expression<Func<TEntity, bool>> predicate);
        Task<TEntity> AddAsync(TEntity entity);
        Task<IEnumerable<TEntity>> AddRangeAsync(IEnumerable<TEntity> entities);
        Task UpdateAsync(TEntity entity);
        Task UpdateRangeAsync(IEnumerable<TEntity> entities);
        Task RemoveAsync(TEntity entity);
        Task RemoveAllAsync();
        Task RemoveRangeAsync(IEnumerable<TEntity> entities);
    }

    public abstract class DatabaseService<TEntity> : IDatabaseService<TEntity>
        where TEntity : class
    {
        protected DbContext _context { get; }
        protected DbSet<TEntity> _dbSet { get; }

        protected DatabaseService(DbContext context)
        {
            _context = context;
            _dbSet = context.Set<TEntity>();
        }

        public virtual async Task<TEntity?> GetByIdAsync(object id)
        {
            return await _dbSet.FindAsync(id);
        }

        public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
        {
            return await _dbSet.ToListAsync();
        }

        public virtual async Task<IEnumerable<TEntity>> FindAsync(Expression<Func<TEntity, bool>> predicate)
        {
            return await _dbSet.Where(predicate).ToListAsync();
        }

        public virtual async Task<TEntity> AddAsync(TEntity entity)
        {
            var entry = await _dbSet.AddAsync(entity);
            await _context.SaveChangesAsync();
            return entry.Entity;
        }

        public virtual async Task<IEnumerable<TEntity>> AddRangeAsync(IEnumerable<TEntity> entities)
        {
            await _dbSet.AddRangeAsync(entities);
            await _context.SaveChangesAsync();
            return entities;
        }

        public virtual async Task UpdateAsync(TEntity entity)
        {
            _dbSet.Update(entity);
            await _context.SaveChangesAsync();
        }

        public virtual async Task UpdateRangeAsync(IEnumerable<TEntity> entities)
        {
            _dbSet.UpdateRange(entities);
            await _context.SaveChangesAsync();
        }

        public virtual async Task RemoveAllAsync()
        {
            await _dbSet.ExecuteDeleteAsync();
        }

        public virtual async Task RemoveAsync(TEntity entity)
        {
            _dbSet.Remove(entity);
            await _context.SaveChangesAsync();
        }

        public virtual async Task RemoveRangeAsync(IEnumerable<TEntity> entities)
        {
            _dbSet.RemoveRange(entities);
            await _context.SaveChangesAsync();
        }
    }
}

================
File: CrateApi.Services/Database/TrackService.cs
================
using CrateApi.Common.Dto.Response;
using CrateApi.Data;
using CrateApi.Data.Models;
using CrateApi.Services.Mappings;
using Microsoft.EntityFrameworkCore;

namespace CrateApi.Services.Database;

public interface ITrackService
{
    Task<List<TrendingTrackResponseDto>> GetTrendingAsync(int start, int size);
    Task<Track> AddAsync(Track track);
    Task DeleteAsync(int id);
    Task DeleteAllAsync();
}

public class TrackService : DatabaseService<Track>, ITrackService
{
    private readonly CrateDbContext context;
    private readonly IUserManager userManager;

    public TrackService(CrateDbContext context, IUserManager userManager)
        : base(context)
    {
        this.context = context;
        this.userManager = userManager;
    }

    public async Task<List<TrendingTrackResponseDto>> GetTrendingAsync(int start, int size)
    {
        var tracks = await context
            .Tracks.GroupBy(t => new { t.ArtistName, t.TrackTitle })
            .Select(g => g.OrderByDescending(t => t.Created).First())
            .Skip(start)
            .Take(size)
            .ToListAsync();

        var mapper = new TrendingTrackMapper();
        return mapper.FromEntity(tracks);
    }

    public override async Task<Track> AddAsync(Track track)
    {
        track.UserId = userManager.CurrentUser.UserId;
        return await base.AddAsync(track);
    }

    public async Task DeleteAllAsync()
    {
        var tracks = context.Tracks.Where(e => e.UserId == userManager.CurrentUser.UserId);
        context.RemoveRange(tracks);
        await context.SaveChangesAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var tracks = context.Tracks.Where(e => e.Id == id && e.UserId == userManager.CurrentUser.UserId);
        context.RemoveRange(tracks);
        await context.SaveChangesAsync();
    }
}

================
File: CrateApi.Services/Logic/DubCoClient.cs
================
using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace CrateApi.Services.Logic;



public class DubCoClient
{
    private readonly HttpClient _client;

    public DubCoClient()
    {
        _client = new HttpClient();
        _client.DefaultRequestHeaders.Add("Authorization", $"Bearer dub_x54AV6qJEdlYNkLdXlPxAGb6");
    }

    public async Task<string> CreateLinkAsync(string url, string domain = "dub.sh", string slug = null)
    {
        var endpoint = "https://api.dub.co/links";

        // Create request object
        var requestData = new
        {
            url = url,
            domain = domain,
            slug = slug
        };

        // Remove null properties
        var jsonOptions = new JsonSerializerOptions
        {
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };

        var content = new StringContent(
          JsonSerializer.Serialize(requestData, jsonOptions),
          Encoding.UTF8,
          "application/json");

        var response = await _client.PostAsync(endpoint, content);
        var responseBody = await response.Content.ReadAsStringAsync();

        response.EnsureSuccessStatusCode();

        return responseBody;
    }

    private class ErrorResponse
    {
        public string Error { get; set; }
    }
}

// Example usage:
/*
static async Task Main(string[] args)
{
  try
  {
    var dubClient = new DubCoClient("your_api_key");
    var result = await dubClient.CreateLinkAsync(
      "https://example.com", 
      "dub.sh", 
      "my-custom-slug");

    Console.WriteLine($"Created link: {result}");
  }
  catch (Exception ex)
  {
    Console.WriteLine($"Error: {ex.Message}");
  }
}
*/

================
File: CrateApi.Services/Logic/UnfurlService.cs
================
using System.Net;
using System.Net.Http.Json;
using CrateApi.Common;
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Exceptions;
using CrateApi.Data.Models;
using CrateApi.Services.Database;
using CrateApi.Services.Logic;
using LanguageExt;
using LanguageExt.UnsafeValueAccess;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using static LanguageExt.Prelude;

namespace CrateApi.Services;

public interface IUnfurlService
{
    Task<Option<UnfurledTrackResponseDto>> UnfurlTrackUrl(UnfurlUrlRequestDto request);
    Task<Option<UnfurledTrackResponseDto>> UnfurlTrackUrlAnonymous(UnfurlUrlRequestDto request);
}

public class UnfurlService(
    DubCoClient dubCoClient,
    IHttpClientFactory httpClientFactory,
    ITrackService trackService,
    ServiceSettings serviceSettings,
    ILogger<UnfurlService> logger,
    IConfiguration config
) : IUnfurlService
{
    public Task<Option<UnfurledTrackResponseDto>> UnfurlTrackUrl(UnfurlUrlRequestDto request) =>
        GetUnfurledTrack(request)
            .Bind(SaveTrackToDatabase)
            .Bind(CreateDubCoLink);

    public Task<Option<UnfurledTrackResponseDto>> UnfurlTrackUrlAnonymous(UnfurlUrlRequestDto request) =>
        GetUnfurledTrack(request)
            .Map(val => val.track.Map(track =>
                new UnfurledTrackResponseDto(null, track.Artist, track.Title, track.MediaUrl, val.url)
            ));

    private async Task<(Option<ITrack> track, string url)> GetUnfurledTrack(UnfurlUrlRequestDto request)
    {
        var client = httpClientFactory.CreateClient();
        var platformType = request.GetUrlType();
        var endpoint = GetEndpointFromUrlType(platformType);
        var url = $"{serviceSettings.UnfurlServiceUrl}/api/v1/unfurl/{endpoint}";
        var response = await client.PostAsJsonAsync(url, request);

        if (!response.IsSuccessStatusCode)
        {
            logger.LogWarning("Failed to unfurl URL: {Url}, status code: {StatusCode}", request.Url, response.StatusCode);
            throw new Exception($"Failed to unfurl URL: {request.Url}");
        }

        if (response.StatusCode == HttpStatusCode.NoContent)
        {
            return (Option<ITrack>.None, request.Url);
        }

        ITrack? trackData = platformType switch
        {
            UrlType.Spotify => (await response.Content.ReadFromJsonAsync<SpotifyTrack>()),
            UrlType.AppleMusic => (await response.Content.ReadFromJsonAsync<AppleTrack>()),
            UrlType.YouTubeMusic => (await response.Content.ReadFromJsonAsync<YouTubeMusicTrack>()),
            UrlType.Unsupported => (await response.Content.ReadFromJsonAsync<GenericTrack>()),
            _ => (await response.Content.ReadFromJsonAsync<GenericTrack>()),
        };
        if (trackData is null)
        {
            logger.LogWarning("No track data returned from URL: {Url}", request.Url);
            throw new Exception($"No track data returned from URL: {request.Url}");
        }

        return (Some(trackData), request.Url);
    }
    private async Task<(Option<UnfurledTrackResponseDto> track, string url)> SaveTrackToDatabase((Option<ITrack> data, string url) val) =>
        await val.data.MatchAsync(
            async some =>
            {
                var track = new Track
                {
                    Url = val.url,
                    Duration = some is IDuration duration ? duration.Duration : null,
                    Isrc = some is IIsrc isrcCode ? isrcCode.Isrc : null,
                    ArtistName = some.Artist,
                    TrackTitle = some.Title,
                    MediaUrl = some.MediaUrl,
                    Created = DateTime.Now,
                    Updated = DateTime.Now,
                };
                var addedTrack = await trackService.AddAsync(track);
                var value = UnfurledTrackResponseDto.FromTrack(addedTrack);
                return (Some(value), val.url);
            },
            () =>  (Option<UnfurledTrackResponseDto>.None, val.url)
        );    
    

    private async Task<Option<UnfurledTrackResponseDto>> CreateDubCoLink((Option<UnfurledTrackResponseDto> track, string url) data)
    { 
        await data.track.IfSomeAsync(async e => {
            var val = parseBool(config["DubCoEnabled"]);
            if (!val.IsNone && val.Value())
            {
                var response = await dubCoClient.CreateLinkAsync(e.Url);
                logger.LogInformation($"[DubCo] LINK CREATED {response} ");
            }
        });
        return data.track;
    }

    private string GetEndpointFromUrlType(UrlType urlType) =>
        urlType switch
        {
            UrlType.Spotify => "spotify",
            UrlType.AppleMusic => "apple",
            UrlType.YouTubeMusic => "youtube",
            UrlType.Unsupported => "unsupported",
            _ => "unsupported",
        };
}

================
File: CrateApi.Services/Mappings/TrackMapper.cs
================
using CrateApi.Common.Dto.Request;
using CrateApi.Data.Models;
using Riok.Mapperly.Abstractions;

namespace CrateApi.Services.Mappings;

[Mapper]
public partial class TrackMapper
{
    [MapperIgnoreTarget(nameof(Track.Created))]
    [MapperIgnoreTarget(nameof(Track.Updated))]
    [MapperIgnoreTarget(nameof(Track.CollectionId))]
    [MapperIgnoreTarget(nameof(Track.UserId))]
    [MapperIgnoreTarget(nameof(Track.Url))]
    public partial Track ToEntity(TrackRequestDto track);

    [MapperIgnoreSource(nameof(Track.Created))]
    [MapperIgnoreSource(nameof(Track.Updated))]
    [MapperIgnoreSource(nameof(Track.CollectionId))]
    [MapperIgnoreSource(nameof(Track.UserId))]
    [MapperIgnoreSource(nameof(Track.Url))]
    public partial TrackRequestDto ToDto(Track track);

    public partial IEnumerable<Track> ToEntity(IEnumerable<TrackRequestDto> tracks);

    public partial IEnumerable<TrackRequestDto> ToDto(IEnumerable<Track> tracks);
}

================
File: CrateApi.Services/Mappings/TrendingTrackMapper.cs
================
using CrateApi.Common.Dto.Response;
using CrateApi.Data.Models;
using Riok.Mapperly.Abstractions;

namespace CrateApi.Services.Mappings;

[Mapper]
public partial class TrendingTrackMapper
{
    [MapperIgnoreSource(nameof(Track.CollectionId))]
    [MapperIgnoreSource(nameof(Track.UserId))]
    public partial TrendingTrackResponseDto FromEntity(Track entity);

    public partial List<TrendingTrackResponseDto> FromEntity(List<Track> entity);
}

================
File: CrateApi.Services/CrateApi.Services.csproj
================
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LineEndings>LF</LineEndings>

    <WarningsAsErrors>true</WarningsAsErrors>
  </PropertyGroup>


  <ItemGroup>
    <CompilerVisibleProperty Include="RoslynExtensionsOptions_EnableAnalyzersSupport" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="LanguageExt.Core" Version="4.4.9" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="OneOf" Version="3.0.271" />
    <PackageReference Include="Riok.Mapperly" Version="4.1.1" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CrateApi.Data\CrateApi.Data.csproj" />
    <ProjectReference Include="..\CrateApi.Common\CrateApi.Common.csproj" />
  </ItemGroup>
</Project>

================
File: CrateApi.Services/UserManager.cs
================
using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace CrateApi.Services;

public record UserContext(Guid UserId, bool IsAuthenticated);

public interface IUserManager
{
    UserContext CurrentUser { get; }
}

public class UserManager : IUserManager
{
    private readonly Lazy<UserContext> _userContext;

    public UserManager(IHttpContextAccessor httpContextAccessor)
    {
        _userContext = new Lazy<UserContext>(() =>
        {
            var user = httpContextAccessor.HttpContext?.User;

            if (user is null)
            {
                return new UserContext(Guid.Empty, false);
            }

            var userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                return new UserContext(Guid.Empty, false);
            }

            return new UserContext(Guid.Parse(userId), true);
        });
    }

    public UserContext CurrentUser => _userContext.Value;
}

================
File: UnfurlApi/Controllers/ErrorController.cs
================
using CrateApi.Common.Dto.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Net;

namespace UnfurlApi.Controllers;

[ApiController]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/v1/[controller]")]
public class ErrorController : ControllerBase
{
    [Route("")]
    [AllowAnonymous]
    public IActionResult Error()
    {
        return StatusCode(500, new ErrorResponseDto("An Unexpected error occured. Please check the logs."));
    }
}

================
File: UnfurlApi/Controllers/UnfurlController.cs
================
using System.Reflection.Metadata;
using CrateApi.Common;
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Exceptions;
using LanguageExt;
using Microsoft.AspNetCore.Mvc;
using UnfurlApi.Services;
using UnfurlApi.Services.Platforms;

namespace UnfurlApi.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class UnfurlController(
    ISpotifyService spotifyService,
    IAppleService appleService,
    IGenericService genericService,
    IYouTubeMusicService youTubeMusicService,
    ILogger<UnfurlController> logger
) : ControllerBase
{
    [HttpPost("spotify")]
    [ProducesResponseType(typeof(SpotifyTrack), 200)]
    public async Task<IActionResult> UnfurlSpotify([FromBody] UnfurlUrlRequestDto model)
    {
        var url = new Uri(model.Url);
        var response = await (spotifyService.FetchResource(url).Try());

        return response.Match<IActionResult>(
            result => result.Match<IActionResult>(track => Ok(track), album => NoContent()),
            ExceptionResult.Handle(logger)
        );
    }

    [HttpPost("apple")]
    [ProducesResponseType(typeof(AppleTrack), 200)]
    public async Task<IActionResult> UnfurlApple([FromBody] UnfurlUrlRequestDto model)
    {
        var url = new Uri(model.Url);
        var response = await (appleService.FetchResource(url).Try());

        return response.Match<IActionResult>(
            result => Ok(result),
            ExceptionResult.Handle(logger)
        );
    }

    [HttpPost("youtube")]
    [ProducesResponseType(typeof(YouTubeMusicTrack), 200)]
    public async Task<IActionResult> UnfurlYouTube([FromBody] UnfurlUrlRequestDto model)
    {
        var url = new Uri(model.Url);
        var response = await (youTubeMusicService.FetchResource(url).Try());

        return response.Match<IActionResult>(
            result => Ok(result),
            ExceptionResult.Handle(logger)
        );
    }

    [HttpPost("unsupported")]
    [ProducesResponseType(typeof(GenericTrack), 200)]
    public async Task<IActionResult> UnfurlGeneric([FromBody] UnfurlUrlRequestDto model)
    {
        var url = new Uri(model.Url);
        var response = await (genericService.FetchResource(url).Try());

        return response.Match<IActionResult>(
            result => Ok(result),
            ExceptionResult.Handle(logger)
        );
    }
}

================
File: UnfurlApi/Infrastructure/HttpClientExtensions.cs
================
using UnfurlApi.Services.Clients;

namespace UnfurlApi.Infrastructure;

public static class HttpClientExtensions
{
    public static IServiceCollection ConfigureHttpClientExt(this IServiceCollection services)
    {
        services.AddHttpClient(
            SpotifyClient.ClientName,
            client =>
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0");
                client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
                client.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9");
                client.DefaultRequestHeaders.Add("Accept-Encoding", "identity");
                client.DefaultRequestHeaders.Add("Connection", "keep-alive");
                client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
            }
        );
        services.AddHttpClient(
            AppleClient.ClientName,
            client =>
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0");
                client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
                client.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9");
                client.DefaultRequestHeaders.Add("Accept-Encoding", "identity");
                client.DefaultRequestHeaders.Add("Connection", "keep-alive");
                client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
            }
        );
        services.AddHttpClient(
            GenericClient.ClientName,
            client =>
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0");
                client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
                client.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9");
                client.DefaultRequestHeaders.Add("Accept-Encoding", "identity");
                client.DefaultRequestHeaders.Add("Connection", "keep-alive");
                client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
            }
        );
        services.AddHttpClient(
            YouTubeMusicClient.ClientName,
            client =>
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0");
                client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
                client.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9");
                client.DefaultRequestHeaders.Add("Accept-Encoding", "identity");
                client.DefaultRequestHeaders.Add("Connection", "keep-alive");
                client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
                // YouTube Music specific headers
                client.DefaultRequestHeaders.Add("X-YouTube-Client-Name", "67");
                client.DefaultRequestHeaders.Add("X-YouTube-Client-Version", "1.20240125.01.00");
            }
        );
        return services;
    }
}

================
File: UnfurlApi/Infrastructure/SwaggerExtensions.cs
================
using Microsoft.OpenApi.Models;

namespace UnfurlApi.Infrastructure;

public static class SwaggerExtensions
{
    public static IServiceCollection ConfigureSwaggerExt(this IServiceCollection services)
    {
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "CrateApi", Version = "v1" });
            c.CustomSchemaIds(type => $"{type.ToString()}");
        });
        return services;
    }
}

================
File: UnfurlApi/Properties/launchSettings.json
================
{
    "$schema": "http://json.schemastore.org/launchsettings.json",
    "iisSettings": {
        "windowsAuthentication": false,
        "anonymousAuthentication": true,
        "iisExpress": {
            "applicationUrl": "http://localhost:18680",
            "sslPort": 44357
        }
    },
    "profiles": {
        "https": {
            "commandName": "Project",
            "dotnetRunMessages": true,
            "launchBrowser": true,
            "launchUrl": "swagger",
            "applicationUrl": "http://localhost:8001",
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            }
        },
        "IIS Express": {
            "commandName": "IISExpress",
            "launchBrowser": true,
            "launchUrl": "swagger",
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            }
        }
    }
}

================
File: UnfurlApi/appsettings.json
================
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}

================
File: UnfurlApi/DiConfig.cs
================
using UnfurlApi.Infrastructure;
using UnfurlApi.Services.Clients;
using UnfurlApi.Services.Platforms;

namespace UnfurlApi;

public static class Di
{
    public static void Configure(WebApplicationBuilder builder)
    {
        var services = builder.Services;
        var config = builder.Configuration;

        // Required Microsoft Dependencies
        services.ConfigureSwaggerExt();
        services.AddControllers();
        services.AddRouting(options => options.LowercaseUrls = true);
        services.AddHttpContextAccessor();
        // Our service code
        services.ConfigureHttpClientExt();
        services.AddScoped<ISpotifyClient, SpotifyClient>(e =>
        {
            var httpClientFactory = e.GetRequiredService<IHttpClientFactory>();
            return SpotifyClient.Create(httpClientFactory).GetAwaiter().GetResult();
        });
        services.AddScoped<IAppleClient, AppleClient>(e =>
        {
            var httpClientFactory = e.GetRequiredService<IHttpClientFactory>();
            return AppleClient.Create(httpClientFactory);
        });
        services.AddScoped<IGenericClient, GenericClient>(e =>
        {
            var httpClientFactory = e.GetRequiredService<IHttpClientFactory>();
            return GenericClient.Create(httpClientFactory);
        });
        services.AddScoped<IYouTubeMusicClient, YouTubeMusicClient>(e =>
        {
            var httpClientFactory = e.GetRequiredService<IHttpClientFactory>();
            return YouTubeMusicClient.Create(httpClientFactory);
        });
        services.AddScoped<ISpotifyService, SpotifyService>();
        services.AddScoped<IAppleService, AppleService>();
        services.AddScoped<IGenericService, GenericService>();
        services.AddScoped<IYouTubeMusicService, YouTubeMusicService>();
    }
}

================
File: UnfurlApi/Dockerfile
================
# UnfurlApi/Dockerfile

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy application source files
COPY . .

# Downloads and installs packages dependencies
RUN dotnet restore "UnfurlApi/UnfurlApi.csproj"

# Compile app
WORKDIR /src/UnfurlApi
RUN dotnet publish -c Release -o /app/publish

# Multi-stage build for minimal size and dependencies
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY --from=build /app/publish .

ENTRYPOINT ["dotnet", "UnfurlApi.dll"]

================
File: UnfurlApi/Program.cs
================
using UnfurlApi;

var builder = WebApplication.CreateBuilder(args);

Di.Configure(builder);

var app = builder.Build();

var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("UnfurlApi is starting up...");
logger.LogInformation("Environment: {Environment}", app.Environment.EnvironmentName);

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    logger.LogInformation("Running in Development mode");
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseRouting();
app.UseAuthorization();
app.MapControllers();
app.UseExceptionHandler("/api/v1/error");

logger.LogInformation("UnfurlApi is ready to accept requests");
await app.RunAsync();

================
File: UnfurlApi/UnfurlApi.csproj
================
<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="LanguageExt.Core" Version="4.4.9" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.12" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UnfurlApi.Services\UnfurlApi.Services.csproj" />
  </ItemGroup>

</Project>

================
File: UnfurlApi.Services/Clients/AppleClient.cs
================
using System.Text;
using LanguageExt;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace UnfurlApi.Services.Clients;

public interface IAppleClient : IClient { }

public class AppleClient : IAppleClient
{
    private const string BaseUrl = "https://music.apple.com";
    public const string ClientName = "AppleClient";
    private readonly HttpClient client;

    private AppleClient(HttpClient client)
    {
        this.client = client;
    }

    public static AppleClient Create(IHttpClientFactory hcf)
    {
        var client = hcf.CreateClient(ClientName);
        client.DefaultRequestHeaders.Add(
            "User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        );
        return new AppleClient(client);
    }

    public async Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default)
    {
        var response = await client.GetAsync($"{BaseUrl}{endpoint}", cancellationToken);
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(content);
    }

    public async Task<T?> PostAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );

        var response = await client.PostAsync($"{BaseUrl}{endpoint}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(responseContent);
    }

    public async Task<T?> PutAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );
        var response = await client.PutAsync($"{BaseUrl}{endpoint}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(responseContent);
    }

    private static StringContent CreateRequest(object data) =>
        new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");
}

================
File: UnfurlApi.Services/Clients/GenericClient.cs
================
using HtmlAgilityPack;

namespace UnfurlApi.Services.Clients;

public interface IGenericClient
{
    Task<HtmlDocument> GetAsync(string endpoint, CancellationToken cancellationToken = default);
}

public class GenericClient(HttpClient client) : IGenericClient
{
    public const string ClientName = "GenericClient";

    public static GenericClient Create(IHttpClientFactory hcf)
    {
        var client = hcf.CreateClient(ClientName);
        client.DefaultRequestHeaders.Add(
            "User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        );
        return new GenericClient(client);
    }

    public async Task<HtmlDocument> GetAsync(string url, CancellationToken cancellationToken = default)
    {
        var response = await client.GetAsync($"{url}", cancellationToken);
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync(cancellationToken);

        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(content);
        return htmlDoc;
    }
}

================
File: UnfurlApi.Services/Clients/SpotifyClient.cs
================
using System.Text;
using HtmlAgilityPack;
using LanguageExt;
using static LanguageExt.Prelude;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using LanguageExt.ClassInstances.Pred;

namespace UnfurlApi.Services.Clients;

public interface IClient
{
    Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default);
    Task<T?> PostAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default);
    Task<T?> PutAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default);
}

public interface ISpotifyClient : IClient { }

public class SpotifyClient : ISpotifyClient
{
    public const string ClientName = "SpotifyClient";
    private readonly HttpClient client;
    private const string BaseUrl = "https://api.spotify.com/v1";
    private const string SpotifyUrl = "https://open.spotify.com/search";

    private SpotifyClient(HttpClient client)
    {
        this.client = client;
    }

    public static async Task<SpotifyClient> Create(IHttpClientFactory hcf)
    {
        var client = hcf.CreateClient(ClientName);
        await ExtractSpotifyKeys(client);
        return new SpotifyClient(client);
    }

    private static async Task ExtractSpotifyKeys(HttpClient client)
    {
        var response = await client.GetAsync(SpotifyUrl);
        response.EnsureSuccessStatusCode();
        var htmlContent = await response.Content.ReadAsStringAsync();
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(htmlContent);
        var scriptTag = htmlDoc.DocumentNode.SelectSingleNode("//script[@id='session']");
        if (scriptTag != null)
        {
            var sessionData = JObject.Parse(scriptTag.InnerText);
            var accessToken = sessionData.GetValue("accessToken")?.ToString() ?? "";
            if (!client.DefaultRequestHeaders.Contains("Authorization"))
            {
                client.DefaultRequestHeaders.Add("Authorization", $"Bearer {accessToken}");
            }
            return;
        }
        throw new ArgumentException("Failed to set access token.");
    }

    public async Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default)
    {
        var response = await client.GetAsync($"{BaseUrl}{endpoint}", cancellationToken);
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        if (typeof(T) == typeof(JObject))
        {
            return (T)(object)JObject.Parse(content);
        }
        return JsonConvert.DeserializeObject<T>(content);
    }

    public async Task<T?> PostAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );

        var response = await client.PostAsync($"{BaseUrl}{endpoint}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(responseContent);
    }

    public async Task<T?> PutAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );
        var response = await client.PutAsync($"{BaseUrl}{endpoint}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(responseContent);
    }

    private static StringContent CreateRequest(object data) =>
        new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");
}

================
File: UnfurlApi.Services/Clients/YouTubeMusicClient.cs
================
using System.Text;
using System.Text.RegularExpressions;
using HtmlAgilityPack;
using LanguageExt;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using static LanguageExt.Prelude;

namespace UnfurlApi.Services.Clients;

public interface IYouTubeMusicClient : IClient { }

public class YouTubeMusicClient : IYouTubeMusicClient
{
    public const string ClientName = "YouTubeMusicClient";
    private readonly HttpClient client;
    private const string BaseUrl = "https://music.youtube.com";

    private YouTubeMusicClient(HttpClient client)
    {
        this.client = client;
    }

    public static YouTubeMusicClient Create(IHttpClientFactory hcf)
    {
        var client = hcf.CreateClient(ClientName);
        return new YouTubeMusicClient(client);
    }

    public async Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default)
    {
        var response = await client.GetAsync($"{BaseUrl}{endpoint}", cancellationToken);
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        
        if (typeof(T) == typeof(string))
        {
            return (T)(object)content;
        }
        
        return JsonConvert.DeserializeObject<T>(content);
    }

    public async Task<T?> PostAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );

        var response = await client.PostAsync($"{BaseUrl}{endpoint}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(responseContent);
    }

    public async Task<T?> PutAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );
        var response = await client.PutAsync($"{BaseUrl}{endpoint}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(responseContent);
    }

    private static StringContent CreateRequest(object data) =>
        new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");
}

================
File: UnfurlApi.Services/Platforms/AppleService.cs
================
using CrateApi.Common;
using CrateApi.Common.Exceptions;
using HtmlAgilityPack;
using LanguageExt;
using Microsoft.Extensions.Logging;
using UnfurlApi.Services.Clients;
using static LanguageExt.Prelude;

namespace UnfurlApi.Services.Platforms;

public interface IAppleService
{
    TryAsync<AppleTrack> FetchResource(Uri uri);
}

public class AppleService(IAppleClient client) : IAppleService
{
    public TryAsync<AppleTrack> FetchResource(Uri uri)
    {
        return TryAsync(async () => {
            var trackId = ExtractId(uri);
            return await GetTrackData(uri);
        });
    }

    private string ExtractId(Uri uri)
    {
        if (!uri.Host.Contains("apple.com"))
        {
            throw new ScraperException("[UNFURL] Invalid music URL format");
        }

        var parts = uri.AbsolutePath.Trim('/').Split('/');
        if (!parts.Contains("album"))
        {
            throw new ScraperException("[UNFURL] Invalid music URL format");
        }

        return parts[^1];
    }

    private async Task<AppleTrack> GetTrackData(Uri url)
    {
        var response = await client.GetAsync<string>(url.ToString());
        if (response is null)
        {
            throw new ScraperException("[UNFURL] Failed to fetch content");
        }


        var doc = new HtmlDocument();
        doc.LoadHtml(response);

        var title = GetTitle(doc);
        var artist = GetArtist(doc);
        var imageUrl = GetImageUrl(doc);

        return new AppleTrack(title, artist, imageUrl);
    }

    private string GetTitle(HtmlDocument doc)
    {
        var titleMeta = doc.DocumentNode.SelectSingleNode("//meta[@property='og:title']");
        if (titleMeta is null)
        {
            throw new ScraperException("[UNFURL] Missing metadata");
        }

        var titleContent = titleMeta.GetAttributeValue("content", "");
        var titleParts = titleContent.Split(" by ");
        if (titleParts.Length < 2)
        {
            throw new ScraperException("[UNFURL] Unexpected metadata format");
        }

        return titleParts[0];
    }

    private string GetArtist(HtmlDocument doc)
    {
        var titleMeta = doc.DocumentNode.SelectSingleNode("//meta[@property='og:title']");
        if (titleMeta is null)
        {
            throw new ScraperException("[UNFURL] Missing metadata");
        }

        var titleContent = titleMeta.GetAttributeValue("content", "");
        var byParts = titleContent.Split(" by ");
        if (byParts.Length < 2)
        {
            throw new ScraperException("[UNFURL] Unexpected metadata format");
        }

        var artistParts = byParts[1].Split(" on ");
        return artistParts[0];
    }

    private string GetImageUrl(HtmlDocument doc)
    {
        var pictureElem = doc.DocumentNode.SelectSingleNode("//picture[@class='svelte-3e3mdo']");
        if (pictureElem is null)
        {
            throw new ScraperException("[UNFURL] Required element not found");
        }

        var jpegSource = pictureElem.SelectSingleNode(".//source[@type='image/jpeg']");
        if (jpegSource is null)
        {
            throw new ScraperException("[UNFURL] Required element not found");
        }

        var srcset = jpegSource.GetAttributeValue("srcset", "").Split(',');
        if (srcset.Length == 0)
        {
            throw new ScraperException("[UNFURL] Required attribute not found");
        }

        var highestResPart = srcset[^1].Trim();
        var highestResUrl = highestResPart.Split(' ')[0];

        return highestResUrl.Replace("316x316bb-60.jpg", "2000x2000bf-60.jpg");
    }
}

================
File: UnfurlApi.Services/Platforms/GenericService.cs
================
using CrateApi.Common;
using CrateApi.Common.Exceptions;
using HtmlAgilityPack;
using LanguageExt;
using Microsoft.Extensions.Logging;
using UnfurlApi.Services.Clients;
using static LanguageExt.Prelude;

namespace UnfurlApi.Services.Platforms;

public interface IGenericService
{
    TryAsync<GenericTrack> FetchResource(Uri uri);
}

public class GenericService(IGenericClient client, ILogger<GenericService> logger) : IGenericService
{
    public TryAsync<GenericTrack> FetchResource(Uri uri)
    {
        return TryAsync(async () => {
            var htmlDoc = await client.GetAsync(uri.ToString());
            if (htmlDoc == null)
            {
                throw new ScraperException("[UNFURL] Failed to fetch content");
            }

            return ExtractOpenGraphData(htmlDoc);
        });
    }

    private GenericTrack ExtractOpenGraphData(HtmlDocument htmlDoc)
    {
        var metaTags = htmlDoc.DocumentNode.SelectNodes("//meta[contains(@property, 'og:')]");
        if (metaTags == null)
        {
            logger.LogWarning("[UNFURL] No OpenGraph meta tags found");
        }

        Option<string> title = None;
        Option<string> description = None;
        Option<string> url = None;

        if (metaTags != null)
        {
            foreach (var tag in metaTags)
            {
                var property = tag.GetAttributeValue("property", "");
                var content = tag.GetAttributeValue("content", "");

                if (string.IsNullOrWhiteSpace(content))
                    continue;

                switch (property)
                {
                    case "og:title":
                        title = content;
                        break;
                    case "og:description":
                        description = content;
                        break;
                    case "og:url":
                        url = content;
                        break;
                }
            }
        }

        if (title.IsNone)
        {
            var titleElement = htmlDoc.DocumentNode.SelectSingleNode("//title");
            if (titleElement is not null && !string.IsNullOrWhiteSpace(titleElement.InnerText))
            {
                title = titleElement.InnerText.Trim();
                logger.LogInformation("[UNFURL] Using HTML title element as fallback");
            }
        }

        if (title.IsNone)
        {
            throw new ScraperException("[UNFURL] Required metadata not found");
        }

        return new GenericTrack(
            title.Match(d => d, () => string.Empty),
            description.Match(d => d, () => string.Empty),
            url.Match(u => u, None: () => string.Empty)
        );
    }
}

================
File: UnfurlApi.Services/Platforms/SpotifyService.cs
================
using CrateApi.Common;
using CrateApi.Common.Dto.Response;
using Newtonsoft.Json.Linq;
using UnfurlApi.Services.Clients;
using CrateApi.Common.Exceptions;
using LanguageExt;
using LanguageExt.TypeClasses;
using static LanguageExt.Prelude;

namespace UnfurlApi.Services.Platforms;

public interface ISpotifyService
{
    TryAsync<Either<AlbumMetadata, SpotifyTrack>> FetchResource(Uri uri);
}

public class SpotifyService(ISpotifyClient client) : ISpotifyService
{
    public TryAsync<Either<AlbumMetadata, SpotifyTrack>> FetchResource(Uri uri)
    {
        return TryAsync(async () => {
            var resource = ExtractId(uri);
            return await FetchResourceData(resource.Type, resource.Id);
        });
    }
    private static (string Type, string Id) ExtractId(Uri uri)
    {
        var parts = uri.AbsolutePath.Trim('/').Split('/');
        if (parts.Length == 2 && (parts[0] == "track" || parts[0] == "album"))
        {
            return (parts[0], parts[1]);
        }

        throw new ScraperException($"[UNFURL] Invalid Spotify URL format, URL:{uri.ToString()}");
    }

    private async Task<Either<AlbumMetadata, SpotifyTrack>> FetchResourceData(string resourceType, string resourceId)
    {
        Either<AlbumMetadata, SpotifyTrack> result = resourceType switch
        {
            "track" => await GetTrackData(resourceId),
            "album" => await GetAlbumData(resourceId),
            _ => throw new ScraperException($"[UNFURL] Unsupported resource type: {resourceType}"),
        };
        return result;
    }

    private async Task<AlbumMetadata> GetAlbumData(string albumId)
    {
        var url = $"/albums/{albumId}";
        var response = await client.GetAsync<JObject>(url);
        if (response is null)
        {
            throw new ScraperException($"[UNFURL] Failed to deserialize response URL: {url}");
        }
        try
        {
            var title = response["name"]!.ToString();
            var artist = response["artists"]![0]!["name"]!.ToString();
            var mediaUrl = response["images"]![0]!["url"]!.ToString();
            return new AlbumMetadata(title, artist, mediaUrl);
        }
        catch (Exception ex)
        {
            throw new ScraperException($"[UNFURL] Failed to parse album", ex);
        }
    }

    private async Task<SpotifyTrack> GetTrackData(string trackId)
    {
        var url = $"/tracks/{trackId}";
        var response = await client.GetAsync<SpotifyTrack>(url);
        if (response is null)
        {
            throw new ScraperException($"[UNFURL] Failed to deserialize response URL: {url}");
        }
        return response!;
    }
}

================
File: UnfurlApi.Services/Platforms/YouTubeMusicService.cs
================
using System.Text.RegularExpressions;
using CrateApi.Common;
using CrateApi.Common.Exceptions;
using HtmlAgilityPack;
using LanguageExt;
using UnfurlApi.Services.Clients;
using static LanguageExt.Prelude;

namespace UnfurlApi.Services.Platforms;

public interface IYouTubeMusicService
{
    TryAsync<YouTubeMusicTrack> FetchResource(Uri uri);
}

public class YouTubeMusicService(IYouTubeMusicClient client, ISpotifyService spotifyService)
    : IYouTubeMusicService
{
    private static readonly Regex VideoIdRegex = new(
        @"(watch\?v=|youtu\.be\/|music\.youtube\.com\/watch\?v=)([^&\s]+)",
        RegexOptions.Compiled
    );

    public TryAsync<YouTubeMusicTrack> FetchResource(Uri uri)
    {
        return TryAsync(async () =>
        {
            var videoId = ExtractVideoId(uri);
            if (string.IsNullOrEmpty(videoId))
            {
                throw new ScraperException($"[UNFURL] Could not extract video ID from URL: {uri}");
            }

            var endpoint = $"/watch?v={videoId}";
            var html = await client.GetAsync<string>(endpoint);

            if (string.IsNullOrEmpty(html))
            {
                throw new ScraperException(
                    $"[UNFURL] Failed to get HTML from YouTube Music: {uri}"
                );
            }

            var track = ExtractTrackInfo(html, videoId, uri.ToString());

            // Try to get ISRC by cross-referencing with Spotify
            var isrc = await FindIsrc(track.Artist, track.Title);

            return isrc.Match(Some: code => track with { Isrc = code }, None: () => track);
        });
    }

    private async Task<Option<string>> FindIsrc(string artist, string title)
    {
        try
        {
            // Construct a fake Spotify URI to search for the track
            var searchUri = new Uri(
                $"https://open.spotify.com/search/{Uri.EscapeDataString($"{artist} {title}")}"
            );

            // Use the Spotify service to find matching tracks
            var spotifyResult = await spotifyService.FetchResource(searchUri);

            return spotifyResult.Match(
                track => track.Match(e => Option<string>.Some(e.Isrc), e => Option<string>.None),
                ex => Option<string>.None
            );
        }
        catch
        {
            return Option<string>.None;
        }
    }

    private static string ExtractVideoId(Uri uri)
    {
        var match = VideoIdRegex.Match(uri.ToString());
        return match.Success && match.Groups.Count > 2 ? match.Groups[2].Value : string.Empty;
    }

    private static YouTubeMusicTrack ExtractTrackInfo(string html, string videoId, string url)
    {
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(html);

        // Extract title and artist from the HTML
        // YouTube Music typically has a structure like:
        // <title>Song Name • Artist - YouTube Music</title>
        var titleNode = htmlDoc.DocumentNode.SelectSingleNode("//title");
        var titleText = titleNode?.InnerText ?? "";

        // Parse title text to extract song name and artist
        var titleParts = titleText.Split(
            new[] { " • ", " - YouTube Music" },
            StringSplitOptions.RemoveEmptyEntries
        );

        string title = titleParts.Length > 0 ? titleParts[0] : "Unknown Title";
        string artist =
            titleParts.Length > 1
                ? titleParts[1].Replace(" - YouTube Music", "")
                : "Unknown Artist";

        // Extract album if available
        string album = "Unknown Album"; // Default value
        var albumNode = htmlDoc.DocumentNode.SelectSingleNode(
            "//span[contains(@class, 'subtitle')]"
        );
        if (albumNode != null)
        {
            album = albumNode.InnerText.Trim();
        }

        // Extract thumbnail URL
        var thumbnailUrl = ExtractThumbnailUrl(htmlDoc, videoId);

        return new YouTubeMusicTrack
        {
            VideoId = videoId,
            Title = title,
            Artist = artist,
            Album = album,
            ThumbnailUrl = thumbnailUrl,
            Url = url,
        };
    }

    private static string ExtractThumbnailUrl(HtmlDocument htmlDoc, string videoId)
    {
        // Try to find thumbnail in the HTML first
        var imgNode = htmlDoc.DocumentNode.SelectSingleNode("//img[contains(@class, 'thumbnail')]");
        if (imgNode != null)
        {
            var srcAttribute = imgNode.Attributes["src"];
            if (srcAttribute != null)
            {
                return srcAttribute.Value;
            }
        }

        // Fallback to the YouTube thumbnail URL format
        return $"https://i.ytimg.com/vi/{videoId}/hqdefault.jpg";
    }
}

================
File: UnfurlApi.Services/UnfurlApi.Services.csproj
================
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="HtmlAgilityPack" Version="1.11.72" />
    <PackageReference Include="LanguageExt.Core" Version="4.4.9" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CrateApi.Common\CrateApi.Common.csproj" />
  </ItemGroup>


</Project>

================
File: .editorconfig
================
# EditorConfig is awesome: https://EditorConfig.org
# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 4
insert_final_newline = true
trim_trailing_whitespace = true
max_line_length = 100

# C# files
[*.cs]
#dotnet_analyzer_diagnostic.category-Globalization.severity = warning
dotnet_analyzer_diagnostic.category-Style.severity = none
dotnet_analyzer_diagnostic.category-CodeStyle.severity = none
dotnet_analyzer_diagnostic.category-Performance.severity = none
dotnet_analyzer_diagnostic.category-Reliability.severity = none
dotnet_analyzer_diagnostic.category-Security.severity = none
dotnet_analyzer_diagnostic.category-Usage.severity = none

# Braces and control flow
#csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_between_query_expression_clauses = true
csharp_indent_case_contents = true
csharp_indent_switch_labels = true
csharp_indent_labels = no_change

# Spaces
csharp_space_after_cast = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_around_binary_operators = before_and_after
# LoggerMessage delegate
dotnet_diagnostic.CA1848.severity = none

# Prefer expression body
csharp_style_expression_bodied_methods = when_on_single_line:suggestion
csharp_style_expression_bodied_properties = true:suggestion
csharp_style_expression_bodied_accessors = true:suggestion

# XML project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_size = 2
indent_style = space
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

# XML config files
[*.{props,targets,ruleset,config,nuspec,resx,vsixmanifest,vsct}]
indent_size = 2
indent_style = space

# JSON files
[*.{json,json5}]
indent_size = 4

# YAML files
[*.{yml,yaml}]
indent_size = 4

# XML files
[*.{xml,xaml}]
indent_size = 4

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# Web files
[*.{htm,html,js,ts,css,scss,less}]
indent_size = 4
max_line_length = 200

# Shell scripts
[*.sh]
end_of_line = lf

# Windows scripts
[*.{cmd,bat,ps1}]
end_of_line = crlf

================
File: .gitattributes
================
* text eol=lf
*.cs text eol=lf
*.csproj text eol=lf
*.sln text eol=lf"

================
File: .gitignore
================
## Ignore Visual Studio temporary files, build results, and
## files generated by popular Visual Studio add-ons.
##
## Get latest from https://github.com/github/gitignore/blob/main/VisualStudio.gitignore

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Mono auto generated files
mono_crash.*

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio 2015/2017 cache/options directory
.vs/
# Uncomment if you have tasks that create the project's static files in wwwroot
#wwwroot/

# Visual Studio 2017 auto generated files
Generated\ Files/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit
*.VisualState.xml
TestResult.xml
nunit-*.xml

# Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

# Benchmark Results
BenchmarkDotNet.Artifacts/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# StyleCop
StyleCopReport.xml

# Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
# but not Directory.Build.rsp, as it configures directory-level build defaults
!Directory.Build.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.tlog
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Chutzpah Test files
_Chutzpah*

# Visual C++ cache files
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# Visual Studio Trace Files
*.e2e

# TFS 2012 Local Workspace
$tf/

# Guidance Automation Toolkit
*.gpState

# ReSharper is a .NET coding add-in
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# TeamCity is a build add-in
_TeamCity*

# DotCover is a Code Coverage Tool
*.dotCover

# AxoCover is a Code Coverage Tool
.axoCover/*
!.axoCover/settings.json

# Coverlet is a free, cross platform Code Coverage Tool
coverage*.json
coverage*.xml
coverage*.info

# Visual Studio code coverage results
*.coverage
*.coveragexml

# NCrunch
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

# MightyMoose
*.mm.*
AutoTest.Net/

# Web workbench (sass)
.sass-cache/

# Installshield output folder
[Ee]xpress/

# DocProject is a documentation generator add-in
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/*.hhc
DocProject/Help/*.hhk
DocProject/Help/*.hhp
DocProject/Help/Html2
DocProject/Help/html

# Click-Once directory
publish/

# Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
# Note: Comment the next line if you want to checkin your web deploy settings,
# but database connection strings (with potential passwords) will be unencrypted
*.pubxml
*.publishproj

# Microsoft Azure Web App publish settings. Comment the next line if you want to
# checkin your Azure Web App publish settings, but sensitive information contained
# in these scripts will be unencrypted
PublishScripts/

# NuGet Packages
*.nupkg
# NuGet Symbol Packages
*.snupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/[Pp]ackages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# Microsoft Azure Build Output
csx/
*.build.csdef

# Microsoft Azure Emulator
ecf/
rcf/

# Windows Store app package directories and files
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx
*.appxbundle
*.appxupload

# Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!?*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs

# Including strong name files can present a security risk
# (https://github.com/github/gitignore/pull/2483#issue-259490424)
#*.snk

# Since there are multiple workflows, uncomment next line to ignore bower_components
# (https://github.com/github/gitignore/pull/1529#issuecomment-104372622)
#bower_components/

# RIA/Silverlight projects
Generated_Code/

# Backup & report files from converting an old project file
# to a newer Visual Studio version. Backup files are not needed,
# because we have git ;-)
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm
ServiceFabricBackup/
*.rptproj.bak

# SQL Server files
*.mdf
*.ldf
*.ndf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser
*- [Bb]ackup.rdl
*- [Bb]ackup ([0-9]).rdl
*- [Bb]ackup ([0-9][0-9]).rdl

# Microsoft Fakes
FakesAssemblies/

# GhostDoc plugin setting file
*.GhostDoc.xml

# Node.js Tools for Visual Studio
.ntvs_analysis.dat
node_modules/

# Visual Studio 6 build log
*.plg

# Visual Studio 6 workspace options file
*.opt

# Visual Studio 6 auto-generated workspace file (contains which files were open etc.)
*.vbw

# Visual Studio 6 auto-generated project file (contains which files were open etc.)
*.vbp

# Visual Studio 6 workspace and project file (working project files containing files to include in project)
*.dsw
*.dsp

# Visual Studio 6 technical files
*.ncb
*.aps

# Visual Studio LightSwitch build output
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtifacts
**/*.Server/ModelManifest.xml
_Pvt_Extensions

# Paket dependency manager
.paket/paket.exe
paket-files/

# FAKE - F# Make
.fake/

# CodeRush personal settings
.cr/personal

# Python Tools for Visual Studio (PTVS)
__pycache__/
*.pyc

# Cake - Uncomment if you are using it
# tools/**
# !tools/packages.config

# Tabs Studio
*.tss

# Telerik's JustMock configuration file
*.jmconfig

# BizTalk build output
*.btp.cs
*.btm.cs
*.odx.cs
*.xsd.cs

# OpenCover UI analysis results
OpenCover/

# Azure Stream Analytics local run output
ASALocalRun/

# MSBuild Binary and Structured Log
*.binlog

# NVidia Nsight GPU debugger configuration file
*.nvuser

# MFractors (Xamarin productivity tool) working folder
.mfractor/

# Local History for Visual Studio
.localhistory/

# Visual Studio History (VSHistory) files
.vshistory/

# BeatPulse healthcheck temp database
healthchecksdb

# Backup folder for Package Reference Convert tool in Visual Studio 2017
MigrationBackup/

# Ionide (cross platform F# VS Code tools) working folder
.ionide/

# Fody - auto-generated XML schema
FodyWeavers.xsd

# VS Code files for those working on multiple tools
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Local History for Visual Studio Code
.history/

# Windows Installer files from build outputs
*.cab
*.msi
*.msix
*.msm
*.msp

# JetBrains Rider
*.sln.iml

# Appsettings development
**/appsettings.Development.json

# database
*.db

================
File: CLAUDE.md
================
# CrateNFCAPI Project Guide

## Project Overview

CrateNFCAPI is a music sharing system backend that allows users to manage and share music tracks through NFC cards. It serves as the backend for a mobile app that enables users to scan NFC cards to access music collections.

## System Architecture

The system is split into two main components:

1. **CrateApi**: Main API service for mobile clients handling user authentication, track management, collections, and NFC cards
2. **UnfurlApi**: Specialized service for scraping music service metadata, maintaining separate concerns for better maintainability

## Key Features

- Track unfurling (extracting metadata from various music service URLs)
- Track collections management
- NFC card registration and management
- User management and authentication
- Cross-referencing between music services to get additional metadata

## Supported Music Services

- Spotify
- Apple Music
- YouTube Music (recently added)
- Generic web pages (fallback for unsupported services)

## Code Conventions

### Architecture Patterns

- Multi-layered architecture with controllers, services, and data layers
- Entity Framework Core for database operations
- LanguageExt for functional programming patterns

### Coding Conventions

- **Primary Constructors**: Use C# primary constructors for dependency injection
- **Field Naming**: DO NOT prefix private fields with underscores (`_`)
- **LanguageExt**: Utilize LanguageExt patterns for better functional programming style
- **DTOs**: Separate request and response DTOs in the Common project
- **Error Handling**: Use Exception handlers and TryAsync pattern for error handling

## Project Structure

- **CrateApi.Common**: Shared models, DTOs, and utilities
- **CrateApi.Data**: Entity Framework models and database context
- **CrateApi.Services**: Business logic and service implementations
- **CrateApi**: Main API controllers and endpoint definitions
- **UnfurlApi.Services**: Music service scrapers and platform-specific clients
- **UnfurlApi**: API for music metadata extraction

## Key Implementation Patterns

### Track Unfurling Process

1. Client sends URL to CrateApi
2. CrateApi determines service type (Spotify, Apple Music, YouTube Music, etc.)
3. CrateApi forwards request to UnfurlApi with appropriate endpoint
4. UnfurlApi scrapes metadata and returns structured track data
5. CrateApi saves track (if authenticated) and optionally creates a shortened link via dub.co

### Cross-Referencing for ISRC

For services that don't directly provide ISRC codes (like YouTube Music):

1. Extract basic track metadata (title, artist)
2. Use Spotify service to search for matching track
3. When a match is found, extract ISRC from Spotify data
4. Return combined metadata with the retrieved ISRC

## Enhancements & Future Work

- Support for more music services
- Improved match accuracy for cross-referencing
- Caching for better performance
- Additional track metadata

## Useful Development Commands

For future development tasks, use these commands:

```bash
# Build the project
dotnet build

# Run the API locally 
dotnet run --project CrateApi/CrateApi.csproj

# Run the unfurl service locally
dotnet run --project UnfurlApi/UnfurlApi.csproj

# Entity Framework migrations
dotnet ef migrations add <MigrationName> --project CrateApi.Data/CrateApi.Data.csproj --startup-project CrateApi/CrateApi.csproj
dotnet ef database update --project CrateApi.Data/CrateApi.Data.csproj --startup-project CrateApi/CrateApi.csproj
```

================
File: CrateCS.sln
================
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CrateApi", "CrateApi\CrateApi.csproj", "{0B249DF4-CEA9-4774-A447-1C1C171DF5DA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CrateApi.Common", "CrateApi.Common\CrateApi.Common.csproj", "{14A045EB-DB8A-405A-9795-70B59FCE698A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnfurlApi", "UnfurlApi\UnfurlApi.csproj", "{8968599B-5D1F-4896-BF8D-A050F2266256}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnfurlApi.Services", "UnfurlApi.Services\UnfurlApi.Services.csproj", "{D155D4B7-7F39-4D2F-9080-E9D547C03565}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CrateApi.Data", "CrateApi.Data\CrateApi.Data.csproj", "{96AB5D10-683B-4015-8551-1B7DFB86D956}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CrateApi.Services", "CrateApi.Services\CrateApi.Services.csproj", "{5E56DB76-BAEF-4C23-BCFC-BEB097714FCF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0B249DF4-CEA9-4774-A447-1C1C171DF5DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B249DF4-CEA9-4774-A447-1C1C171DF5DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B249DF4-CEA9-4774-A447-1C1C171DF5DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B249DF4-CEA9-4774-A447-1C1C171DF5DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{14A045EB-DB8A-405A-9795-70B59FCE698A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{14A045EB-DB8A-405A-9795-70B59FCE698A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{14A045EB-DB8A-405A-9795-70B59FCE698A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{14A045EB-DB8A-405A-9795-70B59FCE698A}.Release|Any CPU.Build.0 = Release|Any CPU
		{8968599B-5D1F-4896-BF8D-A050F2266256}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8968599B-5D1F-4896-BF8D-A050F2266256}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8968599B-5D1F-4896-BF8D-A050F2266256}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8968599B-5D1F-4896-BF8D-A050F2266256}.Release|Any CPU.Build.0 = Release|Any CPU
		{D155D4B7-7F39-4D2F-9080-E9D547C03565}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D155D4B7-7F39-4D2F-9080-E9D547C03565}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D155D4B7-7F39-4D2F-9080-E9D547C03565}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D155D4B7-7F39-4D2F-9080-E9D547C03565}.Release|Any CPU.Build.0 = Release|Any CPU
		{96AB5D10-683B-4015-8551-1B7DFB86D956}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{96AB5D10-683B-4015-8551-1B7DFB86D956}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{96AB5D10-683B-4015-8551-1B7DFB86D956}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{96AB5D10-683B-4015-8551-1B7DFB86D956}.Release|Any CPU.Build.0 = Release|Any CPU
		{5E56DB76-BAEF-4C23-BCFC-BEB097714FCF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5E56DB76-BAEF-4C23-BCFC-BEB097714FCF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5E56DB76-BAEF-4C23-BCFC-BEB097714FCF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5E56DB76-BAEF-4C23-BCFC-BEB097714FCF}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal

================
File: docker-compose.yml
================
services:
  crateapi:
    build:
      context: .
      dockerfile: CrateApi/Dockerfile
    ports:
      - 8000:8000
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8000
      # - ConnectionStrings__DefaultConnection=Host=db;Database=cratedb;Username=postgres;Password=yourpassword
      - ConnectionStrings__DefaultConnection=Data Source=/data/cratedb.db
      - UnfurlServiceUrl=http://unfurlapi
    depends_on:
      - migrations
      - unfurlapi
    volumes:
      - ./data:/data

  unfurlapi:
    build:
      context: .
      dockerfile: UnfurlApi/Dockerfile
    ports:
      - "8001:8001"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8001

  migrations:
    build:
      context: .
      dockerfile: CrateApi/Dockerfile
    # entrypoint: ["dotnet", "ef", "database", "update", "--assembly", "CrateApi.dll"]
    entrypoint: [ "sh", "-c", "./efbundle --connection 'Data Source=/data/cratedb.db'" ]
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      # - ConnectionStrings__DefaultConnection=Host=db;Database=cratedb;Username=postgres;Password=yourpassword
      - ConnectionStrings__DefaultConnection=Data Source=/data/cratedb.db
    restart: "no" # Run once and exit
    volumes:
      - ./data:/data

================
File: Makefile
================
build:
	dotnet build
clean:
	dotnet clean
restore:
	dotnet restore
watch:
	dotnet watch --project ./CrateApi/CrateApi.csproj run
run:
	dotnet run --project ./CrateApi/CrateApi.csproj
run2:
	dotnet run --project ./UnfurlApi/UnfurlApi.csproj
dbu:
	dotnet ef database update --project ./CrateApi/
dba:
	dotnet ef migrations add $(name) --project CrateApi.Data --startup-project CrateApi

================
File: README.md
================
# NETCrateAPI

CrateNFC API Implementation in C# using .NET 8.

## Prerequisites

Before running the project, ensure you have the necessary tools installed.

### Install .NET 8 SDK

The application requires the [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0) to build and run.

#### macOS Installation (Recommended via Homebrew)

Install [.NET 8 using Homebrew on macOS](<https://formulae.brew.sh/formula/dotnet@8>).

```sh
brew install dotnet@8
echo 'export PATH="/opt/homebrew/opt/dotnet@8/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
dotnet --version

# Expected output:
# 8.0.112
```

You may need to update `DOTNET_ROOT` in your shell profile to point to the correct .NET SDK path.

```sh
dotnet --info
# Environment variables:
#   DOTNET_ROOT       [/opt/homebrew/Cellar/dotnet@8/8.0.12/libexec]
```

### Install Entity Framework Core CLI Tool

Install dotnet-ef tool to work with database models and migrations.

```sh
dotnet tool install --global dotnet-ef
```

More details: [Entity Framework Core CLI](https://learn.microsoft.com/en-us/ef/core/cli/dotnet)

## Clone and Set Up the Project

### Clone the Repository

```sh
<NAME_EMAIL>:lilrobo/CrateNFCAPI.git>
cd CrateNFCAPI
```

### Set Up the Local SQLite Database

Create the database directory and local sqlite database file

```sh
mkdir -p CrateApi.Data/Data
touch CrateApi.Data/Data/cratedb.db
```

### Apply Database Migrations

Run database migrations to create the necessary tables in the SQLite database.

```sh
cd ./CrateApi.Data
dotnet-ef database update --startup-project ../CrateApi
```

## Run the Application

### Start the API Server and Unfurl Api Server


```sh
make run
make run2 #seperate terminal window
```

The main server will be accessible at at [http://localhost:8000](http://localhost:8000).

## Verify API Endpoints

### Open Swagger UI

Explore API documentation and test endpoints using Swagger UI.

* [http://localhost:8000/swagger](http://localhost:8000/swagger)

## Testing with Sample Data

### Default API Credentials

```json
{
  "email": "<EMAIL>",
  "password": "123"
}
```

## Additional Notes

* If testing with the **Crate NFC** app, set the server to `http://localhost:8000` in the app settings.

* Refer to [.NET Documentation](https://learn.microsoft.com/en-us/dotnet/) for further reading.



================================================================
End of Codebase
================================================================
