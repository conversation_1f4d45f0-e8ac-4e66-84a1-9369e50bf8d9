using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HtmlAgilityPack;
using static LanguageExt.Prelude;
using LanguageExt.Traits;
using LanguageExt;
using UnfurlApi.Services.Clients;
using Microsoft.Extensions.Caching.Memory;

namespace UnfurlApi.Services.Runtimes;

public sealed record UnfurlRuntimeEnv(HtmlWeb HtmlWeb, IMemoryCache MemoryCache, IGenericClient GenericClient, IYoutubeClient YoutubeClient, IAppleClient AppleClient);
public sealed record UnfurlRuntime(UnfurlRuntimeEnv Env) :
    Has<Eff<UnfurlRuntime>, HtmlWeb>,
    Has<Eff<UnfurlRuntime>, IMemoryCache>,
    Has<Eff<UnfurlRuntime>, IYoutubeClient>,
    Has<Eff<UnfurlRuntime>, IGenericClient>,
    Has<Eff<UnfurlRuntime>, IAppleClient>
{
    static K<Eff<UnfurlRuntime>, HtmlWeb> Has<Eff<UnfurlRuntime>, HtmlWeb>.Ask =>
        liftEff<UnfurlRuntime, HtmlWeb>(e => e.Env.HtmlWeb);
    static K<Eff<UnfurlRuntime>, IGenericClient> Has<Eff<UnfurlRuntime>, IGenericClient>.Ask =>
        liftEff<UnfurlRuntime, IGenericClient>(e => e.Env.GenericClient);
    static K<Eff<UnfurlRuntime>, IYoutubeClient> Has<Eff<UnfurlRuntime>, IYoutubeClient>.Ask =>
        liftEff<UnfurlRuntime, IYoutubeClient>(e => e.Env.YoutubeClient);
    static K<Eff<UnfurlRuntime>, IMemoryCache> Has<Eff<UnfurlRuntime>, IMemoryCache>.Ask =>
        liftEff<UnfurlRuntime, IMemoryCache>(e => e.Env.MemoryCache);
    static K<Eff<UnfurlRuntime>, IAppleClient> Has<Eff<UnfurlRuntime>, IAppleClient>.Ask =>
        liftEff<UnfurlRuntime, IAppleClient>(e => e.Env.AppleClient);

    //public static UnfurlRuntime Create(HtmlWeb htmlWeb, IMemoryCache memoryCache, IGenericClient genericClient, IYoutubeClient youtubeClient, IAppleClient appleClient) =>
    //    new UnfurlRuntime(new UnfurlRuntimeEnv(htmlWeb, memoryCache, genericClient, youtubeClient, appleClient));
}
