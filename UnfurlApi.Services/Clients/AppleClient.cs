using System.Text;
using LanguageExt;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace UnfurlApi.Services.Clients;

public interface IAppleClient : IClient
{
    Task<string> GetStringAsync(string endpoint, CancellationToken cancellationToken = default);
}

public class AppleClient : IAppleClient
{
    private const string BaseUrl = "https://music.apple.com";
    public const string ClientName = "AppleClient";
    private readonly HttpClient client;

    private AppleClient(HttpClient client)
    {
        this.client = client;
    }

    public static AppleClient Create(IHttpClientFactory hcf)
    {
        var client = hcf.CreateClient(ClientName);
        client.DefaultRequestHeaders.Add(
            "User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        );
        return new AppleClient(client);
    }

    public async Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default)
    {
        var response = await client.GetAsync($"{endpoint}", cancellationToken);
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(content);
    }
    public async Task<string> GetStringAsync(string endpoint, CancellationToken cancellationToken = default)
    {
        var response = await client.GetAsync($"{endpoint}", cancellationToken);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadAsStringAsync(cancellationToken);
    }
    public async Task<T?> PostAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );

        var response = await client.PostAsync($"{endpoint}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(responseContent);
    }

    public async Task<T?> PutAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );
        var response = await client.PutAsync($"{endpoint}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(responseContent);
    }

    private static StringContent CreateRequest(object data) =>
        new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");
}
