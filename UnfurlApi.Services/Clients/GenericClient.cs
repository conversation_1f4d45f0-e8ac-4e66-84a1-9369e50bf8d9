using HtmlAgilityPack;

namespace UnfurlApi.Services.Clients;

public interface IGenericClient
{
    Task<HtmlDocument> GetAsync(string endpoint, CancellationToken cancellationToken = default);
}

public class GenericClient(HttpClient client) : IGenericClient
{
    public const string ClientName = "GenericClient";

    public static GenericClient Create(IHttpClientFactory hcf)
    {
        var client = hcf.CreateClient(ClientName);
        client.DefaultRequestHeaders.Add(
            "User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        );
        client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
        client.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9");
        //client.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
        client.DefaultRequestHeaders.Add("Connection", "keep-alive");
        client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");

        // Make it look like a browser with no JavaScript
        client.DefaultRequestHeaders.Add("DNT", "1"); // Do Not Track
        client.DefaultRequestHeaders.Add("Cache-Control", "max-age=0");

        // Slow connection indicators
        client.DefaultRequestHeaders.Add("Save-Data", "on"); // Signal preference for reduced data usage

        // Accessibility-related header (sometimes set for screen readers)
        client.DefaultRequestHeaders.Add("X-Purpose", "preview");

        // Add referrer to appear more legitimate
        client.DefaultRequestHeaders.Add("Referer", "https://www.google.com/");
        return new GenericClient(client);
    }

    public async Task<HtmlDocument> GetAsync(string url, CancellationToken cancellationToken = default)
    {
        var response = await client.GetAsync($"{url}", cancellationToken);
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync(cancellationToken);

        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(content);
        return htmlDoc;
    }
}
