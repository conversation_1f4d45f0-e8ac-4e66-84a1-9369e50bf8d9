using System.Text;
using HtmlAgilityPack;
using LanguageExt;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace UnfurlApi.Services.Clients;

public interface IClient
{
    Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default);
    Task<T?> PostAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default);
    Task<T?> PutAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default);
}

public interface ISpotifyClient : IClient
{
    //Task ExtractSpotifyKeys();
}

public class SpotifyClient : ISpotifyClient
{
    public const string ClientName = "SpotifyClient";
    private readonly HttpClient client;
    private const string BaseUrl = "https://api.spotify.com/v1";
    private const string SpotifyUrl = "https://open.spotify.com/search";

    private SpotifyClient(HttpClient client)
    {
        this.client = client;
    }
    public static SpotifyClient Create(IHttpClientFactory hcf)
    {
        var client = hcf.CreateClient(ClientName);
        return new SpotifyClient(client);
    }

    public async Task ExtractSpotifyKeys()
    {
        var response = await client.GetAsync("https://open.spotify.com/track/2TmqHjg7uhizGndzXQdFuf");
        response.EnsureSuccessStatusCode();
        var htmlContent = await response.Content.ReadAsStringAsync();
        var htmlDoc = new HtmlDocument();
        htmlDoc.LoadHtml(htmlContent);
        var scriptTag = htmlDoc.DocumentNode.SelectSingleNode("//script[@id='session']");
        if (scriptTag != null)
        {
            var sessionData = JObject.Parse(scriptTag.InnerText);
            var accessToken = sessionData.GetValue("accessToken")?.ToString() ?? "";
            if (!client.DefaultRequestHeaders.Contains("Authorization"))
            {
                client.DefaultRequestHeaders.Add("Authorization", $"Bearer {accessToken}");
            }
            return;
        }
        throw new ArgumentException("Failed to set access token.");
    }

    public async Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default)
    {
        var response = await client.GetAsync($"{BaseUrl}{endpoint}", cancellationToken);
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        if (typeof(T) == typeof(JObject))
        {
            return (T)(object)JObject.Parse(content);
        }
        return JsonConvert.DeserializeObject<T>(content);
    }

    public async Task<T?> PostAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );

        var response = await client.PostAsync($"{BaseUrl}{endpoint}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(responseContent);
    }

    public async Task<T?> PutAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );
        var response = await client.PutAsync($"{BaseUrl}{endpoint}", content, cancellationToken);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<T>(responseContent);
    }

    private static StringContent CreateRequest(object data) =>
        new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");
}
