using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using HtmlAgilityPack;
using LanguageExt;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using static LanguageExt.Prelude;

namespace UnfurlApi.Services.Clients;

public interface IYoutubeClient 
{
    Task<Option<T>> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default);
    Task<Option<T>> PostAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default);
    Task<Option<T>> PutAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default);
}

public class YoutubeClient(HttpClient client) : IYoutubeClient
{
    public const string ClientName = "YoutubeClient";
    public static YoutubeClient Create(IHttpClientFactory hcf)
    {
        var client = hcf.CreateClient(ClientName);
        
        // Clear default headers and set more browser-like headers to avoid detection
        client.DefaultRequestHeaders.Clear();
        
        // Set common browser headers with randomized elements to avoid fingerprinting
        string[] userAgents = new[]
        {
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        };
        
        client.DefaultRequestHeaders.Add("User-Agent", userAgents[0]);
        client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8");
        client.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9");
        //client.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
        client.DefaultRequestHeaders.Add("Connection", "keep-alive");
        client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
        client.DefaultRequestHeaders.Add("Sec-Fetch-Dest", "document");
        client.DefaultRequestHeaders.Add("Sec-Fetch-Mode", "navigate");
        client.DefaultRequestHeaders.Add("Sec-Fetch-Site", "none");
        client.DefaultRequestHeaders.Add("Sec-Fetch-User", "?1");
        client.DefaultRequestHeaders.Add("DNT", "1");
        
        // YouTube Music specific headers
        client.DefaultRequestHeaders.Add("X-YouTube-Client-Name", "67");
        client.DefaultRequestHeaders.Add("X-YouTube-Client-Version", "1.20240301.00.00");
        
        return new YoutubeClient(client);
    }

    public async Task<Option<T>> GetAsync<T>(string url, CancellationToken cancellationToken = default)
    {
        var response = await client.GetAsync(url, cancellationToken);

        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadFromJsonAsync<T>(cancellationToken);

        if (content is null)
        {
            return Option<T>.None;
        }

        return Some(content);
    }

    public async Task<Option<T>> PostAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );

        var response = await client.PostAsync(endpoint, content, cancellationToken);

        response.EnsureSuccessStatusCode();

        var responseContent = await response.Content.ReadFromJsonAsync<T>(cancellationToken);

        if (responseContent is null)
        {
            return Option<T>.None;
        }

        return Some(responseContent);
    }
    

    public async Task<Option<T>> PutAsync<T>(string endpoint, Option<object> data = default, CancellationToken cancellationToken = default)
    {
        var content = data.Match(
            Some: value => CreateRequest(value),
            None: () => null!
        );

        var response = await client.PutAsync(endpoint, content, cancellationToken);

        response.EnsureSuccessStatusCode();

        var responseContent = await response.Content.ReadFromJsonAsync<T>(cancellationToken);

        if (responseContent is null)
        {
            return Option<T>.None;
        }

        return Some(responseContent);
    }

    private static StringContent CreateRequest(object data) =>
        new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");
}
