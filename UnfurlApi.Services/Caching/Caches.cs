using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HtmlAgilityPack;
using LanguageExt;
using static LanguageExt.Prelude;
using LanguageExt.Traits;
using Microsoft.Extensions.Caching.Memory;
using System.Runtime.CompilerServices;
using UnfurlApi.Services.Runtimes;

namespace UnfurlApi.Services.Caching;

public static partial class Caches
{
    public static string Key(string url) => $"UnfurledContent_{url}";
}

public static partial class Caches<M, RT> 
    where RT :
        Has<M, IMemoryCache>
    where M :
        Monad<M>,
        Fallible<M>
{
    private static readonly MemoryCacheEntryOptions cacheOptions = new MemoryCacheEntryOptions()
        .SetSlidingExpiration(TimeSpan.FromHours(1))
        .SetAbsoluteExpiration(TimeSpan.FromDays(1))
        .SetSize(1);

    public static K<M, T> GetOrAdd<T>(string cacheKey, K<M, T> getItemFunc) =>
        from cache in Has<M, RT, IMemoryCache>.ask
        from ff    in getItemFunc.ToIO()
        from zz    in IO.liftAsync(() => cache.GetOrCreateAsync(cacheKey, async _ => await ff.RunAsync(), cacheOptions))
        select zz;
}
