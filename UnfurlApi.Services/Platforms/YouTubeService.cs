using System;
using System.Text.RegularExpressions;
using CrateApi.Common;
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Exceptions;
using LanguageExt;
using LanguageExt.Traits;
using Microsoft.Extensions.Caching.Memory;
using UnfurlApi.Services.Caching;
using UnfurlApi.Services.Clients;
using static LanguageExt.Prelude;

namespace UnfurlApi.Services.Platforms;

public static class YouTubeService<M, RT>
    where RT :
        Has<M, IYoutubeClient>,
        Has<M, IMemoryCache>
    where M :
        Monad<M>,
        Fallible<M>
{
    private const string OEmbedUrl = "https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v={0}&format=json";

    private static readonly Regex VideoIdRegex = new(
        @"(watch\?v=|youtu\.be\/|music\.youtube\.com\/watch\?v=)([^&\s]+)",
        RegexOptions.Compiled | RegexOptions.IgnoreCase
    );
    public static K<M, ContentUnfurled> Fetch(string url) =>
        from id         in ExtractVideoId(url)
        from item       in TryGetTrackViaOEmbed(id, url)
        select item;

    private static K<M, string> ExtractVideoId(string url) =>
        from match in M.Pure<Match>(VideoIdRegex.Match(url))
        from _     in guard(match.Success && match.Groups.Count > 2, () => throw new ScraperException($"[UNFURL] Could not extract video ID from URL: {url}"))
        select match.Groups[2].Value;

    private static K<M, ContentUnfurled> TryGetTrackViaOEmbed(string videoId, string url) =>
        from client     in Has<M, RT, IYoutubeClient>.ask
        from oembedUrl  in M.Pure(string.Format(OEmbedUrl, videoId))
        from response   in IO.liftAsync(() => client.GetAsync<YouTubeVideo>(oembedUrl))
        from _          in guard(response.IsSome, () => throw new ScraperException("[UNFURL] Failed to unfurl youtube video"))
        select response.Match(
            Some: v  => new ContentUnfurled(v.Title, v.AuthorName, v.ThumbnailUrl ?? string.Empty, url, (int)UnfurledContentType.YoutubeVideo),
            None: () => new ContentUnfurled("", "", "", url, (int)UnfurledContentType.YoutubeVideo)
        );
}
