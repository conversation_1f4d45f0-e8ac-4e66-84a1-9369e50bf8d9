using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Common;
using CrateApi.Common.Dto.Request;
using CrateApi.Services.Database;
using CrateApi.Services.DomainTypes;
using CrateApi.Services.Runtimes;
using LanguageExt;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace CrateApi.Services.Test.DomainTypeTests;

public class UrlTests
{
    [Test]
    public void TestValidSpotifyUrl()
    {
        var result = DomainUnfurlUrl.From("https://open.spotify.com/bladeeTrack123");
        Assert.That(result.IsSucc);
        var url = result.ThrowIfFail();
        Assert.That(url.UrlType == UnfurledContentType.Spotify);
        Assert.That(url.Endpoint == "spotify");
    }

    [Test]
    public void TestValidAppleUrl()
    {
        var result = DomainUnfurlUrl.From("https://music.apple.com/blahblah");
        Assert.That(result.IsSucc);
        var url = result.ThrowIfFail();
        Assert.That(url.UrlType == UnfurledContentType.AppleMusic);
        Assert.That(url.Endpoint == "apple");
    }

    [Test]
    public void TestValidYoutubeUrl()
    {
        var result = DomainUnfurlUrl.From("https://youtube.com/ajdoiawd");
        Assert.That(result.IsSucc);
        var url = result.ThrowIfFail();
        Assert.That(url.UrlType == UnfurledContentType.YoutubeVideo);
        Assert.That(url.Endpoint == "youtube");
    }

    [Test]
    public void TestValidUnsupportedUrl()
    {
        var result = DomainUnfurlUrl.From("https://www.google.com/ajdoiawd");
        Assert.That(result.IsSucc);
        var url = result.ThrowIfFail();
        Assert.That(url.UrlType == UnfurledContentType.Unsupported);
        Assert.That(url.Endpoint == "unsupported");
    }
    [Test]
    public void TestUrlWithNoProtocol()
    {
        var result = DomainUnfurlUrl.From("youtube.com/ajdoiawd");
        Assert.That(result.IsSucc);
        var url = result.ThrowIfFail();
        Assert.That(url.UrlType == UnfurledContentType.YoutubeVideo);
        Assert.That(url.Endpoint == "youtube");
        Assert.That(url.Url == "http://youtube.com/ajdoiawd");
    }
}
