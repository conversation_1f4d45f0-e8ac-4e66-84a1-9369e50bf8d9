using CrateApi.Common;
using CrateApi.Data;
using CrateApi.Services.DomainTypes;
using LanguageExt.Traits;
using LanguageExt;
using Microsoft.EntityFrameworkCore;
using Moq;
using Microsoft.Extensions.Logging;
using static LanguageExt.Prelude;
using CrateApi.Data.Models;
using CrateApi.Services.Database;
using CrateApi.Services.Runtimes;
using CrateApi.Services.Logic;
using CrateApi.Common.Dto.Request;
using System.Net.Http;
using Moq.Protected;
using System.Net;
using System.Text.Json;
using CrateApi.Services.Test.Helpers;
namespace CrateApi.Services.Test.UnfurlClient;


public class PostJsonTests
{
    private sealed record MockRuntime :
        Has<Eff<MockRuntime>, IHttpClientFactory>,
        Has<Eff<MockRuntime>, ILogger<ApiRuntime>>
    {
        private sealed record MockRuntimeEnv(IHttpClientFactory clientFactory, ILogger<ApiRuntime> logger);
        private MockRuntimeEnv Env { get; }
        private MockRuntime(MockRuntimeEnv env) => Env = env;
        public static K<Eff<MockRuntime>, IHttpClientFactory> Ask =>
            liftEff<MockRuntime, IHttpClientFactory>(e => e.Env.clientFactory);

        static K<Eff<MockRuntime>, ILogger<ApiRuntime>> Has<Eff<MockRuntime>, ILogger<ApiRuntime>>.Ask =>
            liftEff<MockRuntime, ILogger<ApiRuntime>>(e => e.Env.logger);

        public static MockRuntime Create(IHttpClientFactory factory, ILogger<ApiRuntime> logger) =>
            new MockRuntime(new MockRuntimeEnv(factory,logger));
    }

    private ILogger<ApiRuntime> mockLogger;

    [SetUp]
    public void Setup()
    {
        mockLogger = Mock.Of<ILogger<ApiRuntime>>(MockBehavior.Loose);
    }
    [Test]
    public async Task UnfurlTrackSpotify()
    {
        // Setup
        var track = new ContentUnfurled("Song", "Artist", "Url", "Url2", (int)UnfurledContentType.Spotify);
        var unfurlUrl = "https://testUnfurlUrl.com";
        var unfurlRequest = new UnfurlUrlRequestDto("http://testUrl.com");

        var factory = HttpMockHelper.CreateMockJsonResponseFactoryForUri
        (
            e => e.RequestUri == new Uri(unfurlUrl),
            track
        );
    
        var runtime = MockRuntime.Create(factory.Object, mockLogger);
        // End setup

        var r = await UnfurlClient<Eff<MockRuntime>, MockRuntime>.PostJson(unfurlRequest, UnfurledContentType.Spotify, unfurlUrl).RunAsync(runtime);

        Assert.IsTrue(r.IsSucc);
        var result = r.ThrowIfFail();
        Assert.That(result.IsSome);
        result.IfSome(t => {
            Assert.That(t is ContentUnfurled);
            var track = (t as ContentUnfurled)!;
            Assert.That(track.Title, Is.EqualTo("Song"));
            Assert.That(track.Detail, Is.EqualTo("Artist"));
            Assert.That(track.MediaUrl, Is.EqualTo("Url"));
        });
    }
    [Test]
    public async Task UnfurlTrackApple()
    {
        // Setup
        var track = new ContentUnfurled("Song", "Artist", "Url", "Url2", (int)UnfurledContentType.AppleMusic);
        var unfurlUrl = "https://testUnfurlUrl.com";
        var unfurlRequest = new UnfurlUrlRequestDto("http://testUrl.com");

        var factory = HttpMockHelper.CreateMockJsonResponseFactoryForUri
        (
            e => e.RequestUri == new Uri(unfurlUrl),
            track
        );
    
        var runtime = MockRuntime.Create(factory.Object, mockLogger);
        // End setup

        var r = await UnfurlClient<Eff<MockRuntime>, MockRuntime>.PostJson(unfurlRequest, UnfurledContentType.AppleMusic, unfurlUrl).RunAsync(runtime);

        Assert.IsTrue(r.IsSucc);
        var result = r.ThrowIfFail();
        Assert.That(result.IsSome);
        result.IfSome(t => {
            Assert.That(t is ContentUnfurled);
            var track = (t as ContentUnfurled)!;
            Assert.That(track.Title, Is.EqualTo("Song"));
            Assert.That(track.Detail, Is.EqualTo("Artist"));
            Assert.That(track.MediaUrl, Is.EqualTo("Url"));
        });
    }
    [Test]
    public async Task UnfurlTrackYoutube()
    {
        // Setup
        var track = new ContentUnfurled("Song", "Artist", "MediaUrl", "Url", (int)UnfurledContentType.Spotify);
        var unfurlUrl = "https://testUnfurlUrl.com";
        var unfurlRequest = new UnfurlUrlRequestDto("http://testUrl.com");

        var factory = HttpMockHelper.CreateMockJsonResponseFactoryForUri
        (
            e => e.RequestUri == new Uri(unfurlUrl),
            track
        );
    
        var runtime = MockRuntime.Create(factory.Object, mockLogger);
        // End setup

        var r = await UnfurlClient<Eff<MockRuntime>, MockRuntime>.PostJson(unfurlRequest, UnfurledContentType.YoutubeVideo, unfurlUrl).RunAsync(runtime);

        Assert.IsTrue(r.IsSucc);
        var result = r.ThrowIfFail();
        Assert.That(result.IsSome);
        result.IfSome(t => {
            Assert.That(t is ContentUnfurled);
            var track = (t as ContentUnfurled)!;
            Assert.That(track.Title, Is.EqualTo("Song"));
            Assert.That(track.Detail, Is.EqualTo("Artist"));
            Assert.That(track.MediaUrl, Is.EqualTo("MediaUrl"));
            Assert.That(track.Url, Is.EqualTo("Url"));
        });
    }
}
