using LanguageExt;
using static LanguageExt.Prelude;
using LanguageExt.Traits;
using Microsoft.Extensions.Logging;
using Moq;
using CrateApi.Common.Dto.Request;
using CrateApi.Common;
using CrateApi.Services.Logic;
using CrateApi.Services.Test.Helpers;
using CrateApi.Data;
using CrateApi.Services.DomainTypes;
using CrateApi.Services.Runtimes;
using Microsoft.EntityFrameworkCore;

namespace CrateApi.Services.Test.UnfurlService;

public class UnfurlTests
{
    private ILogger mockLogger;
    private CrateDbContext dbContext;

    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<CrateDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        dbContext = new CrateDbContext(options);
        mockLogger = Mock.Of<ILogger>(MockBehavior.Loose);
    }

    [Test]
    public async Task UnfurlAnonymousTest()
    {
        // Setup

        //-- UserManager
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId);
        //--

        //-- Service Settings
        var serviceSettings = new ServiceSettings("https://test.com");
        //--

        //-- DomainUnfurlUrl
        var domainUnfurl = DomainUnfurlUrl.From("https://open.spotify.com/bladeeTrack123").ThrowIfFail();
        //--

        //-- Set up spotify track as response from anonymous url
        var spotifyTrack = new ContentUnfurled("Song", "Artist", "Url", "Url2", (int)UnfurledContentType.Spotify);
        var requestUrl = $"{serviceSettings.UnfurlServiceUrl}/api/v1/unfurl/{domainUnfurl.Endpoint}";
        var unfurlRequest = new UnfurlUrlRequestDto("http://testUrl.com");
        var factory = HttpMockHelper.CreateMockJsonResponseFactoryForUri
        (
            e => e.RequestUri == new Uri(requestUrl),
            spotifyTrack
        );
        //--

        //--Runtime
        var runtime = RuntimeHelper.CreateUnfurlRuntime(dbContext, userManager.Object, factory.Object, serviceSettings);
        //-- 
        
        // End setup

        var service = UnfurlService<Eff<ApiRuntime>, ApiRuntime>.Create(domainUnfurl);
        var r = await service.UnfurlAnonymous(unfurlRequest).RunAsync(runtime);
        Assert.That(r.IsSucc);
        var result = r.ThrowIfFail();
        result.IfSome(t => {
            Assert.That(t.Url, Is.EqualTo("Url2"));
            Assert.That(t.Title, Is.EqualTo("Song"));
            Assert.That(t.Detail, Is.EqualTo("Artist"));
            Assert.That(t.MediaUrl, Is.EqualTo("Url"));
        });

        // Assert that during an anonymous unfurl tracks were NOT added to the db
        var databaseResults = await dbContext.Tracks.ToListAsync();
        Assert.That(databaseResults.Count == 0, "Track was added during anoynmous unfurl. Please fix");
    }

    [Test]
    public async Task UnfurlSignedInTest()
    {
        // Setup

        //-- UserManager
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId);
        //--

        //-- Service Settings
        var serviceSettings = new ServiceSettings("https://test.com");
        //--

        //-- DomainUnfurlUrl
        var domainUnfurl = DomainUnfurlUrl.From("https://open.spotify.com/bladeeTrack123").ThrowIfFail();
        //--

        //-- Set up spotify track as response from anonymous url
        var spotifyTrack = new ContentUnfurled("Song", "Artist", "Url", "Url2", (int)UnfurledContentType.Spotify);
        var requestUrl = $"{serviceSettings.UnfurlServiceUrl}/api/v1/unfurl/{domainUnfurl.Endpoint}";
        var unfurlRequest = new UnfurlUrlRequestDto("http://testUrl.com");
        var factory = HttpMockHelper.CreateMockJsonResponseFactoryForUri
        (
            e => e.RequestUri == new Uri(requestUrl),
            spotifyTrack
        );
        //--

        //--Runtime
        var runtime = RuntimeHelper.CreateUnfurlRuntime(dbContext, userManager.Object, factory.Object, serviceSettings);
        //-- 
        
        // End setup
        var service = UnfurlService<Eff<ApiRuntime>, ApiRuntime>.Create(domainUnfurl);
        var r = await service.Unfurl(unfurlRequest).RunAsync(runtime);
        Assert.That(r.IsSucc);
        var result = r.ThrowIfFail();
        Assert.That(result.IsSome);
        result.IfSome(t => {
            Assert.That(t.Url, Is.EqualTo("Url2"));
            Assert.That(t.Title, Is.EqualTo("Song"));
            Assert.That(t.Detail, Is.EqualTo("Artist"));
            Assert.That(t.MediaUrl, Is.EqualTo("Url"));
        });

        //Finally check the EFCore database to make sure everything is correctly set up
        var databaseResults = await dbContext.Tracks.ToListAsync();
        Assert.That(databaseResults.Count == 1);
        var first = databaseResults.FirstOrDefault()!;
        Assert.That(first.ArtistName == "Artist");
        Assert.That(first.UserId == userId);
    }

    [TearDown]
    public void TearDown()
    {
        dbContext?.Dispose();
    }
}
