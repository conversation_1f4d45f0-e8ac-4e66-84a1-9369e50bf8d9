using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Data.Models;
using CrateApi.Data;
using CrateApi.Services.Database;
using CrateApi.Services.Runtimes;
using LanguageExt;
using Microsoft.EntityFrameworkCore;
using Moq;
using Microsoft.Extensions.Logging;
using CrateApi.Common.Dto.Request;
using CrateApi.Services.Test.Helpers;

namespace CrateApi.Services.Test.CollectionService;
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
public class AddCollectionTests
{

    private CrateDbContext dbContext;


    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<CrateDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        dbContext = new CrateDbContext(options);
    }

    [Test]
    public async Task AddCollection()
    {
        //setup
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var runtime = RuntimeHelper.CreateRuntime(dbContext, userManager);

        var createdDate = DateTime.Now;
        var updatedDate = DateTime.Now;
        var t_1 = Items.CreateTrack(null, userId, "Track1", "Artist1", "someImage.Png", "url", "domainUrl", createdDate, updatedDate);
        var t_2 = Items.CreateTrack(null, userId, "Track2", "Artist2", "someImage.Png", "url", "domainUrl", createdDate, updatedDate);
        var e_1 = dbContext.Tracks.Add(t_1);
        var e_2 = dbContext.Tracks.Add(t_2);
        var c_1 = dbContext.Collections.Add(new Collection { Name = "Test", Thumbnail = "Test" , UserId = userId });
        await dbContext.SaveChangesAsync();
        //end setup
        

        var r = await CollectionService<Eff<ApiRuntime>, ApiRuntime>.Add(new AddCollectionRequestDto("Collection", "thumb.png")).RunAsync(runtime);

        Assert.IsTrue(r.IsSucc);
        var result = r.ThrowIfFail();
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Contents.Count, Is.EqualTo(0));

        var collection = await dbContext.Collections.FirstOrDefaultAsync(e => e.Id == result.Id);
        Assert.That(collection!.UserId! == userId);
    }
        [TearDown]
    public void TearDown()
    {
        dbContext?.Dispose();
    }
}
