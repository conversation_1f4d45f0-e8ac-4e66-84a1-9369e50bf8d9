using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Data.Models;
using CrateApi.Data;
using CrateApi.Services.Database;
using CrateApi.Services.Runtimes;
using LanguageExt;
using Microsoft.EntityFrameworkCore;
using Moq;
using Microsoft.Extensions.Logging;
using CrateApi.Services.Test.Helpers;

namespace CrateApi.Services.Test.CollectionService;

public class GetCollectionsTests
{
    private CrateDbContext dbContext;

    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<CrateDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        dbContext = new CrateDbContext(options);
    }

    [Test]
    public async Task ReturnsExpectedCollections()
    {
        //setup
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var otherUserId = Guid.Parse("00000000-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var runtime = RuntimeHelper.CreateRuntime(dbContext, userManager);

        var createdDate = DateTime.Now;
        var updatedDate = DateTime.Now;
        dbContext.Collections.Add(Items.CreateCollection("Col1", "test.png", userId, createdDate, updatedDate));
        dbContext.Collections.Add(Items.CreateCollection("Col2", "test.png", userId, createdDate, updatedDate));
        dbContext.Collections.Add(Items.CreateCollection("Col3", "test.png", otherUserId, createdDate, updatedDate));
        dbContext.Collections.Add(Items.CreateCollection("Col4", "test.png", otherUserId, createdDate, updatedDate));
        dbContext.Collections.Add(Items.CreateCollection("Col5", "test.png", otherUserId, createdDate, updatedDate));
        dbContext.Collections.Add(Items.CreateCollection("Col6", "test.png", otherUserId, createdDate, updatedDate));
        dbContext.Collections.Add(Items.CreateCollection("Col7", "test.png", otherUserId, createdDate, updatedDate));
        await dbContext.SaveChangesAsync();
        //end setup
        

        var r = await CollectionService<Eff<ApiRuntime>, ApiRuntime>.Paged(0, 5).RunAsync(runtime);
        Assert.IsTrue(r.IsSucc);
        var result = r.ThrowIfFail();
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count == 2);

        var collections = await dbContext.Collections.ToListAsync();
        Assert.That(collections.Count, Is.EqualTo(7)); // Check db also to be correct
    }

    [Test]
    public async Task ReturnsExpectedCollections_OrderedDescending()
    {
        //setup
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var otherUserId = Guid.Parse("00000000-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var runtime = RuntimeHelper.CreateRuntime(dbContext, userManager);

        var createdDate = DateTime.Now;
        var updatedDate = DateTime.Now;
        dbContext.Collections.Add(Items.CreateCollection("Col1", "test.png", userId, createdDate, updatedDate.AddDays(-4)));
        dbContext.Collections.Add(Items.CreateCollection("Col2", "test.png", userId, createdDate, updatedDate.AddDays(-3)));
        dbContext.Collections.Add(Items.CreateCollection("Col3", "test.png", userId, createdDate, updatedDate.AddDays(-2)));
        dbContext.Collections.Add(Items.CreateCollection("Col4", "test.png", userId, createdDate, updatedDate.AddDays(-1)));
        dbContext.Collections.Add(Items.CreateCollection("Col5", "test.png", userId, createdDate, updatedDate.AddDays(-5)));
        dbContext.Collections.Add(Items.CreateCollection("Col6", "test.png", userId, createdDate, updatedDate.AddDays(-6)));
        dbContext.Collections.Add(Items.CreateCollection("Col7", "test.png", userId, createdDate, updatedDate.AddDays(-2)));
        await dbContext.SaveChangesAsync();
        //end setup
        

        var r = await CollectionService<Eff<ApiRuntime>, ApiRuntime>.Paged(0, 5).RunAsync(runtime);
        Assert.IsTrue(r.IsSucc);
        var result = r.ThrowIfFail();
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count == 5);
        Assert.That(result[0].Name == "Col4");
        Assert.That(result.Last().Name == "Col1");
        var collections = await dbContext.Collections.ToListAsync();
        Assert.That(collections.Count, Is.EqualTo(7)); // Check db also to be correct
    }

    [TearDown]
    public void TearDown()
    {
        dbContext?.Dispose();
    }
}
