using CrateApi.Data.Models;
using CrateApi.Data;
using CrateApi.Services.Database;
using CrateApi.Services.Runtimes;
using LanguageExt;
using Microsoft.EntityFrameworkCore;
using static LanguageExt.Prelude;
using CrateApi.Services.Test.Helpers;

namespace CrateApi.Services.Test.ContentService;

public class DeleteTests
{
    private CrateDbContext dbContext;
    
    private static List<Content> CreateContent(Guid userId, Guid otherUserId, DateTime createdDate, DateTime updatedDate) =>
        new List<Content>()
        {
            Items.CreateContent(1, userId, "Content1", "Detail1", "mediaUrl1", "url1", createdDate, updatedDate.AddDays(-1)),
            Items.CreateContent(2, userId, "Content2", "Detail2", "mediaUrl2", "url2", createdDate, updatedDate.AddDays(-2)),
            Items.CreateContent(3, otherUserId, "Content3", "Detail3", "mediaUrl3", "url3", createdDate.AddDays(-3), updatedDate.AddDays(-3)),
            Items.CreateContent(4, otherUserId, "Content4", "Detail4", "mediaUrl4", "url4", createdDate, updatedDate.AddDays(-4)),
            Items.CreateContent(5, otherUserId, "Content5", "Detail5", "mediaUrl5", "url5", createdDate, updatedDate),
            Items.CreateContent(6, otherUserId, "Content6", "Detail6", "mediaUrl6", "url6", createdDate, updatedDate.AddDays(-5)),
            Items.CreateContent(7, userId, "Content7", "Detail7", "mediaUrl7", "url7", createdDate, updatedDate.AddDays(-6)),
            Items.CreateContent(8, userId, "Content8", "Detail8", "mediaUrl8", "url8", createdDate, updatedDate.AddDays(-7)),
            Items.CreateContent(9, userId, "Content9", "Detail9", "mediaUrl9", "url9", createdDate, updatedDate.AddDays(-8))
        };

    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<CrateDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        dbContext = new CrateDbContext(options);
    }

    [Test]
    public async Task DeleteAllContent()
    {
        //setup
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var otherUserId = Guid.Parse("00000000-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var runtime = RuntimeHelper.CreateRuntime(dbContext, userManager);
        var createdDate = DateTime.UtcNow;
        var updatedDate = DateTime.UtcNow;
        var content = CreateContent(userId, otherUserId, createdDate, updatedDate); 
        await dbContext.Contents.AddRangeAsync(content);
        await dbContext.SaveChangesAsync();
        //end setup

        var r = await Database.ContentService<Eff<ApiRuntime>, ApiRuntime>.DeleteAll().RunAsync(runtime);
        Assert.IsTrue(r.IsSucc);

        var allRemainingContent = await dbContext.Contents.ToListAsync();

        Assert.IsNotNull(allRemainingContent);
        Assert.That(allRemainingContent.Count.Equals(4));
        Assert.That(allRemainingContent.Any(e => e.UserId == userId), Is.False);
    }

    [Test]
    public async Task DeleteOneContent()
    {
        //setup
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var otherUserId = Guid.Parse("00000000-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var runtime = RuntimeHelper.CreateRuntime(dbContext, userManager);
        var createdDate = DateTime.UtcNow;
        var updatedDate = DateTime.UtcNow;
        var content = CreateContent(userId, otherUserId, createdDate, updatedDate);
        await dbContext.Contents.AddRangeAsync(content);
        await dbContext.SaveChangesAsync();
        //end setup

        var r = await Database.ContentService<Eff<ApiRuntime>, ApiRuntime>.Delete(1).RunAsync(runtime);
        Assert.IsTrue(r.IsSucc);

        var allRemainingContent = await dbContext.Contents.ToListAsync();

        Assert.IsNotNull(allRemainingContent);
        Assert.That(allRemainingContent.Count.Equals(8));
    }

    [Test]
    public async Task CannotDeleteAnothersContent()
    {
        //setup
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var otherUserId = Guid.Parse("00000000-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var runtime = RuntimeHelper.CreateRuntime(dbContext, userManager);
        var createdDate = DateTime.UtcNow;
        var updatedDate = DateTime.UtcNow;
        var content = CreateContent(userId, otherUserId, createdDate, updatedDate);
        await dbContext.Contents.AddRangeAsync(content);
        await dbContext.SaveChangesAsync();
        //end setup

        var r = await Database.ContentService<Eff<ApiRuntime>, ApiRuntime>.Delete(3).RunAsync(runtime);
        Assert.IsTrue(r.IsFail);

        var allRemainingContent = await dbContext.Contents.ToListAsync();
        Assert.IsNotNull(allRemainingContent);
        Assert.That(allRemainingContent.Count.Equals(9));
    }

    [TearDown]
    public void TearDown()
    {
        dbContext?.Dispose();
    }
}
