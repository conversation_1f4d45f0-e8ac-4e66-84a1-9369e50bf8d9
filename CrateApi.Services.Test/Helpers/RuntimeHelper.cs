using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Common;
using CrateApi.Data;
using CrateApi.Services.Authentication;
using CrateApi.Services.Runtimes;
using Microsoft.Extensions.Logging;
using Moq;

namespace CrateApi.Services.Test.Helpers;

public static class RuntimeHelper
{
    public static ApiRuntime CreateRuntime(CrateDbContext dbContext, IUserManager userManager)
    {
        var logger2 = Mock.Of<ILogger<ApiRuntime>>(MockBehavior.Loose);
        var serviceProviderMock = new Mock<IServiceProvider>();
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(ILogger<ApiRuntime>)))
            .Returns(logger2);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(CrateDbContext)))
            .Returns(dbContext);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IUserManager)))
            .Returns(userManager);

        var serviceProvider = serviceProviderMock.Object;
        return ApiRuntime.Create(serviceProviderMock.Object);
    }

    public static ApiRuntime CreateUnfurlRuntime(CrateDbContext dbContext, IUserManager userManager, IHttpClientFactory factory, ServiceSettings settings)
    {
        var logger2 = Mock.Of<ILogger<ApiRuntime>>(MockBehavior.Loose);
        var serviceProviderMock = new Mock<IServiceProvider>();
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(ILogger<ApiRuntime>)))
            .Returns(logger2);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(CrateDbContext)))
            .Returns(dbContext);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IUserManager)))
            .Returns(userManager);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IHttpClientFactory)))
            .Returns(factory);
        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(ServiceSettings)))
            .Returns(settings);

        var serviceProvider = serviceProviderMock.Object;
        return ApiRuntime.Create(serviceProviderMock.Object);
    }
}
