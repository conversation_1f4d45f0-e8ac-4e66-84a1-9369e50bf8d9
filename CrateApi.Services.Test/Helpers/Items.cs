using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Data.Models;

namespace CrateApi.Services.Test.Helpers;
public static class Items
{
    public static Track CreateTrack(int? id, Guid userId, string trackTitle, string artist, string mediaUrl,
        string url, string domainUrl, DateTime created, DateTime updated) =>
        new Track
        {
            Id = id,
            UserId = userId,
            TrackTitle = trackTitle,
            ArtistName = artist,
            MediaUrl = mediaUrl,
            Url = url,
            Created = created,
            Updated = updated
        };
    public static Content CreateContent(int? id, Guid userId, string title, string detail, string mediaUrl, string url, DateTime created, DateTime updated) =>
        new Content
        {
            Id = id,
            UserId = userId,
            Title = title,
            Detail = detail,
            Url = url,
            MediaUrl = mediaUrl,
            Created = created,
            Updated = updated
        };
    public static Collection CreateCollection(string name, string thumbnail, Guid userId, DateTime created, DateTime updated) =>
        new Collection
        {
            Name = name,
            Thumbnail = thumbnail,
            UserId =  userId,
            Created = created,
            Updated = updated
        };
}
