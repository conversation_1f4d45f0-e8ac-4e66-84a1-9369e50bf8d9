using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Data.Models;
using CrateApi.Data;
using CrateApi.Services.Database;
using CrateApi.Services.Mappings;
using CrateApi.Services.Runtimes;
using LanguageExt;
using Microsoft.EntityFrameworkCore;
using Moq;
using static LanguageExt.Prelude;
using Microsoft.Extensions.Logging;
using CrateApi.Common.Extensions;
using Microsoft.Extensions.Options;
using CrateApi.Data.Models.Interfaces;
using CrateApi.Services.Test.Helpers;


namespace CrateApi.Services.Test.TrackService;
public class DeleteTests
{
    private CrateDbContext dbContext;
    private static List<Track> CreateTracks(Guid userId, Guid otherUserId, DateTime createdDate, DateTime updatedDate) =>
        new List<Track>()
        {
                Items.CreateTrack(1, userId, "Track1", "Artist1", "someImage.Png", "url", "domainUrl", createdDate, updatedDate.AddDays(-1)),
                Items.CreateTrack(2, userId, "Track2", "Artist2", "someImage.Png", "url", "domainUrl",  createdDate, updatedDate.AddDays(-2)),
                Items.CreateTrack(3, otherUserId, "Track3", "Artist3", "someImage.Png", "url", "domainUrl",  createdDate.AddDays(-3), updatedDate.AddDays(-3)),
                Items.CreateTrack(4, otherUserId, "Track4", "Artist4", "someImage.Png", "url", "domainUrl",  createdDate, updatedDate.AddDays(-4)),
                Items.CreateTrack(5, otherUserId, "Track5", "Artist5", "someImage.Png", "url", "domainUrl",  createdDate, updatedDate),
                Items.CreateTrack(6, otherUserId, "Track6", "Artist6", "someImage.Png", "url", "domainUrl", createdDate, updatedDate.AddDays(-5)),
                Items.CreateTrack(7, userId, "Track7", "Artist7", "someImage.Png", "url", "domainUrl", createdDate, updatedDate.AddDays(-6)),
                Items.CreateTrack(8, userId, "Track8", "Artist8", "someImage.Png", "url", "domainUrl", createdDate, updatedDate.AddDays(-7)),
                Items.CreateTrack(9, userId, "Track9", "Artist9", "someImage.Png", "url", "domainUrl", createdDate, updatedDate.AddDays(-8))
        };

    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<CrateDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        dbContext = new CrateDbContext(options);
    }
    [Test]
    public async Task DeleteAllTracks()
    {
        //setup
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var otherUserId = Guid.Parse("00000000-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var runtime = RuntimeHelper.CreateRuntime(dbContext, userManager);
        var createdDate = DateTime.Now;
        var updatedDate = DateTime.Now;
        var tracks = CreateTracks(userId, otherUserId, createdDate, updatedDate); 
        await dbContext.Tracks.AddRangeAsync(tracks);
        await dbContext.SaveChangesAsync();
        //end setup

        var r = await TrackService<Eff<ApiRuntime>, ApiRuntime>.DeleteAll().RunAsync(runtime);
        Assert.IsTrue(r.IsSucc);

        var allRemainingTracks = await dbContext.Tracks.ToListAsync();

        Assert.IsNotNull(allRemainingTracks);
        Assert.That(allRemainingTracks.Count.Equals(4));
        Assert.That(allRemainingTracks.Any(e => e.UserId == userId), Is.False);
    }

    [Test]
    public async Task DeleteOneTrack()
    {
        //setup
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var otherUserId = Guid.Parse("00000000-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var runtime = RuntimeHelper.CreateRuntime(dbContext, userManager);
        var createdDate = DateTime.Now;
        var updatedDate = DateTime.Now;
        var tracks = CreateTracks(userId, otherUserId, createdDate, updatedDate);
        await dbContext.Tracks.AddRangeAsync(tracks);
        await dbContext.SaveChangesAsync();
        //end setup

        var r = await TrackService<Eff<ApiRuntime>, ApiRuntime>.Delete(1).RunAsync(runtime);
        Assert.IsTrue(r.IsSucc);

        var allRemainingTracks = await dbContext.Tracks.ToListAsync();

        Assert.IsNotNull(allRemainingTracks);
        Assert.That(allRemainingTracks.Count.Equals(8));
    }

    [Test]
    public async Task CannotDeleteAnothersTrack()
    {
        //setup
        var userId = Guid.Parse("2dd0e5e8-1576-4a9c-ba11-c32882be3345");
        var otherUserId = Guid.Parse("00000000-1576-4a9c-ba11-c32882be3345");
        var userManager = UserManagerHelper.MockUserManager(userId).Object;
        var runtime = RuntimeHelper.CreateRuntime(dbContext, userManager);
        var createdDate = DateTime.Now;
        var updatedDate = DateTime.Now;
        var tracks = CreateTracks(userId, otherUserId, createdDate, updatedDate);
        await dbContext.Tracks.AddRangeAsync(tracks);
        await dbContext.SaveChangesAsync();
        //end setup

        var r = await TrackService<Eff<ApiRuntime>, ApiRuntime>.Delete(3).RunAsync(runtime);
        Assert.IsTrue(r.IsFail);

        var allRemainingTracks = await dbContext.Tracks.ToListAsync();
        Assert.IsNotNull(allRemainingTracks);
        Assert.That(allRemainingTracks.Count.Equals(9));
    }

    [TearDown]
    public void TearDown()
    {
        dbContext?.Dispose();
    }
}
