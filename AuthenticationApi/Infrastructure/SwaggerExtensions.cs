using System.Reflection;
using Microsoft.OpenApi.Models;

namespace AuthenticationApi.Infrastructure;

public static class SwaggerExtensions
{
    public static IServiceCollection ConfigureSwaggerExt(this IServiceCollection services)
    {
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "CrateApi", Version = "v1" });
            c.CustomSchemaIds(type => $"{type.ToString()}");
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            c.IncludeXmlComments(xmlPath);

            c.AddSecurityDefinition(
                "Bearer",
                new OpenApiSecurityScheme
                {
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.Http,
                    Description = "Please enter token: 'Bearer test-token'",
                    BearerFormat = "JWT",
                    Scheme = "bearer",
                }
            );

            c.AddSecurityRequirement(
                new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "Bearer" },
                        },
                        Array.Empty<string>()
                    }
                }
            );
        });
        return services;
    }
}
