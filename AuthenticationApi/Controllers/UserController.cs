using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using CrateApi.Data;
using CrateApi.Data.Models;
using AuthenticationApi.Helpers;
using AuthenticationApi.Infrastructure;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace AuthenticationApi.Controllers;

public record LoginRequest(string Email, string Password);

public record CreateUserRequest(string Email, string Password, string Username);

public record UserModel(string UserId, string Email, string Username, string Token);

[ApiController]
[Route("api/v1/[controller]")]
public class UserController(TokenGenerator generator, CrateDbContext context) : ControllerBase
{
    /// <summary>
    /// Log in by getting the JWT token
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("login")]
    [ProducesResponseType(typeof(UserModel), 200)]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        var user = await context.Users.FirstOrDefaultAsync(u => u.Email == request.Email && u.Password == request.Password);

        if (user == null)
        {
            return Unauthorized("Invalid credentials");
        }

        var token = generator.GenerateToken(user.Id!.ToString()!, user.Email);

        generator.DecodeToken(token);

        return Ok(new UserModel(user.Id!.ToString()!, user.Email, user.Username, token));
    }

    /// <summary>
    /// Register a new user
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("register")]
    [ProducesResponseType(typeof(User), 200)]
    public async Task<IActionResult> Register([FromBody] CreateUserRequest request)
    {
        var user = new User
        {
            Email = request.Email,
            Username = request.Username,
            Password = request.Password,
        };

        var dbUser = await context.Users.AddAsync(user);
        await context.SaveChangesAsync();
        return Ok(dbUser.Entity);
    }



    /// <summary>
    /// Refresh JWT
    /// </summary>
    /// <returns></returns>
    [Authorize]
    [HttpPost("token/refresh")]
    public IActionResult RefreshToken()
    {
        var email = User.FindFirst(ClaimTypes.Email)?.Value ?? "";
        if (email is null)
        {
            return Unauthorized();
        }
        var userId = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value ?? "";
        var token = generator.GenerateToken(userId, email);
        return Ok(new { token });
    }
}
